-- stream from producer topic
CREATE STREAM STOCK_DETAIL_TRANSAC_TIME (
    id INT,
    receipt_number INT,
    receipt_type STRING,
    transact_type STRING,
    transact_number INT,
    sku_id INT,
    price_type STRING,
    location_id INT,
    pallet_detail_id INT,
    batch_detail_id INT,
    carton_id INT,
    supplier_id INT,
    quantity double,
    original_quantity double,
    unit_price double,
    avg_price_rt double,
    status INT,
    grn_number STRING,
    remarks STRING,
    account_id INT,
    receipt_date STRING,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.STOCK_DETAIL',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM STOCK_DETAIL_TRANSAC AS
SELECT
    id,
    receipt_number,
    receipt_type,
    transact_type,
    transact_number,
    sku_id,
    price_type,
    location_id,
    pallet_detail_id,
    batch_detail_id,
    carton_id,
    supplier_id,
    quantity,
    original_quantity,
    unit_price,
    avg_price_rt,
    status,
    grn_number,
    remarks,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(receipt_date, 1, 10),
        ' ',
        SUBSTRING(receipt_date, 12, 8)
    ) AS receipt_date,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM 
    STOCK_DETAIL_TRANSAC_TIME;