-- stream from producer topic
CREATE STREAM ORDER_DETAIL_TIME (
    id INT,
    user INT,
    quantity double,
    original_quantity double,
    order_code STRING,
    status STRING,
    unit_price double,
    order_type STRING,
    order_reference STRING,
    account_id INT,
    sku_id INT,
    customer_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.ORDER_DETAIL',
    VALUE_FORMAT = 'JSON'
);

-- stream 2
CREATE STREAM ORDER_DETAIL AS
SELECT
    id,
    user,
    quantity,
    original_quantity,
    order_code,
    status,
    unit_price,
    order_type,
    order_reference,
    account_id,
    sku_id,
    customer_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    ORDER_DETAIL_TIME;