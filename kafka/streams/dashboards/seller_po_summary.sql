-- stream from producer topic
CREATE STREAM SELLER_PO_SUMMARY_TIME (
    id INT,
    grn_number STRING,
    putaway_quantity double,
    quantity double,
    cess_tax double,
    apmc_tax double,
    price double,
    purchase_order_id INT,
    sales_return_id INT,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.SELLER_PO_SUMMARY',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM SELLER_PO_SUMMARY AS
SELECT
    id,
    grn_number,
    putaway_quantity,
    quantity,
    cess_tax,
    apmc_tax,
    price,
    purchase_order_id,
    sales_return_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    SELLER_PO_SUMMARY_TIME;