-- stream from producer topic
CREATE STREAM INVOICE_DETAIL_TIME (
    id INT,
    warehouse_id INT,
    invoice_number STRING,
    invoice_reference STRING,
    challan_number STRING,
    invoice_date STRING,
    invoice_done_by STRING,
    total_invoice_value double,
    customerinfo_id INT,
    warehouseinfo_id INT,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING

) WITH (
    KAFKA_TOPIC = 'neo.public.INVOICE_DETAIL',
    VALUE_FORMAT = 'JSON'
);

-- stream 2
CREATE STREAM INVOICE_DETAIL AS
SELECT
    id,
    warehouse_id,
    invoice_number,
    invoice_reference,
    challan_number,
    invoice_done_by,
    total_invoice_value,
    customerinfo_id,
    warehouseinfo_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(invoice_date, 1, 10),
        ' ',
        SUBSTRING(invoice_date, 12, 8)
    ) AS invoice_date,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    INVOICE_DETAIL_TIME;