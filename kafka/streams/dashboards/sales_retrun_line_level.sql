-- stream from producer topic
CREATE STREAM SALES_RETURN_LINE_LEVEL_TIME (
    id INT,
    sku_id INT,
    sales_return_id INT,
    order_reference STRING,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.SALES_RETURN_LINE_LEVEL',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM SALES_RETURN_LINE_LEVEL AS
SELECT
    id,
    sku_id,
    sales_return_id,
    order_reference,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    SALES_RETURN_LINE_LEVEL_TIME;