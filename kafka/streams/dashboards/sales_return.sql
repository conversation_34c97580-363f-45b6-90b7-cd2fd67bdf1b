-- stream from producer topic
CREATE STREAM SALES_RETURN_TIME (
    id INT,
    warehouse_id INT,
    account_id INT,
    return_date STRING,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.SALES_RETURN',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM SALES_RETURN AS
SELECT
    id,
    warehouse_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(return_date, 1, 10),
        ' ',
        SUBSTRING(return_date, 12, 8)
    ) AS return_date,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    SALES_RETURN_TIME;