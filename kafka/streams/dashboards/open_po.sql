-- stream from producer topic
CREATE STREAM OPEN_PO_TIME (
    id INT,
    sku_id INT,
    cgst_tax double,
    igst_tax double,
    sgst_tax double,
    apmc_tax double,
    cess_tax double,
    supplier_id INT,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.OPEN_PO',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM OPEN_PO AS
SELECT
    id,
    sku_id,
    cgst_tax,
    igst_tax,
    sgst_tax,
    apmc_tax,
    cess_tax,
    supplier_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    OPEN_PO_TIME;