-- stream from producer topic
CREATE STREAM  CYCLE_COUNT_TIME (
    id INT,
    sku_id INT,
    quantity double,
    seen_quantity double,
    status STRING,
    remarks STRING,
    adjusted_value double,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.CYCLE_COUNT',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM CYCLE_COUNT AS
SELECT
    id,
    sku_id,
    quantity,
    seen_quantity,
    status,
    remarks,
    adjusted_value,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    CYCLE_COUNT_TIME;