-- stream from producer topic
CREATE STREAM RETURN_TO_VENDOR_TIME (
    id INT,
    rtv_number STRING,
    seller_po_summary_id INT,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.RETURN_TO_VENDOR',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM RETURN_TO_VENDOR AS
SELECT
    id,
    rtv_number,
    seller_po_summary_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    RETURN_TO_VENDOR_TIME;