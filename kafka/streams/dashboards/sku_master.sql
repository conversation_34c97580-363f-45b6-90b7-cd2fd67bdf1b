-- stream from producer topic
CREATE STREAM SKU_MASTER_TIME (
    id INT,
    user INT,
    sku_code STRING,
    cost_price double,
    average_price double,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.SKU_MASTER',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM SKU_MASTER AS
SELECT
    id,
    user,
    sku_code,
    cost_price,
    average_price,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    SKU_MASTER_TIME;