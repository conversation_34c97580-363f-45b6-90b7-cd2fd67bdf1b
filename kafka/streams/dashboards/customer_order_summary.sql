-- stream from producer topic
CREATE STREAM CUSTOMER_ORDER_SUMMARY_TIME (
    id INT,
    cgst_tax double,
    sgst_tax double,
    igst_tax double,
    utgst_tax double,
    cess_tax double,
    account_id INT,
    order_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.CUSTOMER_ORDER_SUMMARY',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM CUSTOMER_ORDER_SUMMARY AS
SELECT
    id,
    cgst_tax,
    sgst_tax,
    igst_tax,
    utgst_tax,
    cess_tax,
    account_id,
    order_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    CUSTOMER_ORDER_SUMMARY_TIME;