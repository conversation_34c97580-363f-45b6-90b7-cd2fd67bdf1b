-- stream from producer topic
CREATE STREAM SELLER_ORDER_SUMMARY_TIME (
    id INT,
    quantity double,
    full_invoice_number STRING,
    order_status_flag STRING,
    invoice_reference STRING,
    challan_number STRING,
    account_id INT,
    order_id INT,
    picklist_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.SELLER_ORDER_SUMMARY',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM SELLER_ORDER_SUMMARY AS
SELECT
    id,
    quantity,
    full_invoice_number,
    order_status_flag,
    invoice_reference,
    challan_number,
    account_id,
    order_id,
    picklist_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    SELLER_ORDER_SUMMARY_TIME;