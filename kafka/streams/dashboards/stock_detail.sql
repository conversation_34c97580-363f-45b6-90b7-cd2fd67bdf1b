-- stream from producer topic
CREATE STREAM STOCK_DETAIL_TIME (
    id INT,
    sku_id INT,
    quantity double,
    avg_price_rt double,
    location_id INT,
    account_id INT,
    batch_detail_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.STOCK_DETAIL',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM STOCK_DETAIL AS
SELECT
    id,
    sku_id,
    quantity,
    avg_price_rt,
    location_id,
    account_id,
    batch_detail_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM 
    STOCK_DETAIL_TIME;