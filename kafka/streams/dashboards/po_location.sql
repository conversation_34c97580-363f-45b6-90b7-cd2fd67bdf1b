-- stream from producer topic
CREATE STREAM PO_LOCATION_TIME (
    id INT,
    quantity double,
    status STRING,
    location_id INT,
    seller_po_summary_id INT,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.PO_LOCATION',
    VALUE_FORMAT = 'JSON'
);

-- stream 2
CREATE STREAM PO_LOCATION AS
SELECT
    id,
    quantity,
    status,
    location_id,
    seller_po_summary_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    PO_LOCATION_TIME;