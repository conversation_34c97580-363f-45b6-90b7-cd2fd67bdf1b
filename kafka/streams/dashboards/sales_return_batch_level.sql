-- stream from producer topic
CREATE STREAM SALES_RETURN_BATCH_LEVEL_TIME (
    id INT,
    original_return_quantity double,
    unit_price double,
    sales_return_sku_id INT,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.SALES_RETURN_BATCH_LEVEL',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM SALES_RETURN_BATCH_LEVEL AS
SELECT
    id,
    original_return_quantity,
    unit_price,
    sales_return_sku_id,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    SALES_RETURN_BATCH_LEVEL_TIME;