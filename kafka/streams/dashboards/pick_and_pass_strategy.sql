-- stream from producer topic
CREATE STREAM PICK_AND_PASS_STRATEGY_TIME (
    id INT,
    warehouse_id INT,
    employee_id INT,
    reference_number STRING,
    reference_type STRING,
    sub_zone_id INT,
    lpn_number STRING,
    lpn_status STRING,
    status STRING,
    account_id INT,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING

) WITH (
    KAFKA_TOPIC = 'neo.public.PICK_AND_PASS_STRATEGY',
    VALUE_FORMAT = 'JSON'
);

-- stream 2
CREATE STREAM PICK_AND_PASS_STRATEGY AS
SELECT
    id,
    warehouse_id,
    employee_id,
    reference_number,
    reference_type,
    sub_zone_id,
    lpn_number,
    lpn_status,
    status,
    account_id,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    PICK_AND_PASS_STRATEGY_TIME;