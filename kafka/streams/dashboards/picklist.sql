CREATE STREAM PICKLIST_TIME (
    id INT,
    account_id INT,
    stock_id INT,
    user_id INT,
    location_id INT,
    picklist_number INT,
    picked_quantity double,
    status STRING,
    creation_date STRING,
    updation_date STRING,
    __deleted STRING
) WITH (
    KAFKA_TOPIC = 'neo.public.PICKLIST',
    VALUE_FORMAT = 'JSON'
);

-- stream 2 <dont format this>
CREATE STREAM PICKLIST AS
SELECT
    id,
    account_id,
    stock_id,
    user_id,
    location_id,
    picklist_number,
    picked_quantity,
    status,
    CASE
        WHEN __deleted = 'true' THEN 1
        WHEN __deleted = 'false' THEN 0
        ELSE 1
    END AS __deleted,
    CONCAT(
        SUBSTRING(creation_date, 1, 10),
        ' ',
        SUBSTRING(creation_date, 12, 8)
    ) AS creation_date,
    CONCAT(
        SUBSTRING(updation_date, 1, 10),
        ' ',
        SUBSTRING(updation_date, 12, 8)
    ) AS updation_date
FROM
    PICKLIST_TIME;