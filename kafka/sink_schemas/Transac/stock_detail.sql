CREATE TABLE STOCK_DETAIL_TRANSAC (
    ID UInt64,
    RECEIPT_NUMBER UInt64,
    RECEIPT_TYPE Nullable(String),
    TRANSACT_TYPE Nullable(String),
    TRANSACT_NUMBER UInt64,
    SKU_ID UInt64,
    PRICE_TYPE Nullable(String),
    LOCATION_ID Nullable(UInt64),
    PALLET_DETAIL_ID Nullable(UInt64),
    BATCH_DETAIL_ID Nullable(UInt64),
    CARTON_ID Nullable(UInt64),
    SUPPLIER_ID Nullable(UInt64),
    QUANTITY Float64,
    <PERSON><PERSON><PERSON>AL_QUANTITY Float64,
    UNIT_PRICE Float64,
    AVG_PRICE_RT Float64,
    STATUS UInt64,
    GRN_NUMBER Nullable(String),
    <PERSON><PERSON><PERSON><PERSON> Nullable(String),
    ACCOUNT_ID UInt64,
    RECEIPT_DATE DateTime('UTC'),
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree
ORDER BY (ID, UPDATION_DATE);