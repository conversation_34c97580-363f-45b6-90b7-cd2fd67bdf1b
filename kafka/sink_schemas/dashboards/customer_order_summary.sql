CREATE TABLE CUSTOMER_ORDER_SUMMARY (
    ID UInt64,
    CGST_TAX Float64,
    SGST_TAX Float64,
    IGST_TAX Float64,
    UTGST_TAX Float64,
    CESS_TAX Float64,
    ACCOUNT_ID UInt64,
    ORDER_ID UInt64,
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';