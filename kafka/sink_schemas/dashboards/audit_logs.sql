CREATE TABLE audit_logs
(
    `id` UUID DEFAULT generateUUIDv4(),
    `request_time` DateTime('UTC'),
    `warehouse_id` UInt64,
    `warehouse` String,
    `username` String,
    `request_path` String,
    `request_method` String,
    `request` String,
    `response` String,
    `status_code` UInt16,
    `module_name` Nullable(String),
    `header_referer` Nullable(String),
    `request_id` Nullable(String)
)
ENGINE = MergeTree
PARTITION BY toMonth(request_time)
ORDER BY request_time