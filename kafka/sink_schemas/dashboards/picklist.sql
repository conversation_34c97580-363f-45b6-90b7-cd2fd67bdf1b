CREATE TABLE PICKLIST (
    ID UInt64,
    ACCOUNT_ID UInt64,
    STOCK_ID UInt64,
    USER_ID UInt64,
    LOCATION_ID UInt64,
    PICKLIST_NUMBER UInt64,
    PICKED_QUANTITY Float64,
    <PERSON>AT<PERSON> Nullable(String),
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';