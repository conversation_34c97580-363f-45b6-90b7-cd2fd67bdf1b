CREATE TABLE INVOICE_DETAIL (
    ID UInt64,
    WA<PERSON><PERSON><PERSON>USE_ID UInt64,
    INVOICE_NUMBER Nullable(String),
    INVOICE_REFERENCE Nullable(String),
    CHALLAN_NUMBER Nullable(String),
    INVOICE_DATE DateTime('UTC'),
    INVOICE_DONE_BY Nullable(String),
    TOTAL_INVOICE_VALUE Float64,
    CUSTOMERINFO_ID UInt64,
    WAREHOUSEINFO_ID UInt64,
    ACCOUNT_ID UInt64,
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';