CREATE TABLE CYCLE_COUNT (
    ID UInt64,
    SKU_ID UInt64,
    QUANTITY Float64,
    SEEN_QUANTITY Float64,
    STATUS String,
    REMAR<PERSON> Nullable(String),
    ADJUSTED_VALUE Float64,
    ACCOUNT_ID UInt64,
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';