CREATE TABLE SELLER_ORDER_SUMMARY (
    ID UInt64,
    QUANTITY Float64,
    FULL_INVOICE_NUMBER Nullable(String),
    ORDER_STATUS_FLAG Nullable(String),
    INVOICE_REFERENCE Nullable(String),
    CHALLAN_NUMBER Nullable(String),
    ACCOUNT_ID UInt64,
    ORDER_ID Nullable(UInt64),
    PICKLIST_ID Nullable(UInt64),
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';