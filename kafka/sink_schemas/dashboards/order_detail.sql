CREATE TABLE ORDER_DETAIL (
    ID UInt64,
    ORDER_CODE Nullable(String),
    ORDER_REFERENCE Nullable(String),
    ORDER_TYPE Nullable(String),
    QUANTITY Float64,
    <PERSON><PERSON><PERSON>AL_QUANTITY Float64,
    SKU_ID UInt64,
    STATUS Nullable(String),
    UNIT_PRICE Float64,
    USER UInt64,
    ACCOUNT_ID UInt64,
    CUSTOMER_ID UInt64,
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';