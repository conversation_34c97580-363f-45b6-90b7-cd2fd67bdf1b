CREATE TABLE PICK_AND_PASS_STRATEGY (
    ID UInt64,
    WAREHOUSE_ID UInt64,
    EMPLOYEE_ID UInt64,
    REFERENCE_NUMBER Nullable(String),
    REFERENCE_TYPE Nullable(String),
    SUB_ZONE_ID UInt64,
    LPN_NUMBER Nullable(String),
    LPN_STATUS Nullable(String),
    STATUS Nullable(String),
    ACCOUNT_ID UInt64,
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';