CREATE TABLE SELLER_PO_SUMMARY (
    ID UInt64,
    GRN_NUMBER Nullable(String),
    PUTAWAY_QUANTITY Float64,
    QUANTITY Float64,
    CESS_TAX Float64,
    APMC_TAX Float64,
    PRICE Float64,
    PURCHASE_ORDER_ID Nullable(UInt64),
    SALES_RETURN_ID Nullable(UInt64),
    ACCOUNT_ID UInt64,
    CREATION_DATE DateTime('UTC'),
    UPDATION_DATE DateTime('UTC'),
    __DELETED UInt8
) ENGINE = ReplacingMergeTree(ID, __DELETED) PRIMARY KEY (ID,)
ORDER BY
    ID SETTINGS clean_deleted_rows = 'Always';