# NEO Kubernetes Setup Guide

This guide provides step-by-step instructions to deploy and access the NEO application services on Kubernetes using Minikube.

## Prerequisites

Ensure you have the following installed:

* Minikube
* kubectl

## Step-by-Step Setup

### 1. Start Minikube

Start your Minikube cluster:

```shell
minikube start
```

### 2. Configure Docker Environment

Configure your shell to use Minikube's Docker daemon:

```shell
eval $(minikube docker-env)
```


### 3. Build Docker Image

Navigate to the `stockone-wms` directory and build the Docker image:

```shell
cd stockone-wms && docker build -t neo-api-kube:0 -f Dockerfile-prod .
```

### 3. Deploy Redis

Deploy the Redis service for NEO:

```shell
kubectl apply -f redis/neo-redis-configmap.yml
kubectl apply -f redis/neo-redis-deployment.yml
kubectl apply -f redis/neo-redis-service.yml
```

### 4. Deploy PostgreSQL Database

Deploy the PostgreSQL database service for NEO:

```shell
kubectl apply -f db/neo-db-configmap.yml
kubectl apply -f db/neo-db-secrets.yml
kubectl apply -f db/neo-db-deployment.yml
kubectl apply -f db/neo-db-service.yml
```

### 5. Deploy API Service

Deploy the API service for NEO:

```shell
kubectl apply -f api/neo-api-configmap.yml
kubectl apply -f api/neo-api-secrets.yml
kubectl apply -f api/neo-api-deployment.yml
kubectl apply -f api/neo-api-service.yml
```

### 6. Deploy Celery Worker

Deploy the Celery worker for background tasks in NEO:

```shell
kubectl apply -f celery-worker/neo-celery-deployment.yml
```

### 7. Deploy Celery Beat

Deploy the Celery beat scheduler for periodic tasks in NEO:

```shell
kubectl apply -f beat/neo-beat-deployment.yml
```

### 8. Deploy Flower Monitoring Tool

Deploy Flower for monitoring Celery tasks in NEO:

```shell
kubectl apply -f flower/neo-flower-deployment.yml
kubectl apply -f flower/neo-flower-service.yml
```

### 9. Port Forwarding to Access Services

Access your NEO services using port forwarding.

#### API Service

Forward the API service to your local machine:

```shell
kubectl port-forward service/api 8004:80
```

Access the API at `http://localhost:8004`.

#### Flower Service

Forward the Flower service to your local machine:

```shell
kubectl port-forward service/flower 8005:80
```

Access Flower at `http://localhost:8005`.

By following these steps, you will have all the NEO application services up and running on Kubernetes, and you will be able to access the API and Flower interfaces from your local machine using the specified ports.





## Helper: Generating Secrets in Base64

To securely store sensitive information like passwords, you need to encode these values in base64 and add them to your `api-secrets.yml` file.

### Steps to Generate Base64 Encoded Secrets

1. **Generate base64 encoded string for each secret:**
   Use the following command to encode your secrets:

   ```shell
   echo -n 'your_secret_value' | base64
   ```

    Replace`'your_secret_value'` with your actual secret value.


**2. Add the base64 encoded values to your `api-secrets.yml` file:**
