# Order Data Structure Documentation

This document provides a comprehensive explanation of the fields in the order data structure used in the StockOne Neo system.

## Top-Level Fields

| Field | Type | Description |
|-------|------|-------------|
| `data` | Object | The main container for order information |
| `warehouse` | String | The warehouse code where the order is being processed (e.g., "TEST_WH1") |

## Order Information (data object)

| Field | Type | Description |
|-------|------|-------------|
| `items` | Array | List of items/products in the order |
| `source` | String | Source of the order (e.g., "offline") |
| `status` | String | Numeric status code of the order |
| `slot_to` | String | End time of delivery/pickup slot (format: "YYYY-MM-DD HH:MM:SS") |
| `slot_from` | String | Start time of delivery/pickup slot (format: "YYYY-MM-DD HH:MM:SS") |
| `order_id` | String | Unique identifier for the order (e.g., "MN3406") |
| `hold_date` | String | Date until which the order is on hold (if applicable) |
| `order_date` | String | Date and time when the order was placed (format: "YYYY-MM-DD HH:MM:SS") |
| `order_type` | String | Type of order (e.g., "Amazon") |
| `request_id` | Number | Internal request identifier |
| `updated_by` | String | Username of the person who last updated the order |
| `customer_id` | Number | Unique identifier for the customer |
| `extra_fields` | Object | Additional custom fields for the order |
| `order_status` | String | Status of the order (e.g., "cancelled") |
| `shipment_date` | String | Date when the order is scheduled for shipment (format: "YYYY-MM-DD HH:MM:SS") |
| `invoice_amount` | Number | Total invoice amount for the order |
| `payment_status` | String | Status of payment (e.g., "Pending") |
| `expiration_date` | String | Date when the order expires (if applicable) |
| `order_reference` | String | Reference code for the order (often same as order_id) |
| `order_display_key` | String | Display key for the order (often same as order_id) |
| `customer_po_number` | String | Customer's purchase order number (if applicable) |
| `customer_reference` | String | Customer reference identifier |
| `order_creation_date` | String | Date and time when the order was created in the system (format: "YYYY-MM-DD HH:MM:SS") |
| `order_updation_date` | String | Date and time when the order was last updated (format: "YYYY-MM-DD HH:MM:SS") |

## Customer Information (customer object)

| Field | Type | Description |
|-------|------|-------------|
| `city` | String | Customer's city |
| `name` | String | Customer's name |
| `route` | Null/String | Delivery route assigned to the customer |
| `state` | String | Customer's state |
| `address` | String | Customer's address |
| `country` | String | Customer's country |
| `pincode` | String | Customer's postal/ZIP code |
| `email_id` | String | Customer's email address |
| `latitude` | String | Geographical latitude of customer's location |
| `longitude` | String | Geographical longitude of customer's location |
| `pan_number` | String | Customer's Permanent Account Number (PAN) |
| `tin_number` | String | Customer's Tax Identification Number (TIN) or GST number |
| `customer_id` | Number | Unique identifier for the customer |
| `phone_number` | String | Customer's phone number |
| `customer_code` | String | Customer's code in the system |
| `shipping_address` | String | Customer's shipping address |
| `customer_priority` | String | Priority level assigned to the customer |
| `customer_reference` | String | Customer reference identifier |
| `customer_shelf_life` | Number | Shelf life requirements for the customer (in days) |

## Order Items (items array)

Each item in the `items` array contains the following fields:

| Field | Type | Description |
|-------|------|-------------|
| `id` | Number | Unique identifier for the order item |
| `mrp` | Number | Maximum Retail Price of the item |
| `uom` | String | Unit of Measurement |
| `zone` | String | Storage zone in the warehouse |
| `batch` | Array | Batch information for the item |
| `taxes` | Object | Tax breakdown (CGST, IGST, SGST) |
| `status` | String | Status of the item (e.g., "cancelled") |
| `max_qty` | Number | Maximum quantity allowed |
| `remarks` | String | Additional notes about the item |
| `sku_code` | String | Stock Keeping Unit code |
| `sku_brand` | String | Brand of the product |
| `unit_price` | Number | Price per unit |
| `current_qty` | Number | Current quantity available |
| `stock_count` | Number | Count of stock available |
| `batch_number` | String | Batch number for the item |
| `created_from` | String | Source of item creation (e.g., "WEB") |
| `intransit_qty` | Number | Quantity in transit |
| `open_quantity` | Number | Quantity still open/pending |
| `cancelled_from` | String | Source of cancellation (e.g., "WEB") |
| `invoice_amount` | Number | Total invoice amount for this item |
| `line_reference` | String | Reference for the order line |
| `order_quantity` | Number | Original quantity ordered |
| `serial_numbers` | Array | Serial numbers for the item (if applicable) |
| `customer_gst_no` | String | Customer's GST number |
| `discount_amount` | Number | Discount amount applied to the item |
| `invoice_details` | Array | Details of invoices related to this item |
| `picked_quantity` | Number | Quantity that has been picked |
| `shipment_charge` | Number | Shipping charges for the item |
| `sku_description` | String | Description of the product |
| `picklist_details` | Array | Details of picklists for this item |
| `cancelled_by_user` | String | Username of the person who cancelled the item |
| `pack_uom_quantity` | String | Quantity in pack unit of measurement |
| `allocated_quantity` | Number | Quantity allocated for this order |
| `cancelled_quantity` | Number | Quantity that was cancelled |
| `dispatched_quantity` | Number | Quantity that has been dispatched |
| `unfulfilled_quantity` | Number | Quantity that remains unfulfilled |

## Payment Information (payment_info object)

| Field | Type | Description |
|-------|------|-------------|
| `paid_amount` | Number | Amount that has been paid |
| `payment_mode` | String | Mode of payment (e.g., "Cash Amount") |
| `terms_of_payment` | String | Terms and conditions for payment |

## Address Information

### Billing Address (billing_address object)

| Field | Type | Description |
|-------|------|-------------|
| `city` | String | City in the billing address |
| `name` | String | Name on the billing address |
| `email` | String | Email associated with the billing address |
| `state` | String | State in the billing address |
| `address` | String | Street address for billing |
| `country` | String | Country in the billing address |
| `pincode` | String | Postal/ZIP code for billing |
| `phone_number` | String | Phone number associated with the billing address |

### Shipping Address (shipping_address object)

| Field | Type | Description |
|-------|------|-------------|
| `address` | String | Complete shipping address |

## Extra Fields (extra_fields object)

| Field | Type | Description |
|-------|------|-------------|
| `order1` | String | Custom field 1 for order-specific information |
| `order2` | String | Custom field 2 for order-specific information |
| `order3` | String | Custom field 3 for order-specific information |
