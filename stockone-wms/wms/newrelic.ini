# ---------------------------------------------------------------------------

#
# This file configures the New Relic Python Agent.
#
# The path to the configuration file should be supplied to the function
# newrelic.agent.initialize() when the agent is being initialized.
#
# The configuration file follows a structure similar to what you would
# find for Microsoft Windows INI files. For further information on the
# configuration file format see the Python configparser documentation at:
#
#    https://docs.python.org/library/configparser.html
#
# For further discussion on the behaviour of the Python agent that can
# be configured via this configuration file see:
#
#    https://docs.newrelic.com/docs/apm/agents/python-agent/configuration/python-agent-configuration/
#

# ---------------------------------------------------------------------------

[newrelic]
log_file = stdout
log_level = warning
transaction_tracer.enabled = true
transaction_tracer.transaction_threshold = apdex_f
transaction_tracer.record_sql = obfuscated
transaction_tracer.stack_trace_threshold = 0.5
transaction_tracer.explain_enabled = true
transaction_tracer.explain_threshold = 0.5
error_collector.enabled = true
error_collector.ignore_classes =
error_collector.expected_classes =
browser_monitoring.auto_instrument = true
thread_profiler.enabled = true
distributed_tracing.enabled = true
