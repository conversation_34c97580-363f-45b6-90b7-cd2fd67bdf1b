# package imports
import json
import traceback
from json import loads

# django imports
from django.http import JsonResponse
from django.db.models import Q
from django.db import transaction
from django.core.cache import cache

# wms imports
from wms_base.wms_utils import init_logger

# core operations imports
from core_operations.views.common.main import (
    WMSListView, get_multiple_misc_values
)
from core_operations.views.services.packing_service import PackingService

# inventory imports
from inventory.models import StockDetail, LocationMaster

# outbound imports
from outbound.models import StagingInfo

log = init_logger('logs/location_lpn_mapping.log')


class LocationLPNMapping(WMSListView):
    """
    Class for handling LPN to Location mapping for pigeon hole sorting.

    This class provides APIs for:
    1. Location scan validation
    2. LPN scan validation
    3. LPN and location mapping

    The sorting method is determined by the 'pigeon_hole_sorting' configuration:
    - Location (default): Existing flow
    - LPN: LPN to location mapping
    """

    def get(self, *args, **kwargs):
        """
        GET method to handle location scan and lpn scan validations.

        Request type:
        - location_scan: Validates the location availability in staging info
        - lpn_scan: Validates the LPN usability
        """
        self.set_user_credientials()
        self.errors = []
        self.final_data = {}

        # Get the request type directly from request.GET
        request_type = self.request.GET.get('request_type')

        # Check if pigeon hole sorting is enabled and set to LPN
        self.fetch_sorting_configuration()

        if self.sorting_method.upper() != 'LPN':
            self.errors.append('Pigeon hole sorting is not configured for LPN level')
            return JsonResponse({'errors': self.errors}, status=400)

        if request_type == 'location_scan':
            self.validate_location_scan()
        elif request_type == 'lpn_scan':
            self.validate_lpn_scan()
        else:
            self.errors.append('Invalid request type')

        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)

        return JsonResponse(self.final_data, status=200)

    def post(self, *args, **kwargs):
        """
        POST method to handle LPN and location mapping.

        Maps LPN to location in staging info. If an existing LPN is provided,
        it will replace the LPN number of the current staging info record with the new LPN number.

        Uses transaction lock to ensure data consistency.
        Uses caching to reduce concurrent operations.
        """
        try:
            self.request_data = loads(self.request.body)
        except Exception as e:
            log.error(f"Invalid payload: {str(e)}")
            return JsonResponse({'message': 'Invalid Payload'}, status=400)

        self.set_user_credientials()
        self.errors = []

        try:
            self.fetch_sorting_configuration()

            if self.sorting_method.upper() != 'LPN':
                self.errors.append('Pigeon hole sorting is not configured for LPN level')
                return JsonResponse({'errors': self.errors}, status=400)

            location_obj, location, lpn_number, existing_lpn_number = self._validate_and_get_location()
            if self.errors:
                return JsonResponse({'errors': self.errors}, status=400)

            self.cache_key = f"warehouse_lpn_location_{self.warehouse.id}_{location}_{lpn_number}"
            cache_status = cache.add(self.cache_key, "True", 30)
            if not cache_status:
                log.info(f"Concurrent operation detected for {self.cache_key}")
                cache.delete(self.cache_key)  # Clean up the cache key
                return JsonResponse({'message': 'Another operation is in progress for this location and LPN',}, status=400)  # 400 Conflict

            try:
                with transaction.atomic():
                    if existing_lpn_number:
                        self._remap_existing_lpn(location_obj, location, lpn_number, existing_lpn_number)
                    else:
                        self._map_new_lpn(location_obj, location, lpn_number)

                    if self.errors:
                        return JsonResponse({'errors': self.errors}, status=400)

                return JsonResponse({'message': 'LPN mapped to location successfully'})
            finally:
                cache.delete(self.cache_key)
        except Exception as e:
            log.error(f"LPN mapping failed: {str(e)}")
            log.error(traceback.format_exc())
            return JsonResponse({'message': 'LPN mapping failed', 'error': str(e)}, status=500)

    def fetch_sorting_configuration(self):
        """
        Fetches the pigeon hole sorting configuration.

        Configuration options:
        - Location (default): Existing flow
        - LPN: LPN to location mapping
        """
        misc_types = ['pigeon_hole_sorting']
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        self.sorting_method = misc_dict.get('pigeon_hole_sorting', 'Location')

    def validate_location_scan(self):
        """
        Validates the location availability in staging info.

        Checks if the location is available in staging info with:
        - Status is completed, or
        - Status is pending and order_reference, picklist is null

        Returns:
        - Location details including location, zone, and carton information
        - If no available locations in staging info, still returns the location data
          with carton_no and status as None
        """
        location = self.request.GET.get('location')
        if not location:
            self.errors.append('Location is required')
            return

        # Check if location exists in LocationMaster
        location_obj = LocationMaster.objects.filter(
            zone__user=self.warehouse.id,
            location=location,
            zone__storage_type = 'sorting'
        ).select_related('zone').only('id', 'location', 'zone__zone')

        if not location_obj:
            self.errors.append(f'Location {location} not found')
            return

        # Get all staging info records for this location
        staging_info_records = StagingInfo.objects.filter(
            user=self.warehouse,
            location__location=location,
            segregation='outbound_staging',
            status=0
        )
        
        
        mapped_locations = list(staging_info_records.filter(status=0, carton_no__isnull=False).values('location_id', 'carton_no', 'order_reference', 'picklist_number'))
        location_mapped_cartons, order_references, picklist_numbers, lpn_numbers = {}, set(), set(), set()
        if mapped_locations:
            for record in mapped_locations:
                order_references.add(record.get('order_reference', ''))
                picklist_numbers.add(record.get('picklist_number', ''))
                if not order_references or not picklist_numbers:
                    self.errors.append(f'Order data is not available for location {location}')
                    return
                location_mapped_cartons[record.get('location_id')] = record.get('carton_no', '')
                lpn_numbers.add(record.get('lpn_number', ''))
            
            stock_filter = {
                'sku__user': self.warehouse.id,
                'location__location': location,
                'grn_number__in': order_references,
                'transact_number__in': picklist_numbers,
                'lpn_number__in': lpn_numbers,
                'quantity__gt': 0,
            }
            stock_details = StockDetail.objects.filter(**stock_filter)
            if stock_details.exists():
                self.errors.append(f'Location {location} is already mapped to an order and not available for mapping')
                return
        

        # Prepare the response with location, zone, and carton information
        location_data = []
        for location in location_obj:
            location_info = {
                'location': location.location,
                'zone': location.zone.zone,
                'carton_no': location_mapped_cartons.get(location.id, None),
            }
            location_data.append(location_info)
        self.final_data['location'] = location_data

    def validate_lpn_scan(self):
        """
        Validates the LPN usability.

        Checks:
        1. If LPN is available in staging info with pending status, then throw error
        2. Check in packing service for LPN usability using get_lpn_details function
        """
        lpn_number = self.request.GET.get('lpn_number')
        if not lpn_number:
            self.errors.append('LPN number is required')
            return

        # Check if LPN is already in staging info with pending status
        
        existing_stock = StockDetail.objects.filter(
            sku__user=self.warehouse.id,
            lpn_number=lpn_number,
            quantity__gt=0,
        )

        if existing_stock.exists():
            self.errors.append(f'LPN {lpn_number} is already mapped to location')
            return

        # Check in packing service for LPN usability using get_lpn_details function
        request_dict = {
            "request_headers": {
                "Warehouse": self.warehouse.username,
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }

        packing_service = PackingService(request_dict, self.user, self.warehouse)
        params = {
            "warehouse": self.warehouse.username,
            "lpn_number": lpn_number,
            "limit": 1,
        }

        lpn_details, packing_service_errors = packing_service.get_lpn_number_details(params)

        if packing_service_errors:
            self.errors.extend(packing_service_errors)
            return
        
        if not lpn_details:
            # If LPN not found in packing service, return error
            self.errors.append(f'invalid LPN {lpn_number}')
            return
        else:
            # If found in packing service, check if it's usable
            lpn_info = lpn_details[0]  # Get the first LPN record

            # Check if the LPN is usable (not dropped, not blocked, etc.)
            if lpn_info.get('usable', False) and not lpn_info.get('dropped', False) and not lpn_info.get('blocked', False):
                self.final_data['lpn_status'] = 'existing'
                self.final_data['lpn_details'] = {
                    'lpn_number': lpn_info.get('lpn_number'),
                    'carton_type': lpn_info.get('carton_type'),
                    'usable': lpn_info.get('usable'),
                    'status': lpn_info.get('status')
                }
            else:
                # If LPN exists but is not usable
                self.errors.append(f'LPN {lpn_number} exists but is not usable')
                return


    def _validate_and_get_location(self):
        """
        Validates required fields and retrieves the location object.

        Returns:
            tuple: (location_obj, location, lpn_number, existing_lpn_number)
        """
        location = self.request_data.get('location')
        lpn_number = self.request_data.get('lpn_number')
        existing_lpn_number = self.request_data.get('existing_lpn_number')

        if not location:
            self.errors.append('Location is required')
            return None, None, None, None

        if not lpn_number:
            self.errors.append('LPN number is required')
            return None, None, None, None

        # Get the location object
        location_obj = LocationMaster.objects.filter(
            zone__user=self.warehouse.id,
            location=location,
            zone__storage_type = 'sorting'
        ).first()

        if not location_obj:
            self.errors.append(f'Location {location} not found')
            return None, None, None, None

        return location_obj, location, lpn_number, existing_lpn_number

    def _remap_existing_lpn(self, location_obj, location, lpn_number, existing_lpn_number):
        """
        Remaps an existing LPN to a new LPN number.

        Args:
            location_obj: The location object
            location: The location string
            lpn_number: The new LPN number
            existing_lpn_number: The existing LPN number to be replaced
        """
        try:
            
            #update the blocked status of the existing LPN
            self.update_lpn_blocked_status(existing_lpn_number, False)
            
            #update the blocked status of the new LPN
            self.update_lpn_blocked_status(lpn_number, True)
            if self.errors:
                return
            
            # Get the existing staging info with select_for_update to lock the record
            existing_staging = StagingInfo.objects.select_for_update().filter(
                user=self.warehouse,
                carton_no=existing_lpn_number,
                location_id=location_obj.id,
                status=0,
                segregation='outbound_staging'
            ).first()

            if not existing_staging:
                self.errors.append(f'Existing mapping of LPN {existing_lpn_number} not found')
                return

            # Update the existing staging info with the new LPN number
            existing_staging.carton_no = lpn_number
            existing_staging.json_data = existing_staging.json_data or {}
            existing_staging.json_data['updated_by'] = self.user.username
            existing_staging.json_data['previous_lpn'] = existing_lpn_number
            existing_staging.save()

            log.info(f"Remapped LPN {existing_lpn_number} to {lpn_number} for location {location}")
        except Exception as e:
            log.error(f"Error remapping LPN {existing_lpn_number} to {lpn_number}: {str(e)}")
            self.errors.append(f"Failed to remap LPN: {str(e)}")
            raise
        
    def update_lpn_blocked_status(self, lpn_number, blocked_status):
        """
        Updates the blocked status of an LPN.

        Args:
            lpn_number: The LPN number to be updated
            status: The new blocked status (True/False)
        """
        params = {
            "warehouse": self.warehouse.username,
            "lpn_number": lpn_number,
             "status": True,
            "usable": True,
            "dropped": False,
            "blocked": blocked_status
        }
        request_dict = {
            "request_headers": {
                "Warehouse": self.warehouse.username,
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }
        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        _, packing_service_errors = packing_service_instance.update_lpn(params)
        if packing_service_errors:
            log.error(f"Failed to update LPN {lpn_number} blocked status: {packing_service_errors}")
            self.errors.extend(packing_service_errors)        
    
    def _map_new_lpn(self, location_obj, location, lpn_number):
        """
        Maps a new LPN to a location.

        Args:
            location_obj: The location object
            location: The location string
            lpn_number: The LPN number to be mapped
        """
        try:
            self.update_lpn_blocked_status(lpn_number, True)
            if self.errors:
                return
            self._create_new_staging(location_obj, lpn_number, location)
            log.info(f"Mapped LPN {lpn_number} to location {location}")
        except Exception as e:
            log.error(f"Error mapping LPN {lpn_number} to location {location}: {str(e)}")
            self.errors.append(f"Failed to map LPN: {str(e)}")
            raise

    def _create_new_staging(self, location_obj, lpn_number, location):
        """
        Creates a new staging info record.

        Args:
            location_obj: The location object
            lpn_number: The LPN number
        """
        
        # Create new staging info record
        json_data = {'created_by': self.user.username}
        staging_filters = {
            'user_id': self.warehouse.id,
            'location_id': location_obj.id,
            'segregation': 'outbound_staging',
            'status': 0,
        }
        staging_records = StagingInfo.objects.filter(**staging_filters).exclude(status=0, carton_no__isnull=False)


        if staging_records.exists():
            # update the staging records with lpn number
            staging_records.update(carton_no=lpn_number)
        else:
            StagingInfo.objects.create(
                user=self.warehouse,
                status=0,  # Pending
                location=location_obj,
                segregation='outbound_staging',
                carton_no=lpn_number,
                json_data=json_data,
                account_id=self.warehouse.userprofile.id
            )
