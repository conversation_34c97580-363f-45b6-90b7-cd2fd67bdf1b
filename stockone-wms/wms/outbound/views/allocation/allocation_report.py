#package imports
import pandas as pd
from django.utils import timezone
from dateutil import parser
from django.http import JsonResponse
from django.db.models import Sum, F
from copy import deepcopy
from collections import defaultdict
from datetime import timedelta, datetime
import traceback

#outbound imports
from outbound.models.allocation import StockAllocation
from outbound.models.orders import OrderDetail

#core imports
from core.models import WaveCriteria
from core_operations.views.common.main import WMSListView, get_local_date_known_timezone

#reports imports
from reports.views import CustomEndpointReportMixin

#inventory imports
from inventory.models import ClosingStock

#wms base imports
from wms_base.wms_utils import init_logger
DEFAULT_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
DEFAULT_DATE_FORMAT = "%Y-%m-%d"

log = init_logger('logs/allocation_report.log')

class AllocationReport(WMSListView, CustomEndpointReportMixin):
    """
    This class is used to fetch allocation report data.
    """

    def get(self, *args, **kwargs):
        """
        Handles GET requests to generate the allocation report.

        Returns:
            JsonResponse: A JSON response indicating the status of the report generation.
        """
        self.set_user_credientials()
        request_data = self.request.GET.dict()
        self.time_zone = self.request.timezone

        try:
            error_message = self.validate_and_process_report(request_data)
            if error_message:
                return JsonResponse({"message": error_message}, status=400)

            self.prepare_filters(request_data)
            self.get_allocation_report_data()
        except Exception as e:
            log.error(e)
            log.debug(traceback.format_exc())
            self.save_file_path('', 'Failed', self.generated_report)
            return JsonResponse({"status": "Report Generation Failed", "error": str(e)}, status=400)

        return JsonResponse({"status": "Success"}, status=200)

    def prepare_filters(self, request_data):
        """
        Prepares the filters for fetching allocation data.

        Args:
            request_data (dict): The request parameters.

        Returns:
            None
        """
        wave_data = list(WaveCriteria.objects.filter(warehouse_id=self.warehouse.id, status=1).values('warehouse_id', 'order_creation_days', 'full_open_order', 'order_type'))
        order_days, self.full_open_order = 0, 0
        self.order_filters = { 'user' : self.warehouse.id}
        self.allocation_filters = {
            'status__in': [1, 2],
            'warehouse_id': self.warehouse.id,
            'reference_model': 'OrderDetail',
        }
        if wave_data:
            order_days = wave_data[0]['order_creation_days'] or 0
            self.full_open_order = wave_data[0]['full_open_order'] or 0
            order_types = []
            for wave in wave_data:
                order_types.extend(wave['order_type'].split(','))
            if order_types:
                self.order_filters['order_type__in'] = order_types
            else:
                return
        self.request_date = request_data.get('request_date') or datetime.now().date()
        if request_data.get('allocation_date'):
            self.allocation_date = timezone.make_aware(parser.parse(request_data['allocation_date']), timezone.get_current_timezone())
            if order_days:
                self.order_filters['creation_date__gte'] = self.allocation_date - timedelta(days=order_days)
            self.allocation_filters['creation_date__gte'] = self.allocation_date
            self.allocation_filters['creation_date__lt'] = self.allocation_date + timedelta(days=1)
        
        filters = {
            'order_reference': 'order_reference',
            'sku_code': 'sku__sku_code',
            'order_type': 'order_type',
            'customer_reference': 'customer_identifier__customer_reference',
        }
        for req_filter, filter_key in filters.items():
            if request_data.get(req_filter):
                self.order_filters[filter_key] = request_data[req_filter]

    def get_allocation_report_data(self):
        """
        Fetches and prepares the allocation report data.

        Args:
            request_data (dict): The request parameters.

        Returns:
            None
        """

        self.report_df = pd.DataFrame(columns=[
            'Warehouse', 'Allocation Type', 'Customer Name', 'Customer Reference', 'Route Name', 'Order Type',
            'Customer Type', 'Customer PO Number', 'Allocation Number', 'Wave Number', 'Order Reference',
            'Allocation date & Time', 'Order Creation date & Time', 'Allocated By', 'Order Line Reference',
            'SKU Code', 'SKU Description', 'SKU Category', 'SKU Sub Category', 'SKU Brand', 'MRP', 'Unit Price',
            'Batch Number', 'MFG Date', 'Expiry Date', 'Order Quantity', 'Allocated Quantity', 'Open Order Qty',
            'Cancelled Qty', 'Zone', 'Location', 'Sub Zone', 'Sub Zone Seq', 'Order Status', 'Warehosue Opening Stock',
            'Customer Current Stock', 'Customer Intransit Stock', 'Customer Target Stock', 'Final Coverage'
        ])

        self.fetch_initial_data()
        self.prepare_final_data()
        self.post_report_generation(self.report_df)

    def fetch_initial_data(self):
        """
        Fetches the Required Data for the report.
        1. Allocation Data
        2. Order Data
        """
        
        self.allocation_data = list(StockAllocation.objects.filter(**self.allocation_filters).values(
            'warehouse__username', 'json_data__allocation_type', 'stock__batch_detail__mrp', 
            'stock__unit_price', 'stock__batch_detail__batch_no', 'stock__batch_detail__manufactured_date',
            'stock__batch_detail__expiry_date', 'json_data__zone', 'stock__location__sub_zone__zone',
            'stock__location__location', 'status', 'creation_date', 'json_data__created_by', 
            'stock__location__sub_zone__get_sequence', 'allocation_id', 'reference_id', 'json_data__wave_reference',
            'created_by__username', 'reference_number'
        ).annotate(allocated_quantity=Sum('quantity')))
        
        order_references = set(allocation['reference_number'] for allocation in self.allocation_data)
        orders = OrderDetail.objects.filter(**self.order_filters)
        if self.full_open_order:
            order_refs = set(orders.values('order_reference').\
                annotate(total_qty = Sum('original_quantity'), open_cancelled_qty=Sum('quantity') + Sum('cancelled_quantity')).\
                filter(total_qty=F('open_cancelled_qty')).values_list('order_reference', flat=True))
            order_references = order_references.union(order_refs)
        
        self.order_data = list(orders.filter(order_reference__in=order_references).values(
            'customer_identifier__name', 'customer_identifier__customer_reference', 'customer_identifier__customer_type',
            'customer_po_num', 'customer_identifier__route__name', 'order_type', 'id', 'creation_date', 'line_reference',
            'original_quantity', 'quantity', 'cancelled_quantity', 'sku__sku_code', 'sku__sku_desc', 'sku__sku_brand',
            'sku__sku_category', 'sku__sub_category', 'order_reference',  'json_data', 'status', 'sku_id',
        ).order_by('id'))
        
        self.allocation_dict = defaultdict(list)
        for order in self.order_data:
            order['creation_date'] = get_local_date_known_timezone(self.time_zone, order['creation_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT) if order['creation_date'] else ""
            json_data = order['json_data'] or {}
            order['final_coverage'] = json_data.get('final_coverage', 0)
            order['current_stock'] = int(json_data.get('current_stock') or 0)
            order['intransit_stock'] = int(json_data.get('intransit_stock') or 0)
            order['target_stock'] = int(json_data.get('target_stock') or 0)
            if order['status'] == '3':
                order['status'] = 'Cancelled'
            elif order['status'] == '5':
                order['status'] = 'Completed'
            elif order['status'] == '20':
                order['status'] = 'Allocated'
            elif order['status'] == '1' and order['quantity'] + order['cancelled_quantity'] == order['original_quantity']:
                order['status'] = 'Open'
            else:
                order['status'] = 'In Progress'
        
        for allocation in self.allocation_data:
            allocation['allocation_date'] = get_local_date_known_timezone(self.time_zone, allocation['creation_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT) if allocation['creation_date'] else ""
            allocation['mfg_date'] = get_local_date_known_timezone(self.time_zone, allocation['stock__batch_detail__manufactured_date'], send_date=True).strftime(DEFAULT_DATE_FORMAT) if allocation['stock__batch_detail__manufactured_date'] else ""
            allocation['expiry_date'] = get_local_date_known_timezone(self.time_zone, allocation['stock__batch_detail__expiry_date'], send_date=True).strftime(DEFAULT_DATE_FORMAT) if allocation['stock__batch_detail__expiry_date'] else ""
            allocation['json_data__created_by'] = allocation.get('json_data__created_by')
            allocation['allocated_quantity'] = int(allocation.get('allocated_quantity') or 0)
            self.allocation_dict[allocation['reference_id']].append(allocation)
        
        #Fetch Closing Stock Data
        self.closing_stock = dict(ClosingStock.objects.filter(closing_date=self.request_date-timedelta(days=1)).values_list('stock__sku_id').annotate(Sum('quantity')))

    def prepare_final_data(self):
        """
        Formats the data in the required format.
        """
        final_data = []

        for order_data in self.order_data:
            allocation_data_list = self.allocation_dict.get(str(order_data['id']), [{}])
            for allocation_data in allocation_data_list:
                if order_data['status'] == 'Open' and allocation_data:
                    order_data['status'] = 'Partially Allocated'
                # Create dictionary with data following the column order
                final_data_dict = {
                    'Warehouse': self.warehouse.username,
                    'Allocation Type': allocation_data.get('json_data__allocation_type'),
                    'Customer Name': order_data['customer_identifier__name'],
                    'Customer Reference': order_data['customer_identifier__customer_reference'],
                    'Route Name': order_data['customer_identifier__route__name'],
                    'Order Type': order_data['order_type'],
                    'Customer Type': order_data['customer_identifier__customer_type'],
                    'Customer PO Number': order_data['customer_po_num'],
                    'Allocation Number': allocation_data.get('allocation_id'),
                    'Wave Number': allocation_data.get('json_data__wave_reference'),
                    'Order Reference': order_data['order_reference'],
                    'Allocation date & Time': allocation_data.get('allocation_date'),
                    'Order Creation date & Time': order_data['creation_date'],
                    'Allocated By': allocation_data.get('created_by__username'),
                    'Order Line Reference': order_data['line_reference'],
                    'SKU Code': order_data['sku__sku_code'],
                    'SKU Description': order_data['sku__sku_desc'],
                    'SKU Category': order_data['sku__sku_category'],
                    'SKU Sub Category': order_data['sku__sub_category'],
                    'SKU Brand': order_data['sku__sku_brand'],
                    'MRP': allocation_data.get('stock__batch_detail__mrp', 0),
                    'Unit Price': allocation_data.get('stock__unit_price', 0),
                    'Batch Number': allocation_data.get('stock__batch_detail__batch_no'),
                    'MFG Date': allocation_data.get('mfg_date'),
                    'Expiry Date': allocation_data.get('expiry_date'),
                    'Order Quantity': order_data['original_quantity'],
                    'Allocated Quantity': allocation_data.get('allocated_quantity'),
                    'Open Order Qty': order_data['quantity'],
                    'Cancelled Qty': order_data['cancelled_quantity'],
                    'Zone': allocation_data.get('json_data__zone'),
                    'Location': allocation_data.get('stock__location__location'),
                    'Sub Zone': allocation_data.get('stock__location__sub_zone__zone'),
                    'Sub Zone Seq': allocation_data.get('stock__location__sub_zone__get_sequence'),
                    'Order Status' : order_data['status'],
                    'Warehosue Opening Stock': self.closing_stock.get(order_data['sku_id'], 0),
                    'Customer Current Stock' : order_data['current_stock'],
                    'Customer Intransit Stock' : order_data['intransit_stock'],
                    'Customer Target Stock' : order_data['target_stock'],
                    'Final Coverage' : round(order_data['final_coverage'], 2)
                }
                
                final_data.append(final_data_dict)
        
        # Create DataFrame from the final data list
        if final_data:
            self.report_df = pd.DataFrame.from_records(final_data)
            self.report_df = self.report_df.sort_values(by=['Warehouse', 'Order Creation date & Time', 'Allocation Number'])
