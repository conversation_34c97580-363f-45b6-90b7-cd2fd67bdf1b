#package imports
import pytz
import pandas as pd
from datetime import datetime, timedelta

#django imports
from django.http import JsonResponse
from django.db.models import F, Case, When, Value, CharField

#wms celery imports
from wms.celery import app as celery_app

#wms base imports
from wms_base.wms_utils import init_logger
from wms_base.models import UserProfile, UserGroups

#wms setting imports
from wms.settings.base import reports_database

#core operations imports
from core_operations.views.common.main import (
    get_multiple_misc_values, get_utc_time_from_user_time_input, get_financial_year
)

#outbound imports
from outbound.models import (
    Picklist, InvoiceItems, InvoicedLpns, Order, OrderShipmentItems,
    ManifestDetail
)

#inventory imports
from inventory.models import SerialNumberTransaction

#core imports
from core.models import GatePassItem

#core operation imports
from core_operations.views.common.main import get_multiple_misc_values

#report helpers imports
from .helpers import ReportHelpers

log = init_logger('logs/dispatch_summary.log')

DISPATCH_REPORT_COLUMNS = [
    'Financial_Year', 'Warehouse_Zone', 'Warehouse', 'Order_Type', 'Customer_ID',
    'Customer_Reference', 'Customer_Name', 'Customer_City', 'Customer_State', 'Customer_GSTIN_Number',
    'Customer_Billing_Address', 'Customer_Shipping_Address', 'Customer_Email',
    'Customer_Phone', 'Order_Reference', 'Order_Date', 'Order_Status',
    'Order_Expiry_Date', 'Order_Created_By',
    'Picked_BIN_Location', 'BIN_Location_Zone', 'Picked_Date', 'Picklist_ID',
    'Invoice_LPN_No', 'Invoice_Number', 'Invoice_Date', 'Challan_Number',
    'Invoice_By', 'Vehicle_Number', 'Transporter_Name', 'SKU_Code',
    'SKU_Description', 'Batch_Number', 'UOM', 'Meter_Serial_Number',
    'HSN_Code', 'Order_Qty', 'Picked_Qty', 'Packed_Qty', 'Invoice_Quantity',
    'Order_SKU_Status', 'Unit_Price', 'MRP_Rate',
    'Total_Invoice_Value', 'Gate_Pass_ID', 'Gate_Pass_Date',
    'Gate_Pass_Entry_User', 'Gate_Pass_In_Time', 'Gate_Pass_Status',
    'Gate_Pass_Type', 'Gate_Pass_Sub_Type'
]
INVOICE_ITEMS_FIELDS = {
    "Financial_Year": F("invoice__invoice_date"), "Warehouse": F("warehouse__username"), "Order_Type": F("order_type"), "Customer_ID": F("invoice__customerinfo__customer_id"), "Customer_Reference": F("invoice__customerinfo__customer_reference"), "Customer_Name": F("invoice__customerinfo__name"), "Customer_City": F("invoice__customerinfo__city"), "Customer_State": F("invoice__customerinfo__state"), "Customer_GSTIN_Number": F("invoice__customerinfo__gstin_number"),
    "Customer_Billing_Address": F("invoice__customerinfo__billing_address"), "Customer_Shipping_Address": F("invoice__customerinfo__shipping_address"), "Customer_Email": F("invoice__customerinfo__email_id"), "Customer_Phone": F("invoice__customerinfo__phone_number"), "Order_Reference": F("order_reference"), "Order_Date": F("order_date"), "Total_Invoice_Value": F("invoice__total_invoice_value"),
    "Invoice_Number": F("invoice__invoice_reference"), "Invoice_Date": F("invoice__invoice_date"), "Challan_Number": F("invoice__challan_number"), "Invoice_By": F("invoice__invoice_done_by"), "SKU_Code": F("sku_code"), "SKU_Description": F("sku_desc"), "Batch_Number": F("batch_number"), "Order_Qty": F("order_quantity"), "Invoice_Quantity": F("invoice_quantity"), "Unit_Price": F("unit_price"), "MRP_Rate": F("mrp"),
    "invoice_items_id": F("id"), "invoice_detail_id": F("invoice__id"), "Warehouse_id": F("warehouse_id"), "Line_Reference": F("json_data__order_json__line_reference"), "Picklist_Number": F('picklist_number'), "Invoice_Reference": F("invoice__invoice_reference")
}
INVOICED_LPNS_FIELDS = {
    "invoice_items_id": F("invoice_item_id"), "Invoice_LPN_No": F("lpn_number"), "Packed_Qty": F("packed_quantity"), "Warehouse_id": F("invoice_item__warehouse_id")
}
ORDER_HEADER_FIELDS = {
    "Order_Reference": F("order_reference"),  "Order_Expiry_Date": F("expiration_date"), "Order_Created_By": F("created_by"), "Warehouse_id": F("warehouse_id")
}
PICKLIST_DETAILS_FIELDS = {
    "Picklist_Number": F("picklist_number"), "Picked_BIN_Location": F("location__location"), "BIN_Location_Zone": F("location__zone__zone"), "Picked_Date": F("json_data__confirmation_time"), "Picklist_ID": F("id"), "HSN_Code": F("sku__hsn_code"), "Picked_Qty": F("picked_quantity"),
    "Batch_Number": F("stock__batch_detail__batch_no"), "SKU_Code": F("sku__sku_code"), "Order_Reference": F("order__order_reference"), "Warehouse_id": F("order__user"), "Line_Reference": F("order__line_reference")
}
ORDER_SHIPMENT_FIELDS = {
    "Vehicle_Number": F("order_shipment__vehicle_number"), "Transporter_Name": F("order_shipment__transporter_name"), "Invoice_Reference": F("shipment_invoice__reference_number"), "Order_shipment_id": F("order_shipment_id"), "Warehouse_id": F("order_shipment__user_id")  
}
GATE_PASS_FIELDS = {
    "Gate_Pass_ID": F("gate_pass__gate_pass_id"), "Gate_Pass_Date": F("gate_pass__creation_date"), "Gate_Pass_Entry_User": F("warehouse__username"), "Gate_Pass_In_Time": F("gate_pass__json_data__approved_gate_in_on"), "Gate_Pass_Type": F("gate_pass__gate_pass_type"), "Gate_Pass_Sub_Type": F("gate_pass__gate_pass_sub_type"),
    "Warehouse_id": F("gate_pass__warehouse_id"), "Manifest_Number": F("transaction_id")
}
SERIAL_NUMBER_TRANSACTION_FIELDS = {
    "Meter_Serial_Number": F("serial_number"), "Invoice_Reference": F("reference_number"), "SKU_Code": F("sku_code"), "Batch_Number": F("batch_number"), "LPN_Number": F("lpn_number"),
}


@celery_app.task()
def generate_dispatch_summary_report_async(user_id, warehouse_id, request_data):
    """
    Asynchronously generates a dispatch summary report.
    
    Args:
        user_id (int): The ID of the user requesting the report
        warehouse_id (int): The ID of the warehouse for which the report is generated
        request_data (dict): The data associated with the request
    """
    report_object = DispatchSummaryReportView()
    report_data = report_object.get_report_details(user_id, warehouse_id, request_data)
    report_object.generate_report(report_data=report_data, func=report_object.generate_dispatch_summary_report)

class DispatchSummaryReportView(ReportHelpers):
    """
    View for generating dispatch summary reports.

    This class provides methods for validating, processing, and generating
    dispatch summary reports based on invoice data.
    """

    def get(self, request, *args, **kwargs):
        """
        Handles GET requests for generating dispatch summary reports.
        
        Args:
            request (HttpRequest): The HTTP request object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        
        Returns:
            JsonResponse: A JSON response indicating the status of the report generation
        """
        self.set_user_credientials()
        self.request_data = self.request.GET

        try:
            error_message = self.validate_report_details()
            if error_message:
                return JsonResponse({"message": error_message}, status=400)

            # update to celery async task
            generate_dispatch_summary_report_async.apply_async(args=[self.user.id, self.warehouse.id, self.request_data])
            return JsonResponse({"status": "Success"}, status=200)

        except Exception as e:
            log.info(e)
            self.save_file_path('', 'Failed', self.generated_report)
            return JsonResponse({"status": "Report Generation Failed"}, status=400)

    def get_misc_values(self):
        """
        Fetches miscellaneous configuration values for the warehouse.
        
        Sets:
            self.days_of_report_data (int): Number of days to include in the report
        """
        
        misc_types = ['days_of_report_data']
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        try:
            self.days_of_report_data = int(misc_dict.get('days_of_report_data', 1))
        except:
            self.days_of_report_data = 1
            
    def get_from_date_filter(self, value):
        """
        Sets date filters for the report based on user input or defaults.
        
        Args:
            value (str or None): The date string input by the user. If provided, it is used as
                                the start date for the filter. If not provided, defaults to last
                                'days_of_report_data' days from current UTC time
        
        Modifies:
            self.search_params (dict): Updates with the appropriate date range filters
        """
        
        if value:
            from_date_value = get_utc_time_from_user_time_input(value)
            to_date = from_date_value + timedelta(days=self.days_of_report_data)
            self.search_params['creation_date__gte'] = from_date_value
            self.search_params['creation_date__lt'] = to_date
        else:
            now = datetime.utcnow().replace(tzinfo=pytz.UTC)
            from_date = now - timedelta(days=self.days_of_report_data)
            self.search_params['creation_date__gte'] = from_date
            
    def prepare_report_filters(self):
        """
        Prepares and applies filters for the report based on request data.
        
        Modifies:
            self.search_params (dict): Updates with filter criteria based on request data
        """
        
        filter_columsn = {
            'From Date': 'creation_date__gte',
            'Order Reference': 'order_reference',
            'Invoice Number': 'invoice__invoice_reference',
        }
        self.get_misc_values()
        for key, value in filter_columsn.items():
            if key == 'From Date':
                self.get_from_date_filter(self.request_data.get(key))
            elif self.request_data.get(key):
                self.search_params[value] = self.request_data.get(key)
                
    def get_invoice_items_details(self):
        """
        Fetches invoice item details based on search parameters and prepares a DataFrame.
        
        Modifies:
            self.invoice_items_df (pd.DataFrame): DataFrame of invoice items
            self.errors (dict): Error messages if no data found
            self.invoice_items_ids (list): List of invoice item IDs
            self.order_references (list): List of unique order references
            self.picklist_numbers (list): List of unique picklist numbers
            self.invoice_references (list): List of unique invoice references
        """

        
        invoice_items_details = list(InvoiceItems.objects.using(reports_database).filter(**self.search_params).values(**INVOICE_ITEMS_FIELDS))
        self.invoice_items_df = pd.DataFrame(invoice_items_details)
        if self.invoice_items_df.empty:
            self.errors['invoice_items'] = "No invoice items found"
        else:
            self.invoice_items_ids = self.invoice_items_df['invoice_items_id'].tolist()
            self.order_references = self.invoice_items_df['Order_Reference'].dropna().unique().tolist()
            self.picklist_numbers = self.invoice_items_df['Picklist_Number'].dropna().unique().tolist()
            self.invoice_references = self.invoice_items_df['Invoice_Reference'].dropna().unique().tolist()
            self.invoice_items_df['Warehouse_Zone'] = self.invoice_items_df['Warehouse_id'].map(self.warehouse_zone_details).fillna('')
        
    def get_invoiced_lpns_details(self, warehouse_id):
        """
        Fetches invoiced LPNs details and merges them with invoice items.
        
        Args:
            warehouse_id (int): Warehouse ID to filter the data
        
        Modifies:
            self.invoice_items_df (pd.DataFrame): Updated DataFrame with LPN details
        """
        
        if self.invoice_items_ids:
            invoiced_lpns_df = pd.DataFrame(columns=list(INVOICED_LPNS_FIELDS.keys()))
            invoiced_lpns_details = list(InvoicedLpns.objects.using(reports_database).filter(invoice_item_id__in=self.invoice_items_ids, invoice_item__warehouse_id=warehouse_id).values(**INVOICED_LPNS_FIELDS))
            invoiced_lpns_df = pd.DataFrame(invoiced_lpns_details)
            if not invoiced_lpns_df.empty:
                # Merge invoiced_lpns_df with invoice_items_df on 'warehouse_id' and 'invoice_items_id'
                self.invoice_items_df = pd.merge(
                    self.invoice_items_df,
                    invoiced_lpns_df,
                    how='left',
                    on=['Warehouse_id', 'invoice_items_id']
                )
            else:
                # Ensure all INVOICED_LPNS_FIELDS columns exist in invoice_items_df
                missing_cols = [col for col in INVOICED_LPNS_FIELDS.keys() if col not in self.invoice_items_df.columns]
                if missing_cols:
                    self.invoice_items_df = self.invoice_items_df.reindex(columns=self.invoice_items_df.columns.tolist() + missing_cols, fill_value='')
                
                
    def get_order_header_details(self, warehouse_id):
        """
        Fetches order header details for the given warehouse and order references.
        
        Args:
            warehouse_id (int): Warehouse ID to filter orders
        
        Returns:
            dict: Mapping of (warehouse_id, order_reference) to order header details
        """
        
        order_header_dict = {}
        if self.order_references:
            annotations = {"Order_Status": eval("Case(When(status=1, then=Value('Open')), When(status=0, then=Value('In Progress')), When(status=3, then=Value('Cancelled')), When(status=5, then=Value('Invoiced')), default=Value(''), output_field=CharField())")}
            order_header_details = list(Order.objects.using(reports_database).filter(warehouse_id=warehouse_id, order_reference__in=self.order_references).values(**ORDER_HEADER_FIELDS).annotate(**annotations))
            for order in order_header_details:
                key = (order.get('Warehouse_id',''), order.get('Order_Reference', ''))
                order_header_dict[key] = order
        return order_header_dict
    
    def get_picklist_details(self, warehouse_id, order_header_dict):
        """
        Fetches picklist details and merges them with invoice items.
        
        Args:
            warehouse_id (int): Warehouse ID to filter picklist data
            order_header_dict (dict): Order header details to merge with picklist data
        
        Modifies:
            self.invoice_items_df (pd.DataFrame): Updated DataFrame with picklist details
        """
        
        if self.picklist_numbers and self.order_references:
            annotations = {
                "Order_SKU_Status": eval("Case(When(order__status=1, then=Value('Open')), When(order__status=0, then=Value('In Progress')), When(order__status=3, then=Value('Cancelled')), When(order__status=5, then=Value('Invoiced')), default=Value(''), output_field=CharField())")
            }
            self.picklist_details_df = pd.DataFrame(columns=list(PICKLIST_DETAILS_FIELDS.keys())+list(list(order_header_dict.values())[0].keys())+["Order_SKU_Status"])
            picklist_details = list(Picklist.objects.using(reports_database).filter(order__user =warehouse_id, picklist_number__in=self.picklist_numbers, order__order_reference__in=self.order_references).values(**PICKLIST_DETAILS_FIELDS).annotate(**annotations))
            for picklist in picklist_details:
                key = (picklist.get('Warehouse_id', ''), picklist.get('Order_Reference', ''))
                if key in order_header_dict:
                    picklist.update(order_header_dict[key])
            self.picklist_details_df = pd.DataFrame(picklist_details)
            if not self.picklist_details_df.empty:
                # Convert Line_Reference to string in both DataFrames to ensure consistent data types for merge
                self.picklist_details_df['Batch_Number'] = self.picklist_details_df['Batch_Number'].map(lambda x: '' if pd.isna(x) else x)

                # Group by and sum Picked_Qty before merging
                if 'Picked_Qty' in self.picklist_details_df.columns:
                    self.picklist_details_df['Picked_Qty'] = pd.to_numeric(self.picklist_details_df['Picked_Qty'], errors='coerce').fillna(0)
                    self.picklist_details_df = (
                        self.picklist_details_df
                        .groupby(['Order_Reference', 'Picklist_Number', 'SKU_Code', 'Batch_Number'], as_index=False)
                        .agg({'Picked_Qty': 'sum', **{col: 'first' for col in self.picklist_details_df.columns if col not in ['Picked_Qty']}})
                    )
                self.picklist_details_df['Picklist_Number'] = self.picklist_details_df['Picklist_Number'].astype(str)
                
                self.invoice_items_df = pd.merge(
                    self.invoice_items_df,
                    self.picklist_details_df,
                    how='left',
                    left_on=['Order_Reference', 'Picklist_Number', 'SKU_Code', 'Batch_Number'],
                    right_on=['Order_Reference', 'Picklist_Number', 'SKU_Code', 'Batch_Number']
                )
                
    def get_order_shipment_details_and_gate_details(self, warehouse_id):
        """
        Fetches order shipment and gate pass details and merges them with invoice items.
        
        Args:
            warehouse_id (int): Warehouse ID to filter shipment and gate data
        
        Modifies:
            self.invoice_items_df (pd.DataFrame): Updated DataFrame with shipment and gate details
        """
        
        if self.invoice_references:
            order_shipment_df = pd.DataFrame(columns=list(ORDER_SHIPMENT_FIELDS.keys())+list(GATE_PASS_FIELDS.keys()))
            order_shipment_objs = OrderShipmentItems.objects.using(reports_database).filter(order_shipment__user_id=warehouse_id, shipment_invoice__reference_number__in=self.invoice_references)
            order_shipment_details = list(order_shipment_objs.values(**ORDER_SHIPMENT_FIELDS).distinct())
            self.order_shipment_ids = list(order_shipment_objs.values_list('order_shipment_id', flat=True).distinct())
            manifests = dict(ManifestDetail.objects.using(reports_database).filter(warehouse_id=warehouse_id, order_shipment_id__in=self.order_shipment_ids).values_list('order_shipment_id', 'manifest_number').distinct())
            manifest_numbers = list(manifests.values())
            gate_pass_details = self.get_gate_details(warehouse_id, manifest_numbers)
            for order_shipment in order_shipment_details:
                order_shipment_id = order_shipment.get('Order_shipment_id', '')
                if order_shipment_id in manifests:
                    manifest_number = manifests[order_shipment_id]
                    gate_details = gate_pass_details.get((warehouse_id, manifest_number), {})
                    order_shipment.update(gate_details)
            order_shipment_df = pd.DataFrame(order_shipment_details)
            order_shipment_df = order_shipment_df.fillna('')
            if not order_shipment_df.empty:
                self.invoice_items_df = pd.merge(
                    self.invoice_items_df,
                    order_shipment_df,
                    how='left',
                    left_on=['Warehouse_id_x','Invoice_Reference'],
                    right_on=['Warehouse_id', 'Invoice_Reference']
                )                       

    def get_gate_details(self, warehouse_id, manifest_numbers):
        """
        Fetches gate pass details for given manifest numbers.
        
        Args:
            warehouse_id (int): Warehouse ID to filter gate pass data
            manifest_numbers (list): List of manifest numbers to filter by
        
        Returns:
            dict: Mapping of (warehouse_id, manifest_number) to gate pass details
        """
        gate_pass_details = {}
        if manifest_numbers:
            annotations = {
                "Gate_Pass_Status": eval("Case(When(gate_pass__status=0 , then= Value('Created')),When(gate_pass__status = 1, then = Value('Gate IN')),When(gate_pass__status = 2, then= Value('Gate Out')),default=Value(''),output_field=CharField())")
            }
            gate_pass_data = list(GatePassItem.objects.using(reports_database).filter(gate_pass__warehouse_id=warehouse_id, transaction_id__in=manifest_numbers, transaction_type='sales_order').values(**GATE_PASS_FIELDS).annotate(**annotations))
            for gate_pass in gate_pass_data:
                key = (gate_pass.get('Warehouse_id', ''), gate_pass.get('Manifest_Number', ''))
                gate_pass_details[key] = gate_pass
        return gate_pass_details

    def get_serial_number_transaction_details(self, warehouse_id):
        """
        Fetches serial number transaction details and merges them with invoice items.
        
        Args:
            warehouse_id (int): Warehouse ID to filter serial number transactions
        
        Modifies:
            self.invoice_items_df (pd.DataFrame): Updated DataFrame with serial number transaction details
        """
        if self.invoice_references:
            serial_number_transaction_df = pd.DataFrame(columns=list(SERIAL_NUMBER_TRANSACTION_FIELDS.keys()))
            serial_number_transaction_details = list(SerialNumberTransaction.objects.using(reports_database).filter(
                warehouse=warehouse_id, reference_number__in=self.invoice_references, reference_type='invoice'
            ).values(**SERIAL_NUMBER_TRANSACTION_FIELDS))
            serial_number_transaction_df = pd.DataFrame(serial_number_transaction_details)
            if not serial_number_transaction_df.empty:
                serial_number_transaction_df = serial_number_transaction_df.fillna('')
                self.invoice_items_df = pd.merge(
                    self.invoice_items_df,
                    serial_number_transaction_df,
                    how='left',
                    left_on=['Invoice_Reference', 'SKU_Code', 'Batch_Number', 'Invoice_LPN_No'],
                    right_on=['Invoice_Reference', 'SKU_Code', 'Batch_Number', 'LPN_Number']
                )
                
    def transform_report_data(self):
        """
        Transforms and formats the report data, including date formatting and financial year calculation.
        Modifies:
            self.invoice_items_df (pd.DataFrame): Transformed DataFrame ready for reporting with
                                                 formatted dates and calculated financial years
        """
        
        date_fields = [
            'Order_Date', 'Invoice_Date', "Order_Created_Date", 'Gate_Pass_Date'
        ]
        
        def format_date_field(date_obj):
            """
            Formats date object to local timezone string format.
            Args:
                date_obj: Date object to format
            Returns:
                str: Formatted date string or empty string if date is None/NaN
            """
            if date_obj and pd.notna(date_obj):
                local_time = date_obj.astimezone(pytz.timezone(self.time_zone))
                return local_time.strftime('%Y-%m-%d %H:%M:%S')
            return ''
        
        def get_financial_year(date_obj):
            """
            Calculates financial year based on date (April 1st to March 31st cycle).
            Args:
                date_obj: Date object to calculate financial year for
            Returns:
                str: Financial year in format 'YY-YY' or empty string if date is None/NaN
            """
            if date_obj and pd.notna(date_obj):
                date_val = date_obj.date() if hasattr(date_obj, 'date') else date_obj
                year_of_date = date_val.year
                financial_year_start_date = datetime.strptime(str(year_of_date) + "-04-01", "%Y-%m-%d").date()
                if date_val < financial_year_start_date:
                    return f'{"-" }'.join([str(financial_year_start_date.year - 1)[2:], str(financial_year_start_date.year)[2:]])
                else:
                    return f'{"-" }'.join([str(financial_year_start_date.year)[2:], str(financial_year_start_date.year + 1)[2:]])
            return ''

        # Calculate Financial_Year before formatting dates to strings
        if 'Invoice_Date' in self.invoice_items_df.columns:
            self.invoice_items_df['Financial_Year'] = self.invoice_items_df['Invoice_Date'].apply(get_financial_year)

        # Transform date fields to formatted strings
        for field in date_fields:
            if field in self.invoice_items_df.columns:
                self.invoice_items_df[field] = self.invoice_items_df[field].apply(format_date_field)

        # Ensure only DISPATCH_REPORT_COLUMNS are present in the DataFrame, in the same order
        self.invoice_items_df = self.invoice_items_df.reindex(columns=DISPATCH_REPORT_COLUMNS)

    def generate_dispatch_summary_report(self, warehouse_id):
        """
        Generates a dispatch summary report for the given warehouse.
        Args:
            warehouse_id (int): The ID of the warehouse
        Returns:
            pd.DataFrame: The generated dispatch summary report or empty DataFrame on error
        """
        
        self.errors = {}
        self.search_params = {
            'warehouse_id': warehouse_id,
        }
        self.prepare_report_filters()
        self.time_zone = 'Asia/Calcutta'
        user_details = UserProfile.objects.get(user_id=warehouse_id)
        if user_details.timezone:
            self.time_zone = user_details.timezone
        try:
            self.invoice_items_df = pd.DataFrame(columns=list(INVOICE_ITEMS_FIELDS.keys()))
            self.final_data, self.invoice_items_ids, self.order_references, self.picklist_numbers, self.invoice_references, self.order_shipment_ids = [], [], [], [], [], []
            self.warehouse_zone_details = dict(UserGroups.objects.using(reports_database).filter(user_id=warehouse_id).values_list('user_id', 'admin_user__first_name'))
            self.get_invoice_items_details()
            self.get_invoiced_lpns_details(warehouse_id)
            order_header_dict = self.get_order_header_details(warehouse_id)
            self.get_picklist_details(warehouse_id, order_header_dict)
            self.get_order_shipment_details_and_gate_details(warehouse_id)
            self.get_serial_number_transaction_details(warehouse_id)
            self.transform_report_data()
            return self.invoice_items_df
        except Exception as e:
            log.error(f"Error generating dispatch summary report: {e}")
            self.errors['report_generation'] = str(e)
            return pd.DataFrame(columns=DISPATCH_REPORT_COLUMNS)