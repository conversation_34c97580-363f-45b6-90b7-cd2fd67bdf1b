#package imports
from collections import OrderedDict, defaultdict
from itertools import groupby
from json import loads
import traceback
from datetime import datetime

#django imports
from django.db.models import Sum, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Q
from django.contrib.postgres.aggregates import Array<PERSON>gg, StringAgg
from django.db.models.functions import Cast
from functools import reduce
import operator

#lms imports
from lms.models import ManualAssignment, TaskMaster

#wms imports
from wms.settings.base import reports_database
from wms_base.models import User, UserProfile
from wms_base.wms_utils import DEFAULT_DATETIME_FORMAT, DATATABLE_DATETIME_FORMAT

#core operations imports
from core_operations.views.common.main import (
    frame_datatable_column_filter, get_user_time_zone, get_related_users_filters, check_and_get_plants_depts,
    get_multiple_misc_values, get_admin, get_local_date_known_timezone, get_decimal_value, truncate_float,
    get_misc_options_list
)

#outbound imports
from outbound.models import (
    Picklist, IntermediateOrders, Order<PERSON>ields, PickAndPassStrategy
)

#constanst imports
from .constants import (
    BACKFLUSH_JO_TYPES, DATE_FORMAT, DATE_TIME_FORMAT, PICKLIST_STRATEGIES
)

#logger import
from wms_base.wms_utils import init_logger

log = init_logger('logs/datatable.log')

def open_orders(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse: User, filters, status='open'):
    try:
        status_dict = loads(status)
    except Exception:
        status_dict = status
    users = [warehouse.id]
    user_timezone =get_user_time_zone(warehouse)
    
    #fetching user ids
    if request.user.is_staff and warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
        user_ids = list(users.values_list('id', flat=True))
        user_ids.append(warehouse.id)
    else:
        users = check_and_get_plants_depts(request, users)
        user_ids = list(users.values_list('id', flat=True))
    
    #fetching misc values
    misc_types= ['split_by_pick_group', 'user_sub_zone_mapping']
    multiple_misc_values= get_multiple_misc_values(misc_types, warehouse.id)
    split_by_pick_group = multiple_misc_values.get('split_by_pick_group','false')
    misc_options_dict = get_misc_options_list(['manual_assignment', 'hybrid_task_assignment'], warehouse, to_lower_case=False)
    multiple_misc_values.update(misc_options_dict)
    manual_assignment_order_types = misc_options_dict.get('manual_assignment', [])
    
    request_data = request.GET
    
    #prepare sortby and orderby data
    order_data, column_headers = prepare_sortby_and_orderby_params(request_data)

    #prepare filter params
    filter_params, exclude_filter = prepare_filter_params(request, status, status_dict)

    #prepare column filters
    filter_query, column_filters_dict = prepare_column_filters_dict(column_headers)
    picker_name = column_filters_dict.pop('picker_name', '')
    can_skip_query = False
    if picker_name:
        picklists = get_open_picklists_for_picker(picker_name, warehouse)
        picklist_no = column_filters_dict.get('picklist_number')
        if (picklist_no and picklist_no not in picklists) or not picklists:
            can_skip_query = True
        elif not picklist_no:
            column_filters_dict['picklist_number__in'] = picklists

    #querying picklist
    try:
        if can_skip_query:
            all_picks = Picklist.objects.none()
        else:
            all_picks = Picklist.objects.using(reports_database).select_related('stock__location__zone')\
                .filter(user__in=user_ids, **filter_params, **column_filters_dict)\
                .filter(filter_query)\
                .exclude(**exclude_filter)
    
        master_data = all_picks
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info('Error in picklist query %s' % str(e))
        all_picks = Picklist.objects.none()
        master_data = all_picks

    tot_master_data = master_data

    is_count = False
    if request_data.get('count') == 'true':
        is_count = True

    #prepare master picklist data
    master_data = prepare_master_data(request, warehouse, user_timezone, order_data, order_term, multiple_misc_values, split_by_pick_group, tot_master_data, temp_data, start_index, stop_index, is_count=is_count)

    if request_data.get('count') == 'true':
        temp_data['count'] = temp_data['recordsTotal']
        return

    #prepare group data
    temp_data, all_pick_number, group = prepare_group_and_temp_data(master_data, temp_data, split_by_pick_group)
    
    #prepare open picklist data
    grouping_pick_dict = prepare_open_picklist_data(all_pick_number, tot_master_data, split_by_pick_group, multiple_misc_values)

    #prepare picklist reserved data
    reserved_qty_dict = prepare_picklist_reserved_data(all_picks, all_pick_number, split_by_pick_group)
    
    #prepare manual assignment data
    all_pick_ids = list(all_picks.filter(picklist_number__in=all_pick_number).values_list('id',flat=True))
    manual_obj_dict, task_obj_dict, pick_and_pass_dict = prepare_manual_assignment_data(warehouse, all_pick_ids, all_pick_number, manual_assignment_order_types, multiple_misc_values)

    dc_number_dict = {}
    count = 0
    od_id, od_order_id = '', ''
    decimal_limit = get_decimal_value(warehouse.id)
    for data in master_data:
        dc_num, trip_id, prepare_str, shipment_date, dispatch_date, source_wh = '', '', '', '', '', ''
        order_reference, note, zones, customer_refs, pick_type = '', '', '', '', ''
        order_type = 'Sales Order'
        picklist_status = ''
        create_date_value, order_customer_name, picklist_id, remarks = '', [], '', ''
        if split_by_pick_group == 'true':
            pick_filter_key = (str(data.get('picklist_number')),str(data.get('sku__pick_group','')))
        else:
            pick_filter_key = (str(data.get('picklist_number')))
        if data['stock__sku__user']:
            if split_by_pick_group == 'true':
                pick_filter_key = (str(data.get('picklist_number')),str(data.get('sku__pick_group','')),str(data.get('stock__sku__user','')))
            else:
                pick_filter_key = (str(data.get('picklist_number')),str(data.get('stock__sku__user','')))    

        if split_by_pick_group == 'true':
            if group.get(data['picklist_number'], ''):
                grp_list = group.get(data['picklist_number'])
                final_list = list(set(grp_list) - set([data['sku__pick_group']]))
                join_str = ', '.join(final_list)
                if join_str:
                    note = 'Please ensure to pick the remaining items'+join_str
                else:
                    note = 'Please ensure to pick the remaining items.'
        picklist_obj = grouping_pick_dict.get(pick_filter_key,'')
        distributor_name = data["order__distributor__first_name"]
       
        qty_dict = reserved_qty_dict.get(pick_filter_key, {})
        if not qty_dict:
            reserved_quantity_sum_value = 0
            picked_quantity_sum_value = 0
        else:
            reserved_quantity_sum_value = qty_dict.get('reserved_qty',0)
            picked_quantity_sum_value = qty_dict.get('picked_qty',0)
        picker_name = ""
        warehouse_id = data.get('order__sku__user', '')
        if picklist_obj:
            warehouse_id = data['stock__sku__user'] if data['stock__sku__user'] else picklist_obj.get('order__sku__user')
            order_customer_name = picklist_obj.get('customer_name',[])       
            prepare_str = ','.join(list(set(order_customer_name)))
            customer_refs = ','.join(list(set(picklist_obj.get('customer_reference', []))))
            
            create_date_value = ""
            if picklist_obj.get('creation_date',''):
                create_date_value = get_local_date_known_timezone(user_timezone, picklist_obj.get('creation_date',''),True)
                create_date_value = create_date_value.strftime("%d %b, %Y %H:%M")

            slot_from, slot_to, trip_id, route_id, route_name = '','','','', ''
            order_type = (picklist_obj.get('order_type','')) or order_type
            order_reference = picklist_obj.get('reference_number',[]) or order_reference
            number_of_orders = len(order_reference)
            order_reference = ', '.join(order_reference)
            remarks = picklist_obj.get('remarks','')
            if picklist_obj.get('order__id',''):
                od_order_id = str(picklist_obj.get('original_order_id',''))
                forward_time = picklist_obj.get('forward_time','')
                trip_id = picklist_obj.get('trip_id','')
                route_id = picklist_obj.get('route_id','')
                route_name = picklist_obj.get('route_name','')
                if forward_time:
                    forward_time = "%s %s" %(str(round(int(forward_time)/60, 1)), 'Min')
                try:
                    slot_from = get_local_date_known_timezone(user_timezone, picklist_obj.get('slot_from',''),True)
                    slot_to =  get_local_date_known_timezone(user_timezone,picklist_obj.get('slot_to',''),True)
                    if slot_to:
                       slot_to= slot_to.strftime("%I:%M %p")
                    if slot_from:
                        slot_from= slot_from.strftime("%I:%M %p")
                except Exception:
                    pass
            else:
                od_order_id, slot_from, slot_to, forward_time, trip_id = '','','','',''
            if remarks == 'Auto-generated Picklist':
                try:
                    od_id = int(picklist_obj.get('reference_id', ''))
                except Exception:
                    pass
                od_order_id = str(picklist_obj.get('original_order_id',''))

            picklist_id = picklist_obj.get('picklist_number','')
            city = picklist_obj.get('city','')
            picker_name, task_priority = '', 'Standard'

            try:
                picker_name, task_priority = get_picker_name_from_task(picklist_id, warehouse, task_obj_dict, manual_obj_dict, pick_and_pass_dict)
            except Exception:
                pass        

            try:    
                dc_number_obj = dc_number_dict.get(str(picklist_id))
                if dc_number_obj:
                    dc_num = dc_number_obj.get('dc_number','')
            except Exception:
                pass

            status = 'Not completed'
            if reserved_quantity_sum_value and picked_quantity_sum_value:
                status = 'Partially completed'
            if not reserved_quantity_sum_value:
                status = 'Completed'
            time_slot = ''
            if picklist_obj.get('order__id',''):
                ship_date = picklist_obj.get('shipment_date','')
                if ship_date:
                    shipment_date = get_local_date_known_timezone(user_timezone, ship_date, True)
                    shipment_date = shipment_date.strftime("%d %b, %Y")
                if picklist_obj.get('promised_time',''):
                    time_slot = picklist_obj.get('promised_time','')
                if time_slot and shipment_date:
                    shipment_date = shipment_date+ ', ' + time_slot
                else:
                    if slot_to  and shipment_date:
                        shipment_date = shipment_date + ', ' + slot_to
                dispatch_date = get_dispatch_date(picklist_obj, user_timezone)
            zones = picklist_obj.get('zones','')
            pick_type = picklist_obj.get('pick_type', '')
            pick_type = PICKLIST_STRATEGIES.get(pick_type, pick_type)
        else:
            continue
        try:
            picklist_status = "In Progress" if picker_name else "Open"
            if remarks == "alternative picklist":
                picklist_status = "In Progress(Alternative)" if picker_name else "Alternative"
        except Exception:
            pass
        
        result_data = OrderedDict((
            ('DT_RowAttr', {'data-id': picklist_id}), ('picklist_status', picklist_status),
            ('reserved_quantity', truncate_float(float(reserved_quantity_sum_value or 0), decimal_limit)), ('order_type', order_type),
            ('picked_quantity', picked_quantity_sum_value), ('source_wh', source_wh),
            ('customer', prepare_str), ('shipment_date', shipment_date),
            ('order_reference', order_reference),('pick_group', data.get('sku__pick_group','')),
            ('date', create_date_value),('dc_number', dc_num), ('id', count), ('dispatch_date', dispatch_date),
            ('DT_RowClass', 'results'),('trip_id', trip_id),
            ('od_id', od_id), ('od_order_id', od_order_id), ('task_priority', task_priority),
            ('picker_name', picker_name), ('status', status), ('note', note),
            ('warehouse_id', warehouse_id), ('slot_from', slot_from), 
            ('slot_to', slot_to), ('forward_time', forward_time),
            ('distributor_name',distributor_name),
            ('order_display_key', order_reference or od_order_id),
            ('wave_reference', qty_dict.get('wave_reference','')),
            ('cluster_name', qty_dict.get('cluster_name','')),
            ('number_of_orders', number_of_orders),
            ('number_of_skus', qty_dict.get('sku_count','')),
            ('city', city), ('zones', zones), ('route_id', route_id), ('route_name', route_name),
            ('customer_reference', customer_refs),
            ('pick_type', pick_type),
        ))
        dat = 'picklist_id'
        count += 1
        if status == 'batch_picked':
            dat = 'picklist_id'

            checkbox = "<input type='checkbox' name='%s' value='%s'>" % (
            data['picklist_number'], data['picklist_number'])
            result_data['checkbox'] = checkbox
        result_data[dat] = picklist_id
        temp_data['aaData'].append(result_data)

def get_picker_name_from_task(picklist_id, warehouse, task_obj_dict, manual_obj_dict, pick_and_pass_dict):
    """
    Retrieves the picker name from the task object or manual object based on the given parameters.

    Returns:
    - picker_name (str): The picker name retrieved from the task object or manual object.
    """
    picklist_prority_mapping = {
        1: 'High',
        2: 'Standard',
        3: 'Low'
    }
    picker_name, priority = '', 2
    manual_task_key = (str(picklist_id), str(warehouse.id))
    pick_and_pass_key = (str(picklist_id), str(warehouse.id))
    task_master = task_obj_dict.get(str(picklist_id), {})
    if manual_task_key in manual_obj_dict:
        picker_name = manual_obj_dict.get(manual_task_key,'')
        picker_name = ', '.join(picker_name)
    elif pick_and_pass_key in pick_and_pass_dict:
        picker_name = pick_and_pass_dict.get(pick_and_pass_key, [])
        picker_name = ', '.join(picker_name)
    elif task_master:
        picker_name = task_master.get('picker_name', [])
        picker_name = ', '.join(picker_name)
    priority = task_master.get('task_priority', 2) or 2
    task_priority = picklist_prority_mapping.get(priority, 'Standard')
    return picker_name, task_priority

def get_dispatch_date(picklist_obj, user_timezone):
    '''Function to get dispatch date'''
    dispatch_date = ""
    dispatch_date_time = picklist_obj.get('dispath_date')
    if dispatch_date_time:
        dispatch_date = get_local_date_known_timezone(user_timezone, dispatch_date_time, True)
        dispatch_date = dispatch_date.strftime("%d %b, %Y, %I:%M %p")
    return dispatch_date

def prepare_sortby_and_orderby_params(request_data):
    ''' Function to prepare sort by and order by data '''
    column_headers = loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-picklist_number"
    sort_type = request_data.get('sort_type', 0)
    order_data_dict = {"order_reference":"order__order_reference", "picklist_id": "picklist_number",
                       "customer": "order__customer_name", "slot_from": "slot_from",
                       "slot_to": "slot_to", "trip_id": "order__trip_id",
                       "shipment_date": "shipment_date",
                       "dispatch_date": "dispatch_date",
                       "forward_time": "order__forward_time",
                       "date": "picklist_date",
                       "customer_reference": "order__customer_identifier__customer_reference",
                       }
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column

    #Framing Order By Params
    if sort_type == '1':
        order_data = '-%s' % order_data
    
    return order_data, column_headers

def prepare_filter_params(request, status, status_dict):
    '''Prepare filter params and exclude filter'''
    exclude_filter, filter_params = {}, {}

    #status filter
    if isinstance(status_dict, dict):
        status = status_dict['status']
        filter_params['status__in'] = [status]
    if 'open' in status:
        filter_params['reserved_quantity__gt'] = 0
        filter_params['status__in'] = ["open", "batch_open"]
    else:
        if status != 'batch_picked':
            del filter_params['status__in']
        filter_params['picked_quantity__gt'] = 0

    #ordertype filter
    filter_params['location__isnull'] = False
    if request.GET.get('job_order', '') == 'true':
        filter_params['order_type__in'] = ['Standard JO', 'Non Standard JO','Returnable JO']
    else:
        exclude_filter['order_type__in'] = BACKFLUSH_JO_TYPES
    
    return filter_params, exclude_filter

def prepare_multi_option_filter_key(col_key, in_filter_key):
    if in_filter_key:
        if '__icontains' in col_key:
            col_key = col_key.replace('__icontains', in_filter_key)
        else:
            col_key = col_key + in_filter_key
    return col_key

def prepare_column_filters_dict(column_headers):
    '''Prepare Column filters and Q filters'''
    or_filters = []
    column_filters_dict = {}

    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {
            "order_reference": "order__order_reference",
            "order_reference__icontains": "order__order_reference__icontains",
            "customer__icontains": "order__customer_name__icontains",
            "order_type__icontains": "order_type__icontains",
            "trip_id__icontains": "order__trip_id__icontains",
            "picklist_id__icontains":"picklist_number",
            "order_display_key__icontains": ["order__order_reference__icontains", "order__original_order_id__icontains", "reference_number__icontains"],
            "city__icontains": "order__city__icontains",
            "date__date": "creation_date__date",
            "route_id__icontains": "order__customer_identifier__route__route_id",
            "route_name__icontains": "order__customer_identifier__route__name",
            "customer_reference__icontains": "order__customer_identifier__customer_reference__icontains",
            "picker_name__icontains" : "picker_name"
        }
        
        for key, value in column_filters.items():
            if not value:
                continue
            if key in custom_filter_dict and isinstance(custom_filter_dict[key], list):
                or_filters += [Q(**{custom_filter_dict[key][i]: value}) for i in range(len(custom_filter_dict[key]))]
            else:
                in_filter_key = ''
                if ',' in value:
                    value = value.split(',')
                    in_filter_key = '__in'
                if key in custom_filter_dict:
                    col_key = custom_filter_dict[key]
                    col_key = prepare_multi_option_filter_key(col_key, in_filter_key)
                    column_filters_dict[col_key] = value
                elif key == 'pick_type__icontains':
                    pick_types = []
                    pick_type_dict = dict(zip(PICKLIST_STRATEGIES.values(), PICKLIST_STRATEGIES.keys()))
                    if in_filter_key:
                        for key in value:
                            pick_types.append(pick_type_dict.get(key, key))
                    if pick_types:
                        column_filters_dict['pick_type__in'] = pick_types
                    else:
                        column_filters_dict['pick_type__icontains'] = pick_type_dict.get(value, value)
                else:
                    col_key = prepare_multi_option_filter_key(key, in_filter_key)
                    column_filters_dict[col_key] = value
    filter_query = reduce(operator.or_, or_filters) if or_filters else Q()

    return filter_query, column_filters_dict

def prepare_master_data(request, warehouse, user_timezone, order_data, order_term, multiple_misc_values, split_by_pick_group, tot_master_data, temp_data, start_index, stop_index, is_count=False):
    '''Prepare master picklist data'''
    if split_by_pick_group == 'true':
        open_pick_val_list = ['picklist_number', 'stock__sku__user', 'order__distributor__first_name', 'sku__pick_group']
    else:
        open_pick_val_list = ['picklist_number', 'stock__sku__user', 'order__distributor__first_name']
    
    master_data = tot_master_data.values(*open_pick_val_list).annotate(
        picklist_date=Max('creation_date'), shipment_date=Max('order__shipment_date'),
        slot_from=Max('order__slot_from'), slot_to=Max('order__slot_to'),
        dispatch_date=Max('order__estimated_dispatch_time')
        ).order_by(order_data)

    if is_count:
        temp_data['recordsTotal'] = master_data.count()
        temp_data['recordsFiltered'] = temp_data['recordsTotal']
    master_data = list(master_data[start_index:stop_index])
    
    data_format = request.GET.get('data_format', '')
    if request.GET.get('excel')=='true' and data_format == 'sku_wise':
        return get_sku_wise_pending_picklist(warehouse, user_timezone, temp_data, tot_master_data, switch_values=multiple_misc_values)
    if order_term:
        master_data = [key for key, _ in groupby(master_data)]
    
    return master_data

def prepare_group_and_temp_data(master_data, temp_data, split_by_pick_group):
    '''Prepare group and temp data'''
    group = {}
    total_picked_quantity, total_reserved_quantity = 0, 0
    for grp_data in master_data:
        if not group.get(grp_data['picklist_number'], ''):
            group[grp_data['picklist_number']] = []
        if split_by_pick_group == 'true':
            if grp_data['sku__pick_group'] not in group[grp_data['picklist_number']]:
                group[grp_data['picklist_number']].append(grp_data['sku__pick_group'])
    all_pick_number = list(group.keys())
    
    if not total_picked_quantity:
        total_picked_quantity = 0
    else:
        temp_data['totalPickedQuantity'] = int(total_picked_quantity)

    if not total_reserved_quantity:
        temp_data['totalReservedQuantity'] = 0
    else:
        temp_data['totalReservedQuantity'] = int(total_reserved_quantity)
    
    return temp_data, all_pick_number, group

def prepare_open_picklist_data(all_pick_number, tot_master_data, split_by_pick_group, multiple_misc_values):
    '''Function to prepare open picklist data'''
    grouping_pick_dict, unique_zones = {}, {}
    if split_by_pick_group == 'true':
        open_picklist_data_vals = [
            'picklist_number', 'id', 'reference_id', 'creation_date', 'remarks', 'order_type', 'reference_number',
            'pick_type', 'order', 'sku__pick_group', 'stock__sku__user',
            'order__sku__user', 'order__order_id', 'order__customer_name', 'order__original_order_id',
            'order__forward_time', 'order__nw_status', 'order__slot_to', 'order__slot_from', 'order__shipment_date',
            'order__estimated_dispatch_time', 'order__promised_time', 'order__trip_id', 'order__city',
            'order__customer_identifier__route__route_id', 'order__customer_identifier__route__name',
            'order__customer_identifier__customer_reference'
        ]
    else:
        open_picklist_data_vals = [
            'picklist_number', 'id', 'creation_date', 'remarks', 'order_type', 'reference_number', 'reference_id',
            'pick_type', 'order', 'stock__sku__user', 'order__sku__user',
            'order__order_id', 'order__customer_name', 'order__original_order_id', 'order__forward_time',
            'order__nw_status', 'order__slot_to', 'order__slot_from', 'order__shipment_date',
            'order__estimated_dispatch_time', 'order__promised_time', 'order__trip_id', 'order__city',
            'order__customer_identifier__route__route_id', 'order__customer_identifier__route__name',
            'order__customer_identifier__customer_reference'
        ]
    if multiple_misc_values.get('user_sub_zone_mapping', '') == 'true':
        open_picklist_data_vals.append('stock__location__sub_zone__zone')
    else:
        open_picklist_data_vals.append('stock__location__zone__zone')
    open_picklist_data = list(tot_master_data.filter(picklist_number__in=all_pick_number).values(*open_picklist_data_vals))
    
    for each_data in open_picklist_data:
        if split_by_pick_group == 'true':
            uniq_pick_key = (str(each_data.get('picklist_number','')),str(each_data.get('sku__pick_group','')))
        else:
            uniq_pick_key = (str(each_data.get('picklist_number','')))
        if each_data.get('stock__sku__user',''):
            if split_by_pick_group == 'true':
                uniq_pick_key = (str(each_data.get('picklist_number','')),str(each_data.get('sku__pick_group','')),str(each_data.get('stock__sku__user')))
            else:
                uniq_pick_key = (str(each_data.get('picklist_number','')),str(each_data.get('stock__sku__user')))
        pick_zone = each_data['stock__location__sub_zone__zone'] if multiple_misc_values.get('user_sub_zone_mapping', '') == 'true' else each_data['stock__location__zone__zone']
        if uniq_pick_key in grouping_pick_dict:
            grouping_pick_dict = prepare_group_picking_unique_data(each_data, uniq_pick_key, grouping_pick_dict)
            if pick_zone not in unique_zones[uniq_pick_key]:
                grouping_pick_dict[uniq_pick_key]['zones'] += ',' + pick_zone
                unique_zones[uniq_pick_key].add(pick_zone)
        else:
            grouping_pick_dict[uniq_pick_key] = {
                'id':each_data.get('id',''),
                'picklist_number':each_data.get('picklist_number',''),
                'creation_date':each_data.get('creation_date',''),
                'order__order_id':each_data.get('order__order_id',''),
                'original_order_id':each_data.get('order__original_order_id',''),
                'order__id':each_data.get('order',''),
                'forward_time':each_data.get('order__forward_time',''),
                'nw_status':each_data.get('order__nw_status',''),
                'order_type':each_data.get('order_type'),
                'zones' : pick_zone,
                'route_id': each_data.get('order__customer_identifier__route__route_id',''),
                'remarks':each_data.get('remarks',''),
                'slot_to':each_data.get('order__slot_to',''),
                'slot_from':each_data.get('order__slot_from',''),
                'shipment_date':each_data.get('order__shipment_date',''),
                'trip_id': each_data.get('order__trip_id',''),
                'route_name': each_data.get('order__customer_identifier__route__name',''),
                'dispath_date': each_data.get('order__estimated_dispatch_time',''),
                'order__sku__user':each_data.get('order__sku__user',''),
                'promised_time':each_data.get('order__promised_time',''),
                'reference_id': each_data.get('reference_id'),
                'city': each_data.get('order__city', ''),
                'pick_type': each_data.get('pick_type',''),
            }
            if each_data.get('order__customer_name',''):
                grouping_pick_dict[uniq_pick_key]['customer_name'] = [each_data.get('order__customer_name','')]
            if each_data.get('order__customer_identifier__customer_reference',''):
                grouping_pick_dict[uniq_pick_key]['customer_reference'] = [each_data.get('order__customer_identifier__customer_reference', '')]
            if each_data.get('reference_number',''):
                grouping_pick_dict[uniq_pick_key]['reference_number'] = [each_data.get('reference_number','')]
            unique_zones[uniq_pick_key] = {pick_zone}
    return grouping_pick_dict

def prepare_group_picking_unique_data(each_data, uniq_pick_key, grouping_pick_dict):
    '''Prepare group picking data'''
    if each_data.get('order__customer_name',''):
        customer_name_list= each_data.get('order__customer_name','')
        if grouping_pick_dict[uniq_pick_key].get('customer_name',[]):
            if customer_name_list not in grouping_pick_dict[uniq_pick_key].get('customer_name',[]):
                grouping_pick_dict[uniq_pick_key]['customer_name'].append(customer_name_list)
        else:
            grouping_pick_dict[uniq_pick_key]['customer_name']=[customer_name_list]
    if each_data.get('order__customer_identifier__customer_reference',''):
        customer_reference = each_data.get('order__customer_identifier__customer_reference', '')
        if grouping_pick_dict[uniq_pick_key].get('customer_reference', []):
            if customer_reference not in grouping_pick_dict[uniq_pick_key].get('customer_reference', []):
                grouping_pick_dict[uniq_pick_key]['customer_reference'].append(customer_reference)
        else:
            grouping_pick_dict[uniq_pick_key]['customer_reference'] = [customer_reference]
    if each_data.get('reference_number',''):
        order_reference_list = each_data.get('reference_number','')
        if grouping_pick_dict[uniq_pick_key].get('reference_number',[]):
            if order_reference_list not in grouping_pick_dict[uniq_pick_key].get('reference_number',[]):
                grouping_pick_dict[uniq_pick_key]['reference_number'].append(order_reference_list)
        else:
            grouping_pick_dict[uniq_pick_key]['reference_number'] = [order_reference_list]
    if each_data.get('order__shipment_date',''):
        if not grouping_pick_dict[uniq_pick_key].get('shipment_date',''):
            grouping_pick_dict[uniq_pick_key]['shipment_date'] = each_data.get('order__shipment_date','')

    return grouping_pick_dict

def prepare_picklist_reserved_data(all_picks, all_pick_number, split_by_pick_group):
    '''Function to prepare picklist reserved data'''
    reserved_qty_dict={}
    if split_by_pick_group == 'true':
        picklist_res_vals = ['picklist_number', 'sku__pick_group', 'stock__sku__user', 'order__sku__sku_code', 'order__json_data']
    else:
        picklist_res_vals = ['picklist_number', 'stock__sku__user', 'order__sku__sku_code', 'order__json_data']
    picklist_reserved_obj = list(all_picks.filter(picklist_number__in=all_pick_number).values('order__json_data', 'json_data', 'reserved_quantity', 'picked_quantity', *picklist_res_vals))
    
    for each_pick_obj in picklist_reserved_obj:
        if split_by_pick_group == 'true':
            uniq_reserve_qty_key = (str(each_pick_obj.get('picklist_number','')),str(each_pick_obj.get('sku__pick_group','')))
        else:
            uniq_reserve_qty_key = (str(each_pick_obj.get('picklist_number','')))
        if each_pick_obj.get('stock__sku__user',''):
            if split_by_pick_group == 'true':
                uniq_reserve_qty_key = (str(each_pick_obj.get('picklist_number','')),str(each_pick_obj.get('sku__pick_group','')),str(each_pick_obj.get('stock__sku__user')))
            else:
                uniq_reserve_qty_key = (str(each_pick_obj.get('picklist_number','')),str(each_pick_obj.get('stock__sku__user')))
        
        reserved_qty_dict = prepare_picklist_reserved_unique_data(each_pick_obj, uniq_reserve_qty_key, reserved_qty_dict)

    return reserved_qty_dict

def prepare_picklist_reserved_unique_data(each_pick_obj, uniq_reserve_qty_key, reserved_qty_dict):
    '''Prepare picklist reserverd quantity data'''
    order_json = each_pick_obj.get('order__json_data',{}) or {}
    picklist_json = each_pick_obj.get('json_data',{}) or {}
    pack_uom = order_json.get('pack_uom_quantity','')
    if pack_uom:
        each_pick_obj['reserved_quantity'] = each_pick_obj['reserved_quantity'] / pack_uom
    if uniq_reserve_qty_key in reserved_qty_dict:
        if each_pick_obj.get('reserved_quantity',0):
            if reserved_qty_dict[uniq_reserve_qty_key].get('reserved_qty',''):
                reserved_qty_dict[uniq_reserve_qty_key]['reserved_qty'] += each_pick_obj.get('reserved_quantity',0)
            else:
                reserved_qty_dict[uniq_reserve_qty_key]['reserved_qty'] = each_pick_obj.get('reserved_quantity',0)
        if each_pick_obj.get('picked_quantity',0):
            if reserved_qty_dict[uniq_reserve_qty_key].get('picked_qty',''):
                reserved_qty_dict[uniq_reserve_qty_key]['picked_qty'] += each_pick_obj.get('picked_quantity',0)
            else:
                reserved_qty_dict[uniq_reserve_qty_key]['picked_qty'] = each_pick_obj.get('picked_quantity',0)    
    else:
        if each_pick_obj.get('reserved_quantity',0):
            reserved_qty_dict[uniq_reserve_qty_key] = {'reserved_qty':each_pick_obj.get('reserved_quantity',0), 'sku_codes': [], 'sku_count': 0}
        if each_pick_obj.get('picked_quantity',0):
            if reserved_qty_dict.get(uniq_reserve_qty_key,''):
                reserved_qty_dict[uniq_reserve_qty_key]['picked_qty']=each_pick_obj.get('picked_quantity',0)
            else:
                reserved_qty_dict[uniq_reserve_qty_key] = {'picked_qty':each_pick_obj.get('picked_quantity',0)}
    sku_code = each_pick_obj.get('order__sku__sku_code','')
    if sku_code and sku_code not in reserved_qty_dict[uniq_reserve_qty_key].get('sku_codes', []):
        reserved_qty_dict[uniq_reserve_qty_key]['sku_codes'].append(sku_code)
    reserved_qty_dict[uniq_reserve_qty_key].update({
        "wave_reference": picklist_json.get('wave_reference',''),
        "cluster_name": picklist_json.get('cluster_name',''),
        "sku_count": len(reserved_qty_dict[uniq_reserve_qty_key].get('sku_codes', []))
    })
    return reserved_qty_dict

def prepare_manual_assignment_data(warehouse, all_pick_ids, all_pick_number, manual_assignment_order_types, multiple_misc_values):
    '''Prepare manual assignment data'''
    task_obj_dict = {}
    manual_obj_dict = defaultdict(list)
    pick_and_pass_dict = {}
    hybrid_assignment_order_types = multiple_misc_values.get('hybrid_task_assignment', [])
    if manual_assignment_order_types or hybrid_assignment_order_types:
        manual_picklists_objs = list(ManualAssignment.objects.filter(warehouse_id=warehouse.id,reference_type='picklist',status__in=[0,2]).values('reference_number','employee__user__username'))
        for each_manual in manual_picklists_objs:
            uniq_picker_key = (str(each_manual.get('reference_number')),str(warehouse.id))
            assigned_user = each_manual['employee__user__username']
            if assigned_user not in manual_obj_dict[uniq_picker_key]:
                manual_obj_dict[uniq_picker_key].append(assigned_user)
        if 'all' in manual_assignment_order_types:
            return manual_obj_dict, task_obj_dict, pick_and_pass_dict

    if multiple_misc_values.get('user_sub_zone_mapping') == 'true':
        pick_and_pass_objs = list(PickAndPassStrategy.objects.filter(reference_type='so_picking', reference_number__in=all_pick_number, employee__isnull=False, status__in=['open', 'hold'], warehouse=warehouse).values('reference_number', 'employee__user__username').distinct())
        for each_pick_and_pass in pick_and_pass_objs:
            uniq_picker_key = (str(each_pick_and_pass.get('reference_number')), str(warehouse.id))
            pick_and_pass_dict.setdefault(uniq_picker_key, []).append(each_pick_and_pass.get('employee__user__username', ''))

    task_master_objs = TaskMaster.objects.using(reports_database).filter(warehouse=warehouse.id, task_ref_id__in=all_pick_ids).values('reference_number','employee__user__username', 'task_priority').distinct()
    for each_task in task_master_objs:
        uniq_task_key = str(each_task.get('reference_number', ''))
        if uniq_task_key not in task_obj_dict:
            task_obj_dict[uniq_task_key] = {'picker_name': []}
        if each_task.get('employee__user__username', '') and each_task.get('employee__user__username', '') not in task_obj_dict[uniq_task_key]['picker_name']:
            task_obj_dict[uniq_task_key]['picker_name'].append(each_task.get('employee__user__username'))
        task_obj_dict[uniq_task_key]['task_priority'] = each_task.get('task_priority', 2)
    
    return manual_obj_dict, task_obj_dict, pick_and_pass_dict

def get_picklist_items_details(picklist_objs):
    '''
    Picklist item level query
    '''
    return list(picklist_objs.values('picklist_number', 'order_type', sku_code = F('sku__sku_code'), sku_desc = F('sku__sku_desc'), batch_mrp=F('stock__batch_detail__mrp'),
            batch_no = F('stock__batch_detail__batch_no'), manufactured_date = F('stock__batch_detail__manufactured_date'), expiry_date = F('stock__batch_detail__expiry_date'),
            stock_location = F('stock__location__location'), stock_zone=F('stock__location__zone__zone')).annotate(reserved_qty=Sum('reserved_quantity'),picklist__row_id = ArrayAgg('id'),
            nw_status=Max('order__nw_status'), order_unit_price = Max('order__unit_price'), creation_date=Max('creation_date'), order_date=Max('order__creation_date'), 
            reference_number= StringAgg(Cast('reference_number', CharField()),distinct=True, delimiter=','), order__slot_from=Max('order__slot_from'), order__slot_to=Max('order__slot_to'),
            order__customer_id= StringAgg(Cast('order__customer_id', CharField()),distinct=True, delimiter=','), order__customer_name=StringAgg(Cast('order__customer_name', CharField()),distinct=True, delimiter=',')))

def get_task_master_details(warehouse: User, picklist_numbers, switch_values):
    '''
    Get task master details
    '''
    task_data_dict, exclude_dict = {}, {}
    task_objs, task_master_objs = [], []
    manual_assignment_order_types = switch_values.get('manual_assignment', [])

    if manual_assignment_order_types:
        exclude_dict.update({
            'order_type__in': manual_assignment_order_types,
            'employee': None
        })
        manual_task_master_objs = list(ManualAssignment.objects.filter(warehouse_id=warehouse.id,reference_type='picklist', status__in=[0,2], reference_number__in=picklist_numbers).values('reference_number', warehouse_user = F('warehouse_id'), picker_name=F('employee__user__username')))
        task_objs.extend(manual_task_master_objs)
    if 'all' not in manual_assignment_order_types:
        task_master_objs = list(TaskMaster.objects.using(reports_database).filter(warehouse=warehouse.id, reference_number__in=picklist_numbers).exclude(**exclude_dict).values('reference_number', warehouse_user = F('warehouse_id'), picker_name=F('employee__user__username')))
        task_objs.extend(task_master_objs)
    for each_task in task_objs:
        uniq_picker_key = (str(each_task.get('reference_number')), str(each_task.get('warehouse_user')))
        if not task_data_dict.get(uniq_picker_key):
            task_data_dict[uniq_picker_key]  = each_task.get('picker_name', '')
    return task_data_dict


def prepare_sku_wise_picklist_details(warehouse: User, timezone, picklist_data_list, task_details):
    '''
    Preparing item level data dict
    '''
    picklist_items = []
    for picklist_record in picklist_data_list:
        date_time_fields = formatting_datetime_fields(timezone, picklist_record)
        
        picklist_number = picklist_record.get('picklist_number','')
        picker_unique_key = (str(picklist_number), str(warehouse.id))
        picker_name = task_details.get(picker_unique_key,'')

        data_dict = OrderedDict((
            ('Order Reference', picklist_record.get('reference_number','')),
            ('Order Date', date_time_fields.get('order_date')),
            ('Customer Id', picklist_record.get('order__customer_id','')),
            ('Customer Name', picklist_record.get('order__customer_name','')),
            ('Picklist Number',picklist_record.get('picklist_number','')),
            ('Picklist Creation Time', date_time_fields.get('creation_date')),
            ('SKU Code', picklist_record.get('sku_code','')),
            ('SKU Description', picklist_record.get('sku_desc','')),
            ('Picklist Qty', picklist_record.get('reserved_qty',0)),
            ('MRP', picklist_record.get('batch_mrp','')),
            ('Unit Price', picklist_record.get('order_unit_price',0)),
            ('Batch No', picklist_record.get('batch_no','')),
            ('MFG Date', date_time_fields.get('manufactured_date')),
            ('Expiry Date', date_time_fields.get('expiry_date')),
            ('Location', picklist_record.get('stock_location','')),
            ('Zone', picklist_record.get('stock_zone','')),
            ('Picker Name', picker_name),
            ('Order Type', picklist_record.get('order_type','')),
            ('Slot From', date_time_fields.get('order__slot_to')),
            ('Slot To', date_time_fields.get('order__slot_from')),
        ))
        picklist_items.append(data_dict)

    return picklist_items

def formatting_datetime_fields(timezone, picklist_record):
    '''
    datetime format
    '''
    datetime_fields = {
        'manufactured_date': DATE_FORMAT,
        'expiry_date': DATE_FORMAT,
        'creation_date': DATE_TIME_FORMAT,
        'order_date': DATE_TIME_FORMAT,
        'order__slot_to': DATE_TIME_FORMAT,
        'order__slot_from': DATE_TIME_FORMAT
    }
    date_fields = {}

    for field_name, date_format in datetime_fields.items():
        try:
            date_fields[field_name] = get_local_date_known_timezone(timezone, picklist_record.get(field_name, ''), True).strftime(date_format)
        except Exception:
            pass

    return date_fields

def get_sku_wise_pending_picklist(warehouse: User, timezone, temp_data, picklist_objs, switch_values={}):
    '''
    Get sku wise pending picklist details
    '''
    picklist_numbers = []

    picklist_data_list = get_picklist_items_details(picklist_objs)
    for picklist_record in picklist_data_list:
        if picklist_record.get('picklist_number', ''):
            picklist_numbers.append(picklist_record["picklist_number"])
    picklist_numbers = list(set(picklist_numbers))

    task_details = get_task_master_details(warehouse, picklist_numbers, switch_values)
    total_records = len(picklist_data_list)
    temp_data['recordsTotal'] = temp_data['recordsFiltered'] = total_records

    temp_data['aaData'] = prepare_sku_wise_picklist_details(warehouse, timezone, picklist_data_list, task_details)
    
    return temp_data

def get_open_picklists_for_picker(picker_name, warehouse):
    picklists = set(ManualAssignment.objects.filter(warehouse_id=warehouse.id, employee__user__username=picker_name, status=0, reference_type='picklist').values_list('reference_number', flat=True))
    picklists |= set(TaskMaster.objects.filter(warehouse=warehouse.id, employee__user__username=picker_name, status=False, task_ref_type='Picklist').values_list('reference_number', flat=True))
    return picklists


def get_skipped_tasks_results(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse: User, filters={}):
    """
    Returns skipped tasks data for the Skipped Tasks datatable.
    """

    sort_type = request.GET.get('sort_type', 0)
    sorting_key = request.GET.get('sort_by_column')
    column_filters = request.GET.get('columnFilter', '{}')
    is_count = True if request.GET.get('count', '') == 'true' else False

    column_filters = loads(column_filters)
    if 'skipped_date' in column_filters:
        column_filters['updation_date__date'] = column_filters.pop('skipped_date')

    picklist_filters = {
        'user_id': warehouse.id,
        'status': 'hold',
    }

    filters_mapping = {
        'order_reference': 'reference_number',
        'customer_name': 'order__customer_name',
        'sku_code': 'sku__sku_code',
        'batch_no': 'stock__batch_detail__batch_no',
        'location': 'stock__location__location',
        'zone': 'stock__location__zone__zone',
    }
    for filter in column_filters:
        if filter in filters_mapping:
            picklist_filters[filters_mapping[filter]] = column_filters[filter]

    order_by = ['-id']
    if sorting_key:
        if sort_type == '1':
            sorting_key = f"-{sorting_key}"
        order_by.insert(0, sorting_key)

    picklist_data = Picklist.objects.filter(**picklist_filters).values(
        'picklist_number', 'stock__location__location', order_reference=F('reference_number'), trip_id=F('order__trip_id'),
        customer_name=F('order__customer_name'), customer_reference=F('order__customer_identifier__customer_reference'),
        route_id=F('order__customer_identifier__route__route_id'), route_name=F('order__customer_identifier__route__name'),
        sku_code=F('sku__sku_code'), sku_desc=F('sku__sku_desc'), batch_no=F('stock__batch_detail__batch_no'),
        zone=F('stock__location__zone__zone')
    ).annotate(picklist_id=StringAgg(Cast('id', CharField()), delimiter=','), quantity=Sum('reserved_quantity'),
               skip_json_data=Max(Cast('json_data', CharField()))
    ).order_by(*order_by)

    if is_count:
        temp_data['count'] = picklist_data.count()
        return
    picklist_data = list(picklist_data[start_index: stop_index])

    for each_picklist in picklist_data:
        each_picklist['location'] = each_picklist.pop('stock__location__location', '')
        skip_json_data = loads(each_picklist.pop('skip_json_data', '{}'))
        each_picklist['reason'] = skip_json_data.get('skip_reason', '')
        each_picklist['skip_count'] = skip_json_data.get('skip_count', 0)
        each_picklist['picker'] = skip_json_data.get('skipped_by', '')
        skipped_date = skip_json_data.get('skipped_date', '')
        try:
            if skipped_date:
               skipped_date = datetime.strptime(skipped_date, DEFAULT_DATETIME_FORMAT).strftime(DATATABLE_DATETIME_FORMAT)
        except Exception:
            skipped_date = ''
        each_picklist['date'] = skipped_date
        temp_data['aaData'].append(each_picklist)
