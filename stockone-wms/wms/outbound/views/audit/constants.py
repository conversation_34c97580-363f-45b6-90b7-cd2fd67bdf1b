MANDATORY_FIELDS = ['reference_type', 'reference_number', 'audit_type', 'audit_value', 'status']

AUDIT_MANDATORY_FIELDS = ['lpn_number', 'items']

VALID_REFERENCE_TYPES = ['customer_reference', 'zone', 'sub_zone', 'picker_name', 'order_type']

VALID_AUDIT_TYPES = ['fixed_count', 'sampling_percentage']

STATUS_TEXT_MAPPING = {
    1: 'Open',
    2: 'Pending Resolution',
    3: 'Completed'
}

# Column headers for upload template
UPLOAD_COLUMNS = [
    'Reference Type', 'Reference Value', 'QC Type', 'QC Value', 'Status'
]

STATUS_MAPPING = {'active': 1, 'inactive': 0}

AUDIT_MASTER_STATUS_TEXT_MAPPING = {
    1: 'Active',
    0: 'Inactive'
}

VALIDATION_RULES = {
    'Reference Type': lambda x: x in VALID_REFERENCE_TYPES,
    'QC Type': lambda x: x in VALID_AUDIT_TYPES,
    'QC Value': lambda x: x and float(x) > 0,
    'Status': lambda x: x.lower() in STATUS_MAPPING
}
