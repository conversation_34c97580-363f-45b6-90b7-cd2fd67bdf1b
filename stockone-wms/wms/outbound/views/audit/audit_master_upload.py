import traceback
from django.utils import timezone

# wms imports
from wms_base.wms_utils import init_logger

# outbound imports
from outbound.models import AuditMaster
from outbound.views.audit.constants import VALID_REFERENCE_TYPES, VALID_AUDIT_TYPES, UPLOAD_COLUMNS, STATUS_MAPPING, VALIDATION_RULES

log = init_logger('logs/audit_master_upload.log')

def quality_control_master_form(warehouse, extra_params=None):
    """Returns column headers for audit master upload form."""
    return UPLOAD_COLUMNS

def validate_and_convert_row(row_data, unique_entries_list, active_ref_types):
    """Validate and convert a single row of data."""
    errors = []
    
    # Extract data once
    raw_data = {col: row_data.get(col, '').strip() for col in UPLOAD_COLUMNS}
    
    # Check mandatory fields
    missing_fields = [col for col, val in raw_data.items() if (not val) and col != 'Status']
    if missing_fields:
        return [], [f'{field} is mandatory' for field in missing_fields]
    
    # Check for duplicate entries based on Reference Type, Reference Value and QC Type
    unique_key = (raw_data['Reference Type'], raw_data['Reference Value'], raw_data['QC Type'])
    if unique_key in unique_entries_list:
        return [], ['Duplicate entry for Reference Type, Reference Value and QC Type']
    unique_entries_list.add(unique_key)
    
    # Check if Reference Type is active
    if raw_data['Status'].lower() == 'active':
        if (raw_data['Reference Type'], raw_data['Reference Value']) in active_ref_types:
            return [], ['Multiple Active entries given for this Reference Type and Reference Value']
        active_ref_types.add((raw_data['Reference Type'], raw_data['Reference Value']))

    # Validate using rules
    for field, validator in VALIDATION_RULES.items():
        value = raw_data[field]
        if not validator(value):
            if field == 'Reference Type':
                errors.append(f'Invalid Reference Type. Valid values: {", ".join(VALID_REFERENCE_TYPES)}')
            elif field == 'QC Type':
                errors.append(f'Invalid QC Type. Valid values: {", ".join(VALID_AUDIT_TYPES)}')
            elif field == 'QC Value':
                errors.append('QC Value must be a positive number')
            elif field == 'Status':
                errors.append('Status must be Active or Inactive')
            
        
    if errors:
        return [], errors
    
    # Convert values
    validated_data = {
        'reference_type': raw_data['Reference Type'],
        'reference_value': raw_data['Reference Value'],
        'audit_type': raw_data['QC Type'],
        'audit_value': float(raw_data['QC Value']),
        'status': STATUS_MAPPING[raw_data['Status'].lower()]
    }
    
    return validated_data, []

def create_error_record(row_data, errors):
    """Create error record with original data and errors."""
    error_record = {col: row_data.get(col, '') for col in UPLOAD_COLUMNS}
    errors = errors or []
    error_record['Errors'] = '; '.join(errors)
    return error_record

def create_formated_error_record(row_data, errors):
    """Create error record with original data and errors."""
    UPLOAD_COLUMN_MAPPING = {
        'reference_type': 'Reference Type',
        'reference_value': 'Reference Value',
        'audit_type': 'QC Type',
        'audit_value': 'QC Value',
        'status': 'Status'
    }
    error_record = {field: row_data.get(key, '') for key, field in UPLOAD_COLUMN_MAPPING.items()}
    errors = errors or []
    error_record['Errors'] = '; '.join(errors)
    return error_record

def bulk_save_audit_configs(warehouse, validated_data_list):
    """Bulk create or update audit configurations."""
    try:
        # Get all existing configurations for the warehouse with single query
        existing_configs = {}
        reference_configs = {}
        
        existing_audit_configs = list(AuditMaster.objects.filter(
            warehouse=warehouse,
            reference_type__in=[data['reference_type'] for data in validated_data_list],
            reference_number__in=[data['reference_value'] for data in validated_data_list]
        ))
        for config in existing_audit_configs:
            # Store exact match configs for update logic
            exact_key = (config.reference_type, config.reference_number, config.audit_type)
            existing_configs[exact_key] = config
            
            # Store reference configs for validation logic
            ref_key = (config.reference_type, config.reference_number)
            if ref_key not in reference_configs:
                reference_configs[ref_key] = []
            reference_configs[ref_key].append(config)
        
        # Validate for conflicts before any database operations
        error_list, error_flag = [], False
        for validated_data in validated_data_list:
            validation_errors = []
            exact_key = (validated_data['reference_type'], validated_data['reference_value'], validated_data['audit_type'])
            ref_key = (validated_data['reference_type'], validated_data['reference_value'])
            
            # Only check conflicts if trying to create/update to active status
            if validated_data['status'] == 0:
                continue  # Skip validation for inactive status
            
            if ref_key not in reference_configs:
                continue  # No other configs to check
            
            # Updating existing config to active - check for other active configs
            existing_config = existing_configs.get(exact_key, '')
            for config in reference_configs[ref_key]:
                if config.status == 1 and ((not existing_config) or config.id != existing_config.id):
                    validation_errors.append(
                        f"Cannot activate {validated_data['audit_type']} for {validated_data['reference_type']}:{validated_data['reference_value']}. "
                        f"Another configuration ({config.audit_type}) is already active. Please deactivate it first."
                    )
                    error_flag = True
                    break

            error_list.append(create_formated_error_record(validated_data, validation_errors))

        if error_flag:
            log.info(f'Validation errors in bulk save: {error_list}')
            return error_list

        # Separate data for create and update
        configs_to_create = []
        configs_to_update = []
        
        for validated_data in validated_data_list:
            exact_key = (validated_data['reference_type'], validated_data['reference_value'], validated_data['audit_type'])
            
            if exact_key in existing_configs:
                # Update existing configuration
                config = existing_configs[exact_key]
                config.audit_value = validated_data['audit_value']
                config.status = validated_data['status']
                configs_to_update.append(config)
            else:
                # Create new configuration
                new_config = AuditMaster(
                    warehouse=warehouse,
                    reference_type=validated_data['reference_type'],
                    reference_number=validated_data['reference_value'],
                    audit_type=validated_data['audit_type'],
                    audit_value=validated_data['audit_value'],
                    status=validated_data['status'],
                    account_id=warehouse.userprofile.id
                )
                configs_to_create.append(new_config)
        
        # Perform bulk operations
        if configs_to_create:
            AuditMaster.objects.bulk_create_with_rounding(configs_to_create)
        
        if configs_to_update:
            AuditMaster.objects.bulk_update_with_rounding(configs_to_update, ['audit_value', 'status'])
        
    except Exception as e:
        log.error(f'Error in bulk save audit configs: {str(e)}')
        log.debug(traceback.format_exc())
        for validated_data in validated_data_list:
            validated_data['errors'] = ['Audit Upload Failed']
        return validated_data_list

def quality_control_master_upload(request_user, warehouse, data_dict, extra_params=None):
    """Process audit master upload data."""
    if not data_dict:
        return "Success"
    
    error_data_list, validated_data_list, errors = [], [], False
    unique_entries_list, active_ref_types = set(), set()
    
    try:
        # Validate all rows first
        for row_data in data_dict:
            validated_data, validation_errors = validate_and_convert_row(row_data, unique_entries_list, active_ref_types)
            error_data_list.append(create_error_record(row_data, validation_errors))
            if validation_errors:
                errors = True
            validated_data_list.append(validated_data)
        
        # If there are validation errors, return them
        if errors:
            return error_data_list

        # Bulk save all valid configurations
        if validated_data_list:
            errors = bulk_save_audit_configs(warehouse, validated_data_list)
            if errors:
                return errors
    
    except Exception as e:
        log.error(f'Error in audit master upload: {str(e)}')
        log.debug(traceback.format_exc())
        for audit_data in validated_data_list:
            audit_data['errors'].append('Audit Upload Failed')
        return error_data_list
    
    return "Success"
