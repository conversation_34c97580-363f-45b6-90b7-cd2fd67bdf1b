#package imports
from django.utils import timezone
from dateutil import parser
from django.http import JsonResponse
import pandas as pd
import traceback
import pytz

#outbound imports
from outbound.models import AuditDetail, SellerOrderSummary
from outbound.views.audit.constants import STATUS_TEXT_MAPPING

#lms imports
from lms.models import TaskMaster

#core operations imports
from core_operations.views.common.main import WMSListView, get_local_date_known_timezone

#reports imports
from reports.views import CustomEndpointReportMixin

#wms_base imports
from wms_base.wms_utils import init_logger
from wms.settings.base import reports_database

log = init_logger('logs/audit_report.log')

class AuditReport(WMSListView, CustomEndpointReportMixin):
    """
    This class is used to fetch audit report data.
    """

    def get(self, *args, **kwargs):
        """
        Handles GET requests to generate the audit report.
        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        Returns:
            JsonResponse: A JSON response indicating the status of the report generation
        """
        
        self.set_user_credientials()
        request_data = self.request.GET.dict()
        self.timezone = self.request.timezone or self.warehouse.userprofile.timezone

        try:
            error_message = self.validate_and_process_report(request_data)
            if error_message:
                return JsonResponse({"message": error_message}, status=400)
            # Prepare the report data
            self.get_audit_report_data(request_data)
        except Exception as e:
            log.error(e)
            log.debug(traceback.format_exc())
            self.save_file_path('', 'Failed', self.generated_report)
            return JsonResponse({"status": "Report Generation Failed"}, status= 400)

        return JsonResponse({"status": "Success"}, status=200)

    def get_audit_report_data(self, request_data):
        """
        Fetches the audit report data.

        Returns:
            None
        """
        self.prepare_filters(request_data)

        self.report_df=pd.DataFrame(columns=[
            'Warehouse', 'Audit Date/Time', 'LPN Number', 'Order Reference', 'Picklist No', 'Order Type',	'Customer Reference', 'Customer name', 
            'Route name', 'SKU Code', 'SKU Description', 'Batch No', 'Expiry date', 'Picked Qty', 'Picklist Confirmation Time', 
            'Picked user', 'Location', 'SubZone', 'Zone', 'Audit Qty', 'Audit Remark/Reason', 'Audit Confirmation Time', 
            'Audit User', 'Resolution Time', 'Resolved User', 'Audit Status'
        ])
        self.fetch_required_data()
        self.prepare_final_data()
        self.post_report_generation(self.report_df)
    
    def prepare_filters(self, request_data):
        """
        Prepares the filters for the report.
        """
        self.filters = {'audit_entry__warehouse_id__in': self.warehouses_data}
        if request_data.get('from_date'):
            self.filters['creation_date__date__gte'] = timezone.make_aware(parser.parse(request_data['from_date']), pytz.timezone(self.timezone))
        if request_data.get('to_date'):
            self.filters['creation_date__date__lte'] = timezone.make_aware(parser.parse(request_data['to_date']), pytz.timezone(self.timezone))
        if request_data.get('lpn_number'):
            self.filters['audit_entry__audit_reference'] = request_data['lpn_number']
        if request_data.get('picklist_number'):
            self.filters['audit_entry__reference_number'] = request_data['picklist_number']
        
        self.picklist_filters = {'picklist__user_id__in': self.warehouses_data}
        if request_data.get('zone'):
            self.picklist_filters['picklist__location__zone__zone'] = request_data['zone']
        if request_data.get('sub_zone'):
            self.picklist_filters['picklist__location__sub_zone__zone'] = request_data['sub_zone']
        if request_data.get('order_type'):
            self.picklist_filters['order__order_type'] = request_data['order__order_type']
        if request_data.get('customer_reference'):
            self.picklist_filters['order__customer_identifier__customer_reference'] = request_data['customer_reference']
    
    def fetch_required_data(self):
        """
        Fetches the Audit Data
        """
        audit_detail_data = list(AuditDetail.objects.using(reports_database).filter(**self.filters).values(
            'audit_entry__warehouse__username', 'audit_entry__audit_reference', 'audit_entry__status', 'audit_entry__audit_date', 
            'audit_entry__auditor__username', 'audit_entry__resolved_by__username', 'batch_number', 'audit_entry__creation_date', 
            'audit_entry__updation_date', 'sku_code', 'reason', 'audit_quantity', 'stock__receipt_number', 
            'stock__batch_detail__expiry_date', 'stock__sku__sku_desc', 'picked_quantity').order_by('-id'))
        sos_ids = set( audit_data['stock__receipt_number'] for audit_data in audit_detail_data)
        
        picklist_data = list(SellerOrderSummary.objects.filter(id__in=sos_ids, **self.picklist_filters).values('id', 'picklist__picklist_number',
            'order__order_reference', 'order__order_type', 'order__customer_identifier__customer_reference', 
            'order__customer_identifier__name', 'order__customer_identifier__route__name', 
            'picklist__stock__location__location', 'picklist__stock__location__sub_zone__zone', 
            'picklist__stock__location__zone__zone', 'picklist_id', 'creation_date'))
        
        picklist_data_dict, picklist_ids = {}, set()
        for pick_data in picklist_data:
            picklist_data_dict[pick_data['id']] = pick_data
            picklist_ids.add(pick_data['picklist_id'])

        picker_data = dict(TaskMaster.objects.filter(status=True, task_ref_id__in=picklist_ids).values_list('task_ref_id', 'employee__user__username'))
        
        self.audit_details = {}
        for audit_data in audit_detail_data:
            unique_key = (audit_data['audit_entry__warehouse__username'], audit_data['audit_entry__audit_reference'], audit_data['sku_code'], audit_data['batch_number'])
            pick_data = picklist_data_dict.get(audit_data['stock__receipt_number'], {})
            picker_name = picker_data.get(str(pick_data['picklist_id']), '')
            if not pick_data:
                continue
            if unique_key not in self.audit_details:
                picklist_confirmation_date = get_local_date_known_timezone(self.timezone, pick_data.get('creation_date'), send_date=True).strftime('%Y-%m-%d %H:%M:%S')
                pick_data['picklist_confirmation_date'] = picklist_confirmation_date
                pick_data['picker_name'] = picker_name
                audit_data = self.format_date_fields(audit_data)
                audit_data.update(pick_data)
                self.audit_details[unique_key] = audit_data
            else:
                self.audit_details[unique_key]['picked_quantity'] += audit_data['picked_quantity']
                self.audit_details[unique_key]['audit_quantity'] += audit_data['audit_quantity']

    def format_date_fields(self, audit_data):
        """
        Formats the date fields in the audit data.
        """
        date_fields_mapping = {
            'stock__batch_detail__expiry_date': 'expiry_date',
            'audit_entry__creation_date': 'audit_date',
            'audit_entry__audit_date': 'audit_confirmation_date',
            'audit_entry__updation_date': 'resolution_date'
        }
        for date_field, new_field in date_fields_mapping.items():
            date_value = audit_data.get(date_field) or ''
            if date_value:
                date_value = get_local_date_known_timezone(self.timezone, date_value, send_date=True)
                if new_field == 'expiry_date':
                    date_value = date_value.strftime('%Y-%m-%d')
                else:
                    date_value = date_value.strftime('%Y-%m-%d %H:%M:%S')
            audit_data[new_field] = date_value
        return audit_data

    def prepare_final_data(self):
        """
        Formats the data in the required format.
        """
        final_data_list = []
        for unique_key, audit_data in self.audit_details.items():
            audit_status = STATUS_TEXT_MAPPING.get(audit_data['audit_entry__status'], '')
            final_data = {
                'Warehouse': unique_key[0],
                'Audit Date/Time': audit_data['audit_date'],
                'LPN Number' : audit_data['audit_entry__audit_reference'],
                'Order Reference': audit_data['order__order_reference'],
                'Picklist No': audit_data['picklist__picklist_number'],
                'Order Type': audit_data['order__order_type'],
                'Customer Reference': audit_data['order__customer_identifier__customer_reference'],
                'Customer name': audit_data['order__customer_identifier__name'],
                'Route name': audit_data['order__customer_identifier__route__name'],
                'SKU Code': audit_data['sku_code'],
                'SKU Description': audit_data['stock__sku__sku_desc'],
                'Batch No': audit_data['batch_number'],
                'Expiry date': audit_data['expiry_date'],
                'Picked Qty': audit_data['picked_quantity'],
                'Picklist Confirmation Time': audit_data['picklist_confirmation_date'],
                'Picked user': audit_data['picker_name'],
                'Location': audit_data['picklist__stock__location__location'],
                'SubZone': audit_data['picklist__stock__location__sub_zone__zone'],
                'Zone': audit_data['picklist__stock__location__zone__zone'],
                'Audit Qty': audit_data['audit_quantity'],
                'Audit Remark/Reason': audit_data['reason'],
                'Audit Confirmation Time': audit_data['audit_confirmation_date'],
                'Audit User': audit_data['audit_entry__auditor__username'],
                'Resolution Time': audit_data['resolution_date'],
                'Resolved User': audit_data['audit_entry__resolved_by__username'],
                'Audit Status': audit_status
            }
            final_data_list.append(final_data)        
        
        
        self.report_df = pd.DataFrame.from_records(final_data_list)
        if not self.report_df.empty:
            self.report_df = self.report_df.sort_values(by=['Audit Date/Time', 'Warehouse'])
        
    