# Generated by Django 4.2.20 on 2025-05-02 04:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('outbound', '0073_remove_salesreturnbatchlevel_seller_order_summary'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='historicalpicklist',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='pickandpassstrategy',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='picklist',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='salesreturn',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='salesreturnbatchlevel',
            name='json_data',
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='salesreturnlinelevel',
            name='json_data',
            field=models.J<PERSON>NField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='stockallocation',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
    ]
