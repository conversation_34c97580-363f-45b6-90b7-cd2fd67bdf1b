# Generated by Django 4.2.20 on 2025-05-26 07:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('outbound', '0078_auditentry_auditmaster_auditdetail'),
    ]

    operations = [
        migrations.AlterIndexTogether(
            name='sellerordersummary',
            index_together={('full_invoice_number', 'order'), ('full_invoice_number',), ('order', 'order_status_flag'), ('order',), ('picklist',), ('order', 'picklist')},
        ),
        migrations.AddIndex(
            model_name='invoicedlpns',
            index=models.Index(fields=['invoice_item', 'lpn_number'], name='INVOICED_LP_invoice_3ddc77_idx'),
        ),
        migrations.AddIndex(
            model_name='invoiceitems',
            index=models.Index(fields=['invoice', 'warehouse'], name='INVOICE_ITE_invoice_8083a0_idx'),
        ),
        migrations.AddIndex(
            model_name='sellerordersummary',
            index=models.Index(fields=['invoice_reference', 'order', 'order_status_flag'], name='SELLER_ORDE_invoice_40df28_idx'),
        ),
    ]
