# Generated by Django 4.2.20 on 2025-05-13 09:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0041_rename_userprofile_warehouse_level_idx_user_profil_warehou_9a28d0_idx_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0061_rename_cyclecount_cycle_cycle_count_cycle_debb59_idx_and_more'),
        ('outbound', '0077_customermaster_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditEntry',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('audit_reference', models.CharField(blank=True, max_length=128, null=True)),
                ('reference_number', models.Char<PERSON>ield(blank=True, max_length=128, null=True)),
                ('reference_type', models.Char<PERSON><PERSON>(default='LPN', max_length=64)),
                ('status', models.PositiveIntegerField(choices=[(1, 'Open for Audit'), (2, 'Failed Audit, Pending Resolution'), (3, 'Completed')], default=1)),
                ('audit_date', models.DateTimeField(blank=True, null=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('auditor', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='auditor', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('resolved_by', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='resolved_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'AUDIT_ENTRY',
                'index_together': {('reference_number', 'reference_type', 'status', 'warehouse')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='AuditMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('reference_type', models.CharField(default='', max_length=64)),
                ('reference_number', models.CharField(default='', max_length=64)),
                ('audit_type', models.CharField(choices=[('FIXED_COUNT', 'Fixed Count'), ('SAMPLING', 'Sampling Percentage')], default='FIXED_COUNT', max_length=32)),
                ('audit_value', models.FloatField(default=0)),
                ('current_counter', models.IntegerField(default=0)),
                ('total_count', models.IntegerField(default=0)),
                ('counter_reset_days', models.IntegerField(default=1)),
                ('last_reset_date', models.DateTimeField(auto_now_add=True)),
                ('status', models.BooleanField(default=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'AUDIT_MASTER',
                'unique_together': {('warehouse', 'reference_type', 'reference_number')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='AuditDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('sku_code', models.CharField(blank=True, max_length=128, null=True)),
                ('batch_number', models.CharField(blank=True, max_length=128, null=True)),
                ('picked_quantity', models.FloatField()),
                ('audit_quantity', models.FloatField()),
                ('reason', models.CharField(blank=True, max_length=256, null=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('audit_entry', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='outbound.auditentry')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('stock', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.stockdetail')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'AUDIT_DETAIL',
                'index_together': {('audit_entry', 'stock')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
