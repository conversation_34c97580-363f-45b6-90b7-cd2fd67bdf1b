# Generated by Django 4.2.16 on 2025-03-25 04:36

from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0054_alter_serialnumbertransaction_batch_number'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('outbound', '0065_ordertypezonemapping_order_type_priority'),
    ]

    operations = [
        migrations.AlterIndexTogether(
            name='stockallocation',
            index_together={('stock', 'warehouse', 'status'), ('reference_model', 'reference_id', 'status'), ('warehouse', 'reference_model', 'reference_number', 'status')},
        ),
    ]
