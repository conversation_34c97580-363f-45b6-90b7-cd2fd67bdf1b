# Generated by Django 4.2.20 on 2025-05-26 11:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('outbound', '0079_alter_sellerordersummary_index_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='allocated_quantity',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='order',
            name='cancelled_quantity',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='order',
            name='invoiced_quantity',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='order',
            name='open_quantity',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='order',
            name='quantity',
            field=models.FloatField(default=0),
        ),
    ]
