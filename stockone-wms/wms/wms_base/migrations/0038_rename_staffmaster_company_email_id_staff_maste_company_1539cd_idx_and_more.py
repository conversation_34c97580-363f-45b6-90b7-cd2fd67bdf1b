# Generated by Django 4.2.20 on 2025-05-07 05:21

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0037_companymaster_company_code_and_more'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='staffmaster',
            new_name='STAFF_MASTE_company_1539cd_idx',
            old_fields=('company', 'email_id'),
        ),
        migrations.RenameIndex(
            model_name='staffmaster',
            new_name='STAFF_MASTE_email_i_c945eb_idx',
            old_fields=('email_id',),
        ),
        migrations.RenameIndex(
            model_name='staffmaster',
            new_name='STAFF_MASTE_company_52ed5d_idx',
            old_fields=('company', 'email_id', 'user'),
        ),
        migrations.RenameIndex(
            model_name='staffwarehousemapping',
            new_name='STAFF_WAREH_staff_i_849e40_idx',
            old_fields=('staff', 'status', 'warehouse'),
        ),
        migrations.RenameIndex(
            model_name='usergroups',
            new_name='USER_GROUPS_user_id_d09ac3_idx',
            old_fields=('user', 'company'),
        ),
        migrations.RenameIndex(
            model_name='usergroups',
            new_name='USER_GROUPS_user_id_cff2b2_idx',
            old_fields=('user', 'admin_user'),
        ),
        migrations.RenameIndex(
            model_name='userprofile',
            new_name='USER_PROFIL_user_id_c0f5e0_idx',
            old_fields=('user',),
        ),
        migrations.RenameIndex(
            model_name='userrolemapping',
            new_name='USER_ROLE_M_user_id_49a0d4_idx',
            old_fields=('user', 'role_id', 'role_type'),
        ),
    ]
