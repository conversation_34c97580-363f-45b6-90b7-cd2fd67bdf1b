# Generated by Django 4.2.20 on 2025-05-15 07:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0041_rename_userprofile_warehouse_level_idx_user_profil_warehou_9a28d0_idx_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScriptUsers',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text="Unique name for this user set (e.g., 'Company A Team')", max_length=100, unique=True)),
                ('companies', models.ManyToManyField(blank=True, help_text='Companies this script applies to (leave empty for all)', to='wms_base.companymaster')),
                ('subusers', models.ManyToManyField(blank=True, help_text='Sub-users this script applies to (leave empty for all)', related_name='script_subusers', to=settings.AUTH_USER_MODEL)),
                ('warehouses', models.ManyToManyField(blank=True, help_text='Warehouse users this script applies to (leave empty for all)', related_name='script_warehouses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'SCRIPT_USERS',
            },
        ),
        migrations.CreateModel(
            name='ScriptModel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=256)),
                ('script_type', models.CharField(choices=[('pre', 'pre-Script'), ('post', 'Post-Script')], max_length=4)),
                ('target_name', models.CharField(help_text="Format: 'app.module.function' or 'app.module.ClassName'", max_length=256)),
                ('target_type', models.CharField(choices=[('function', 'Function'), ('method', 'Class Method')], help_text='Specify if target is a function or class method', max_length=10)),
                ('api_name', models.CharField(help_text='API name for the script', max_length=256)),
                ('http_method', models.CharField(choices=[('GET', 'GET'), ('POST', 'POST'), ('PUT', 'PUT'), ('PATCH', 'PATCH')], max_length=10)),
                ('code', models.TextField(help_text="Must Contain a 'run(context)' function")),
                ('skip_original_function', models.BooleanField(default=False, help_text='If True, skips the original function and uses script result only')),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('script_users', models.ForeignKey(help_text='Named user set this script applies to', on_delete=django.db.models.deletion.CASCADE, to='wms_base.scriptusers')),
                ('updated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'SCRIPT_MODEL',
                'unique_together': {('target_name', 'script_type', 'name', 'api_name', 'http_method')},
            },
        ),
    ]
