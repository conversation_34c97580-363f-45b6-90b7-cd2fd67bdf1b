from types import ModuleType
from django.core.exceptions import ValidationError
import re

class ScriptValidator:
    # Validation categories as class attributes
    DANGEROUS_SQL = [
        r'\bupdate\b.*\bset\b',  # SQL UPDATE ... SET
        r'\bdelete\b.*\bfrom\b',  # SQL DELETE FROM
        r'\bdrop\b',             # SQL DROP
        r'\btruncate\b',         # SQL TRUNCATE
        r'\binsert\b.*\binto\b', # SQL INSERT INTO
        r'\balter\b',            # SQL ALTER
    ]

    DANGEROUS_PYTHON = [
        r'\bexec\b\s*\(',        # exec() calls
        r'\beval\b\s*\(',        # eval() calls
        r'\bos\.\b',             # os module
        r'\bsys\.\b',            # sys module
        r'\bshutil\.\b',         # shutil module
        r'\bsubprocess\.\b',     # subprocess module
        r'\bopen\s*\(',          # File operations
        r'\bdel\s+',             # Deleting variables/objects
        r'\bimport\s+os\b',      # Importing os
        r'\bimport\s+sys\b',     # Importing sys
        r'\bimport\s+subprocess\b',  # Importing subprocess
        r'\b__import__\s*\(',    # Dynamic imports
        r'\bsocket\.\b',         # Network operations
        r'\bthreading\.\b',      # Thread manipulation
        r'\bmultiprocessing\.\b',  # Process spawning
        r'\bctypes\.\b',         # Low-level memory access
        r'\bgetattr\s*\(',       # Dynamic attribute access
        r'\bsetattr\s*\(',       # Dynamic attribute setting
        r'\bexecfile\s*\(',      # Older exec variant
        r'\bcompile\s*\(',       # Code compilation
    ]

    DANGEROUS_OS_COMMANDS = [
        r'\brm\s+-',             # Unix rm
        r'\bdel\s+',             # Windows del
        r'\brmdir\b',            # Remove directory
        r'\bshutil\.rmtree\b',   # Recursive directory removal
        r'\bkill\b',             # Process killing
        r'\bchmod\b',            # Change permissions
        r'\bchown\b',            # Change ownership
        r'\bsudo\b',             # Superuser commands
        r'\bshutdown\b',         # System shutdown
        r'\breboot\b',           # System reboot
        r'\bformat\b',           # Disk formatting
        r'\bmkfs\b',             # Make filesystem
        r'\bcommand\s*\(',       # Older cmd execution
        r'\bpopen\s*\(',         # Pipe-based command execution
    ]

    DANGEROUS_ORM = [
        r'\bdelete\s*\(\s*\)',   # Model.delete()
        r'\bupdate\s*\(',        # QuerySet.update()
        r'\bsave\s*\(',          # Model.save()
        r'\bcreate\s*\(',        # Model.objects.create()
        r'\bbulk_create\s*\(',   # Bulk creation
        r'\bbulk_update\s*\(',   # Bulk updates
        r'\braw\s*\(',           # Raw SQL queries
    ]

    DANGEROUS_EXTRAS = [
        r'\bglobals\s*\(\s*\)',  # Access to globals()
        r'\blocals\s*\(\s*\)',   # Access to locals()
        r'\bvars\s*\(\s*\)',    # Access to object vars
        r'\bimportlib\.\b',      # Dynamic module loading
        r'\bmarshal\.\b',        # Binary serialization
        r'\bpickle\.\b',         # Serialization
        r'\bjson\.\b.*\bloads\b',  # JSON deserialization
        r'\bexit\s*\(',          # Program termination
        r'\bquit\s*\(',          # Program termination
        r'\bbreakpoint\s*\(',    # Debugger access
        r'\binput\s*\(',         # Interactive input
        r'\bprint\s*\(',         # Debug output (optional)
        r'\bsecrets\.\b',        # Access to secrets
        r'\bcryptography\.\b',   # Crypto operations
        r'\bhashlib\.\b',        # Hashing
    ]

    @classmethod
    def validate_run_function(cls, code: str):
        """Validate that code contains a 'run(context)' function"""
        try:
            module = ModuleType("test_script")
            exec(code, module.__dict__)
            if not hasattr(module, 'run') or not callable(module.run):
                raise ValidationError("Code must define a 'run(context)' function")
        except SyntaxError as e:
            raise ValidationError(f"Invalid Python syntax: {str(e)}")
        except Exception as e:
            raise ValidationError(f"Code compilation failed: {str(e)}")

    @classmethod
    def validate_sql(cls, code: str):
        """Validate against dangerous SQL operations"""
        code_lower = code.lower()
        for pattern in cls.DANGEROUS_SQL:
            if re.search(pattern, code_lower):
                raise ValidationError(f"Code contains prohibited SQL operation: {pattern}")

    @classmethod
    def validate_python(cls, code: str):
        """Validate against dangerous Python operations"""
        code_lower = code.lower()
        for pattern in cls.DANGEROUS_PYTHON:
            if re.search(pattern, code_lower):
                raise ValidationError(f"Code contains prohibited Python operation: {pattern}")

    @classmethod
    def validate_os_commands(cls, code: str):
        """Validate against dangerous OS commands"""
        code_lower = code.lower()
        for pattern in cls.DANGEROUS_OS_COMMANDS:
            if re.search(pattern, code_lower):
                raise ValidationError(f"Code contains prohibited OS command: {pattern}")

    @classmethod
    def validate_orm(cls, code: str):
        """Validate against dangerous ORM operations"""
        code_lower = code.lower()
        for pattern in cls.DANGEROUS_ORM:
            if re.search(pattern, code_lower):
                raise ValidationError(f"Code contains prohibited ORM operation: {pattern}")

    @classmethod
    def validate_extras(cls, code: str):
        """Validate against additional dangerous operations"""
        code_lower = code.lower()
        for pattern in cls.DANGEROUS_EXTRAS:
            if re.search(pattern, code_lower):
                raise ValidationError(f"Code contains prohibited operation: {pattern}")

    @classmethod
    def validate_code(cls, code: str):
        """Comprehensive validation calling all individual validators"""
        cls.validate_run_function(code)
        cls.validate_sql(code)
        cls.validate_python(code)
        cls.validate_os_commands(code)
        cls.validate_orm(code)
        cls.validate_extras(code)