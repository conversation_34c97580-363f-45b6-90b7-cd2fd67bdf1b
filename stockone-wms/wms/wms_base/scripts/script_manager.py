from types import ModuleType
from typing import Callable, Dict, Any
import threading

from wms_base.models import ScriptModel
import importlib
import functools

from wms_base.wms_utils import init_logger
log = init_logger("logs/script_manager.log")

from wms_base.middleware.uuid_grenerate import request_id_local

class ScriptManager:

    _local = threading.local() #Thread-local storage for active scripts

    @staticmethod
    def get_scripts(target_name: str, script_type:str, api_name:str, http_method: str) -> Dict[str, ModuleType]:

        filters = {
            'target_name': target_name,
            'script_type': script_type,
            'is_active': True,
            'api_name': api_name,
            'http_method': http_method.upper(),
        }

        scripts = ScriptModel.objects.filter(**filters)

        script_dict = {}
        for script in scripts:
            module= ModuleType(f"{script_type}_{script.name}")
            try:
                exec(script.code, module.__dict__)
                script_dict[script.name] = module
            except Exception as e:
                log.error(f"Failed to compile script {script.name}: {str(e)}")

        return script_dict

    @staticmethod
    def execute_scripts(scripts: Dict[str, ModuleType], context: Dict) -> Dict:
        for name, module in scripts.items():
            try:
                if hasattr(module, 'run'):
                    context = module.run(context) or context
            except Exception as e:
                log.error(f"Script {name} failed to execute: {str(e)}")
        return context

    @classmethod
    def wrap_function(cls, func: Callable, target_name: str, api_name: str, http_method: str, skip_original:bool) -> Callable:
        """
        Wraps the function with script execution logic.
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log.info(f"Wrapping {target_name} - entering wrapper")
            context = {
                'args': args,
                'kwargs': kwargs,
                'result': None,
            }

            script_applicability = getattr(request_id_local, '_script_applicability', {})
            applies = script_applicability.get(target_name, False)

            if not applies:
                log.info(f"Skipping scripts for {target_name} - not applicable to request context")
                return func(*args, **kwargs)

            pre_scripts = cls.get_scripts(target_name, 'pre', api_name, http_method)
            context = cls.execute_scripts(pre_scripts, context)

            args = context.get('args', args)
            kwargs = context.get('kwargs', kwargs)

            if not skip_original:
                context['result'] = func(*args, **kwargs)
                log.info(f"Executed original {target_name} with result: {context['result']}")
            else:
                log.info(f"Skipping original {target_name} - not executing")

            post_scripts = cls.get_scripts(target_name, 'post', api_name, http_method)
            context = cls.execute_scripts(post_scripts, context)
            log.info(f"Wrapping {target_name} - exiting wrapper with result: {context['result']}")

            return context['result']
        return wrapper
