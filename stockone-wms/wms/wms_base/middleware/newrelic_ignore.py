# myproject/middleware/newrelic_ignore.py
import newrelic.agent
from django.core.cache import cache

from wms_base.wms_utils import init_logger
log = init_logger("logs/newrelic.log")

class NewRelicIgnoreMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

        check = cache.get("infra_config:NEW_RELIC_IGNORED_PATHS", "")
        if check:
            self.excluded_paths = check.split(",")
        else:
            self.excluded_paths = []

    def __call__(self, request):
        try:
            if request.url_name in self.excluded_paths:
                newrelic.agent.ignore_transaction()
        except Exception:
            import traceback
            log.debug(f"Error in NewRelicIgnoreMiddleware: {traceback.format_exc()}")
        return self.get_response(request)
