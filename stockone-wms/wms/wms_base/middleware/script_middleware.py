# wms_base/middleware.py
from django.urls import resolve
from wms_base.scripts.script_manager import ScriptManager
from wms_base.models import ScriptModel, ScriptUsers
import importlib
from .uuid_grenerate import request_id_local

from wms_base.wms_utils import init_logger
log = init_logger("logs/script_middleware.log")

def strict_context_check(company_name, warehouse_name, username):
    """
    Strict pre-check to determine if script processing should proceed.
    Returns False if any of company, warehouse, or user is None, skipping further processing.
    """
    check = all([company_name, warehouse_name, username])
    return check

def should_apply_script(script_users_id, company_name, warehouse_name, username):
    """
    Determines if a script should apply based on granular company, warehouse, and user context.
    Uses a single query to fetch ScriptUsers data and checks against provided IDs.
    Assumes company, warehouse, and user are non-None (validated by strict_context_check).
    Returns True if the script should be applied, False otherwise.
    """
    # Fetch ScriptUsers with related data in one query
    script_users = ScriptUsers.objects.get(id=script_users_id)

    # Extract IDs from related fields
    company_names = set(script_users.cached_companies)
    warehouse_names = set(script_users.cached_warehouses)
    subuser_names = set(script_users.cached_subusers)

    if company_name in company_names:
        if warehouse_names and warehouse_name not in warehouse_names:
            return False
        if subuser_names and username not in subuser_names:
            return False
        return True
    return False

class ScriptMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Resolve the URL to get the API name and view function
        resolver_match = resolve(request.path_info)
        api_name = resolver_match.url_name
        http_method = request.method.upper()

        company_name = getattr(request, 'company_name', None)
        warehouse_name = getattr(request, 'warehouse_name', None)
        username = getattr(request, 'user_name', None)

        # Strict context check before querying ScriptModel
        if not strict_context_check(company_name, warehouse_name, username):
            return self.get_response(request)

        # Fetch all target functions with scripts for this API
        target_scripts = ScriptModel.objects.filter(
            api_name=api_name,
            http_method=http_method
        ).values('target_name', 'skip_original_function', 'target_type', 'script_users_id', 'is_active').distinct()

        log.info(f"Found scripts for targets: {[(t['target_name'], t['skip_original_function']) for t in target_scripts]}")

        script_applicability = {}
        if target_scripts:

            # Apply scripts to all target functions
            for script in target_scripts:
                target_name = script['target_name']
                skip_original = script['skip_original_function']
                target_type = script['target_type']
                script_users_id = script['script_users_id']
                is_active = script['is_active']

                # Check applicability only for active scripts
                applies = is_active and should_apply_script(script_users_id, company_name, warehouse_name, username)
                script_applicability[target_name] = applies

                try:
                    parts = target_name.split('.')

                    if target_type == 'function':
                        module_name = '.'.join(parts[:-1])
                        func_name = parts[-1]
                        module = importlib.import_module(module_name)
                        original_func = getattr(module, func_name)
                        if callable(original_func):
                            if is_active and not hasattr(original_func, '_wrapped_by_script_manager'):
                                wrapped_func = ScriptManager.wrap_function(original_func, target_name, api_name, http_method, skip_original)
                                wrapped_func._wrapped_by_script_manager = True
                                setattr(module, func_name, wrapped_func)
                                log.info(f"Patched function {target_name} in module {module_name}")
                            elif not is_active and hasattr(original_func, '_wrapped_by_script_manager'):
                                wrapped_original_func = original_func.__wrapped__
                                setattr(module, func_name, wrapped_original_func)
                                log.info(f"Restored function {target_name} due to inactivity")
                        else:
                            log.warning(f"{target_name} is not callable, skipping")
                    elif target_type == 'method':
                        module_name = '.'.join(parts[:-2])
                        class_name = parts[-2]
                        method_name = parts[-1]
                        module = importlib.import_module(module_name)
                        cls = getattr(module, class_name)
                        original_func = getattr(cls, method_name)
                        if callable(original_func):
                            if is_active and not hasattr(original_func, '_wrapped_by_script_manager'):
                                wrapped_func = ScriptManager.wrap_function(original_func, target_name, api_name, http_method, skip_original)
                                setattr(cls, method_name, wrapped_func)
                                log.info(f"Patched method {target_name} in class {class_name}")
                            elif not is_active and hasattr(original_func, '_wrapped_by_script_manager'):
                                wrapped_original_func = original_func.__wrapped__
                                setattr(cls, method_name, wrapped_original_func)
                                log.info(f"Restored method {target_name} due to inactivity")
                        else:
                            log.warning(f"{target_name} is not callable, skipping")

                except (ImportError, AttributeError) as e:
                    log.error(f"Failed to patch {target_name}: {str(e)}")


        # Execute the request with patched functions
        request_id_local._script_applicability = script_applicability
        response = self.get_response(request)
        return response