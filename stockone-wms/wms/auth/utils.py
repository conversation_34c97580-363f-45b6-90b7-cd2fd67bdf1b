import copy
import datetime
import operator
import json

import pytz

from core.models import MiscDetail, UserAttributes
from wms_base.models import (
    CompanyLevelConfigs, CompanyMaster, User, UserGroups, UserProfile, SSOIntegration, StaffWarehouseMapping, AdminGroups,
    Integrations, StaffMaster
)

from wms_base.wms_utils import (
    PERMISSION_DICT, COMPANY_LOGO_PATHS, PERMISSION_IGNORE_LIST, 
    TOP_COMPANY_LOGO_PATHS, init_logger
)

from django.contrib.auth.models import Permission
from django.conf import settings
USE_CACHE_OPS = getattr(settings, 'USE_CACHE_OPS', False)

LABEL_KEYS = ["INVENTORY_LABEL", "INBOUND_LABEL", "PRODUCTION_LABEL", "OUTBOUND_LABEL", "MASTERS_LABEL", "EXTRAS_LABEL" , "UPLOADS_LABEL", "REPORTS_LABEL", "DASHBOARD"]


log = init_logger('logs/auth_utils.log')

def get_utc_start_date(date_obj):
    # Getting Time zone aware start time

    ist_unaware = datetime.datetime.strptime(str(date_obj.date()), '%Y-%m-%d')
    ist_aware = pytz.timezone("Asia/Calcutta").localize(ist_unaware)
    converted_date = ist_aware.astimezone(pytz.UTC)
    return converted_date

def get_admin(user):
    is_admin_exists = UserGroups.objects.filter(user=user)
    if is_admin_exists:
        admin_user = is_admin_exists[0].admin_user
    else:
        admin_user = user
    return admin_user

def get_warehouse_type_name(user_profile):
    warehouse_type_name = 'Warehouse'
    if user_profile.warehouse_type in ['STORE', 'SUB_STORE']:
        warehouse_type_name = 'Department'
    elif user_profile.warehouse_type == 'ST_HUB':
        warehouse_type_name = 'Plant'
    return warehouse_type_name

def get_user_roles_names(request,user):
    roles_list=[]
    all_roles = request.user.roles.all()
    if all_roles.exists():
        roles_list = list(all_roles.values_list('name',flat=True))
    return roles_list

def get_label_permissions(role_perms):
    label_keys = copy.deepcopy(LABEL_KEYS)
    result_dict = {}
    try:
        with open('wms_base/demo_data/permissions.json', 'r') as json_file:
            sub_label_keys = json.load(json_file)

        result_dict = {sub_label_keys[key]['label']: list(sub_label_keys[key].values())[1:] for key in sub_label_keys}
    except:
        pass

    labels = {}
    for label in label_keys:
        label_perms = result_dict.get(label, [])
        if set(label_perms).intersection(role_perms):
            labels[label] = True
        else:
            labels[label] = False

    return labels

def get_multi_permission(codenames: set, user_perms_list: set=None):

    # Initialize all permissions to False
    roles = {codename: False for codename in codenames}

    # Find common permissions (intersection of codenames and user_perms_list)
    common_perms = codenames.intersection(user_perms_list)

    # Update only common permissions to True
    for codename in common_perms:
        roles[codename] = True

    # List of permissions that are in both
    label_perms = list(common_perms)

    return roles, label_perms

def get_user_permissions(request, user):
    roles = {}
    label_perms = []

    # Fetch the configuration in a more efficient way
    configuration = list(MiscDetail.objects.filter(user=user.id).values('misc_type', 'misc_value'))
    config = {item['misc_type']: item['misc_value'] for item in configuration}

    # Ensure 'order_headers' is present in the config
    if 'order_headers' not in config:
        config['order_headers'] = ''

    # Fetch all permission codenames and user-specific permissions in a single query
    permissions_list = set(Permission.objects.values_list('codename', flat=True))

    # Fetch user's role permissions
    all_roles = request.user.roles.all()
    role_ids = all_roles.values_list('id', flat=True)

    user_perms_list = set(Permission.objects.filter(role__id__in=role_ids).\
                                        values_list('codename', flat=True).distinct())


    roles , label_perms = get_multi_permission(permissions_list, user_perms_list=user_perms_list)
    roles.update(config)
    return {'permissions': roles, 'label_perms': label_perms}

def check_password_expiry(user: User):
    """
        Check if the user's password has expired based on the latest password record.

        Parameters:
            user (User): The User object representing the user for whom to check the password expiry.

        Returns:
            bool: True if the password is expired, False otherwise.
    """
    PASS_EXPIRY_DAYS = getattr(settings, "PASS_EXPIRY_DAYS", 30)

    # check for company level config
    company_level_config = CompanyLevelConfigs.objects.filter(config_key="PASS_EXPIRY_DAYS", company=user.current_warehouse.userprofile.company)
    if company_level_config.exists():
        PASS_EXPIRY_DAYS = int(company_level_config[0].config_value)

    is_expired = False
    remaining_days = int(PASS_EXPIRY_DAYS)
    try:
        latest_record = user.password_records.latest()
        password_days = get_utc_start_date(datetime.datetime.now()) - get_utc_start_date(latest_record.date)
        if password_days.days > int(PASS_EXPIRY_DAYS):
            is_expired = True
        remaining_days = int(PASS_EXPIRY_DAYS) - password_days.days
    except Exception as e:
        log.info("check password exipry error %s with PASS_EXPIRY_DAYS %s", str(e), PASS_EXPIRY_DAYS)
        is_expired = False
    is_expired = False if user.userprofile.integrations_user else is_expired
    return is_expired, remaining_days

def get_user_type(profile_warehouse_type: str, profile_user_type: str) -> str:
    """
        Get the type of the provided user based on their warehouse profile.

        Parameters:
            profile_warehouse_type (str): The user warehouse_type for whom the type is to be determined.
            profile_user_type (str): The user user_type for whom the type is to be determined.

        Returns:
            str: The type of the user, which can be one of the following:
    """
    user_type = ''

    if profile_warehouse_type in ['ADMIN', 'ADIMN']:
        user_type = 'company'
    elif profile_warehouse_type == 'ST_HUB':
        user_type = 'subsidiary'
    elif profile_warehouse_type == 'STORE':
        user_type = 'zone'
    elif profile_warehouse_type == 'DEPT' and profile_user_type == "warehouse_user" :
        user_type = 'warehouse'
    elif profile_user_type == "sub_user":
        user_type = 'subuser'
    return user_type


def get_user_warehouses(user: User) -> dict:
    """
        Get a list of warehouse usernames associated with the provided user.

        Parameters:
            user (User): The user object for whom the associated warehouses are to be retrieved.

        Returns:
            list: A list of warehouse usernames that are associated with the given user.
    """
    profile_warehouse_type = user.userprofile.warehouse_type
    profile_user_type = user.userprofile.user_type
    user_type = get_user_type(profile_warehouse_type, profile_user_type)

    filter_params = {
        "user__userprofile__warehouse_type": 'DEPT'
    }

    warehouses_dict = {}
    if not user_type: return {}
    if user_type in ['warehouse']: return { user.username: user }
    if user_type in ['zone']:
        filter_params['admin_user'] = user
    if user_type in ['company']: filter_params['company__company_name']= user.username

    if user_type == "subsidiary":
        zone_params = {
            "user__userprofile__warehouse_type": 'STORE'
        }
        user_zones_ids = UserGroups.objects.filter(**zone_params).values('user_id')


        filter_params['admin_user_id__in'] = user_zones_ids
        warehouses_objs = UserGroups.objects.filter(**filter_params)
        warehouse_dict = {}
        for warehouse  in warehouses_objs:
            warehouse_dict[warehouse.user.username] = warehouse.user
        return warehouse_dict

    if user_type != 'subuser':

        warehouses_objs = UserGroups.objects.filter(**filter_params)
        warehouse_dict = {}
        for warehouse  in warehouses_objs:
            warehouse_dict[warehouse.user.username] = warehouse.user
        return warehouse_dict

    else:
        warehouses_dict = get_subuser_warehouses(user)

    return warehouses_dict

def get_subuser_warehouses(subuser: User) -> dict:
    """
        Get a list of warehouse usernames associated with the provided subuser.

        Parameters:
            subuser (User): The subuser object for whom the associated warehouses are to be retrieved.

        Returns:
            list: A list of warehouse usernames that are associated with the given subuser.

    """

    subuser_filter_params = {
        "staff__user": subuser,
        "warehouse__is_active": 1,
        "status": 1
    }

    warehouses_objs = StaffWarehouseMapping.objects\
                        .filter(**subuser_filter_params)\
                        .select_related('warehouse__userprofile')
    if USE_CACHE_OPS:
        warehouses_objs = warehouses_objs.cache()

    warehouse_dict = {obj.warehouse.username: obj.warehouse for obj in warehouses_objs}

    return warehouse_dict

def check_user_accessibility(user: User, warehouse: str) -> bool:
    """
        Check if the provided subuser has access to the specified warehouse.

        Parameters:
            subuser (User): The subuser object for whom the warehouse access is to be checked.
            warehouse (str): The username of the warehouse to be checked for access.

        Returns:
            bool: True if the subuser has access to the warehouse, False otherwise.
    """
    warehouses = get_user_warehouses(user)
    accessible = warehouse in warehouses

    return accessible


def get_warehouse_companyuser(user: User):
    try:
        company = user.userprofile.company
        return company
    except:
        return None

def check_subusers_belongs_to_same_company(subuser1: User, subuser2: User):
    subuser1_company = get_warehouse_companyuser(subuser1.current_warehouse)
    subuser2_company = get_warehouse_companyuser(subuser2.current_warehouse)

    if subuser1_company and subuser2_company and subuser1_company == subuser2_company:
        return True

    return False



def get_company_nodes(company: CompanyMaster, node_type: str, return_type: str):
    """
        Retrieve nodes (users) associated with a specific company based on the specified node type and return type.

        Parameters:
        - company (CompanyMaster): An instance of the CompanyMaster model representing the company.
        - node_type (str): The type of node to retrieve. Supported types are 'warehouse', 'zone', and 'subsidiary'.
        - return_type (str): The desired format for the output. Supported types are 'ids', 'objects', and 'usernames'.

        Returns:
        - If return_type is 'ids': A list of user IDs associated with the specified company, node type, and warehouse level.
        - If return_type is 'objects': QuerySet of User objects associated with the specified company, node type, and warehouse level.
        - If return_type is 'usernames': A list of usernames associated with the specified company, node type, and warehouse level.
    """

    node_mapping = {
        "warehouse": 3,
        "zone": 2,
        "subsidiary": 1,
    }
    try:
        warehouse_level = node_mapping.get(node_type)
        nodes = User.objects.select_related('userprofile').filter(userprofile__company=company, userprofile__warehouse_level=warehouse_level)

        if return_type == "ids":
            return list(nodes.values_list('id', flat=True))
        elif return_type == "objects":
            return nodes
        elif return_type == "usernames":
            return list(nodes.values_list('username', flat=True))
    except Exception as e:
        log.info("get_company_nodes error %s", str(e))
        return None

def get_company_active_warehouses(company: CompanyMaster):
    """
    Retrieves a list of usernames for users associated with the given company.

    Args:
        user (User): The company object representing the company.

    Returns:
        list: A list of usernames for users associated with the company.
    """
    warehouse_ids = list(
        UserProfile.objects.filter(company=company.id, user__is_active=True, warehouse_level=3).values_list('user_id', flat=True)
    )

    return warehouse_ids

def get_warehouse_objects(user_ids: list):
    """
    Retrieves a list of usernames for users associated with the given company.

    Args:
        user (User): The user object representing the company.

    Returns:
        list: A list of usernames for users associated with the company.
    """
    warehouse_objects_dict = User.objects.filter(id__in=user_ids,is_active=True).in_bulk()

    return warehouse_objects_dict
