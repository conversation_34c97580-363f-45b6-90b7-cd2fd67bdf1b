#package imports
import json
import pytz
import copy
import datetime
from firebase_admin import auth
from oauth2_provider.models import Application, AccessToken
from datetime import timedelta

#django imports
from django.urls import reverse
from django.contrib.auth import authenticate, login, logout
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.models import Permission
from django.core.serializers.json import DjangoJSONEncoder
from django.views.decorators.http import  require_GET
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.views.decorators.http import require_http_methods
from django.utils.encoding import smart_str, smart_bytes, DjangoUnicodeDecodeError
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from django.contrib.sites.shortcuts import get_current_site
from django.conf import settings
from django.utils import timezone


#django restframework imports
from rest_framework import generics, serializers
from rest_framework.exceptions import ValidationError
from rest_framework.views import APIView
from rest_framework.authentication import BasicAuthentication
from core_operations.views.integration.user_integration import CsrfExemptSessionAuthentication
from core.models.common import InvoiceForms

#app imports
from oauth2_provider.models import Application

#wms_base imports
from wms_base.models import (
    CompanyLevelConfigs, User, StaffWarehouseMapping,
    Integrations
)
from wms_base.wms_utils import (
    CONFIG_ORDER_TYPE_DICT, PICKING_SCREEN_ATTRIBUTES, PICKING_SUMMARY_ATTRIBUTES,
    get_git_current_version_number,
    init_logger, signed_public_dashboard, get_permission
)
from wms_base.sendgrid_mail import send_django_mail
from wms_base.middleware.security_check import SecurityCheck
from wms_base.devrev import DevRev



from inventory.models.locator import ZoneMaster
from lms.models import EmployeeMaster

#Core Imports
from core.models import MiscDetail, MiscDetailOptions
from core_operations.views.common.main import (
    get_multiple_misc_values, get_misc_value,
    get_local_date, get_user_role_ids, get_warehouse, get_multiple_misc_value_split, get_user_ip
)
from core_operations.views.configurations.user_zone_config import get_user_assigned_zones

#auth imports
from auth.serializers.password import RequestPasswordResetEmailSerializer, SetNewPasswordAPIViewSerializer
from auth.serializers.users import LoginSerializer, UserFilterSerializer


#service imports
from core_operations.views.services.packing_service import PackingService
from core_operations.views.services.approval_service import ApprovalService

from .utils import *
log = init_logger('logs/auth.log')



def return_response(data, content_type='application/json'):
    return HttpResponse(json.dumps(data, cls=DjangoJSONEncoder), content_type=content_type)

#helper functions
def add_user_permissions(request, response_data, user=None):
    status_dict = {1: 'true', 0: 'false'}
    multi_warehouse = 'false'
    user_profile = user.userprofile
    user_role = ''
    request_user_profile = request.user.userprofile
    show_pull_now = False
    integrations = Integrations.objects.filter(user=user.id, status=1)
    if integrations:
        show_pull_now = True

    if user_profile.multi_warehouse:
        multi_warehouse = 'true'
    parent_data = {}
    parent_data['userId'] = user.id
    parent_data['userName'] = user.username
    parent_data['user_first_name'] = user.first_name
    admin_user = get_admin(user)

    #get the company configs
    config_keys = ['STOCKAI', 'dock_scheduling']
    company_configs = CompanyLevelConfigs.objects.filter(company=user_profile.company, config_key__in=config_keys).values('config_key', 'config_value')

    parent_data['parent_username'] = admin_user.get_username()
    parent_data['logo'] = COMPANY_LOGO_PATHS.get(user.username, '')
    response_data['data']['userName'] = request.user.username
    response_data['data']['userId'] = request.user.id
    response_data['data']['notification_count'] = 0 #notification_count
    response_data['data']['parent'] = parent_data
    response_data['data']['groups'] = get_user_roles_names(request, request.user)
    response_data['data']['roles'] = get_user_permissions(request, user)
    response_data['data']['roles']['tax_type'] = 1 #tax_type
    response_data['data']['roles']['user_role'] = user_role
    response_data['data']['roles']['labels'] = get_label_permissions(response_data['data']['roles']['label_perms'])
    response_data['data']['roles']['permissions']['is_superuser'] = status_dict[int(request.user.is_superuser)]
    response_data['data']['roles']['permissions']['is_staff'] = status_dict[int(request.user.is_staff)]
    response_data['data']['roles']['permissions']['multi_warehouse'] = multi_warehouse
    response_data['data']['roles']['permissions']['show_pull_now'] = show_pull_now
    response_data['data']['roles']['permissions']['custom_roles'] = request.user.current_warehouse.userprofile.company.custom_roles
    response_data['data']['service_details'] = {}

    #add company configs
    for config in company_configs:
        if config.get('config_key') in ['STOCKAI']:
            response_data['data']['roles']['permissions'][config['config_key']] = config['config_value']
        elif config.get('config_key') in ['dock_scheduling']:
            response_data['data']['service_details'][config['config_key']] = config['config_value']

    misc_types = [
        "customer_portal_prefered_view", "weight_integration_name",
        "order_manage", "merge_picking", "sale_order_types", "scan_sku_mandatory", 
        "mandate_first_sku_scan", "scan_picklist_option", "idle_timeout"
    ]
    warehouse_misc_types = [
        "packing_mandatory_for_invoice", "restrict_invoice_at_picking", "allow_close_manifest", "web_invoice_grouping",
        "invoice_split_on_invoice_group","invoice_preview__extra_fields", "invoice_to_picklist_quantity"
    ]
    sub_user_warehouse_misc_types = ["tab_view_in_mobile", "selected_order_types", "rapid_picklist_confirmation", "selected_cluster_names"]
    request_misc_dict = get_multiple_misc_values(misc_types, request.user.id)
    warehouse_misc_dict = get_multiple_misc_values(warehouse_misc_types, user.id)
    sub_user_warehouse_misc_dict = get_multiple_misc_values(sub_user_warehouse_misc_types, request.user.id, account_id=user_profile.id)
    response_data['data']['roles']['permissions']['order_manage'] = request_misc_dict.get('order_manage','false')
    response_data['data']['roles']['permissions']['merge_picking'] = request_misc_dict.get('merge_picking','false')
    response_data['data']['roles']['permissions']['customer_portal_prefered_view'] = request_misc_dict.get('customer_portal_prefered_view','false')
    if response_data['data']['roles']['permissions']['customer_portal_prefered_view'] == 'false':
        response_data['data']['roles']['permissions']['customer_portal_prefered_view'] = ''
    response_data['data']['roles']['permissions']['weight_integration_name'] = request_misc_dict.get('weight_integration_name','false')
    if response_data['data']['roles']['permissions']['weight_integration_name'] == 'false':
        response_data['data']['roles']['permissions']['weight_integration_name'] = ''
    response_data['data']['roles']['permissions']['scan_sku_mandatory'] = request_misc_dict.get('scan_sku_mandatory','false')
    if response_data['data']['roles']['permissions']['scan_sku_mandatory'] == 'false':
        response_data['data']['roles']['permissions']['scan_sku_mandatory'] = ''
    response_data['data']['roles']['permissions']['mandate_first_sku_scan'] = request_misc_dict.get('mandate_first_sku_scan','false')
    if response_data['data']['roles']['permissions']['mandate_first_sku_scan'] == 'false':
        response_data['data']['roles']['permissions']['mandate_first_sku_scan'] = ''
    response_data['data']['roles']['permissions']['invoice_callback_trigger'] = get_permission(request.user,'invoice_callback_trigger')
    response_data['data']['roles']['permissions']['can_change_batch_details'] = get_permission(request.user,'can_change_batch_details')
    response_data['data']['roles']['permissions']['packing_mandatory_for_invoice'] = warehouse_misc_dict.get('packing_mandatory_for_invoice','')
    response_data['data']['roles']['permissions']['web_invoice_grouping'] = request_misc_dict.get('web_invoice_grouping','false')
    response_data['data']['roles']['permissions']['invoice_preview_extra_fields'] = warehouse_misc_dict.get('invoice_preview__extra_fields','false')
    if response_data['data']['roles']['permissions']['web_invoice_grouping'] == 'false':
        response_data['data']['roles']['permissions']['web_invoice_grouping'] = ['order_reference', 'customer_reference']
    if response_data['data']['roles']['permissions']['invoice_preview_extra_fields'] == 'false':
        response_data['data']['roles']['permissions']['invoice_preview_extra_fields'] = []
    response_data['data']['roles']['permissions']['restrict_invoice_at_picking'] = warehouse_misc_dict.get('restrict_invoice_at_picking','true')
    response_data['data']['roles']['permissions']['allow_close_manifest'] = warehouse_misc_dict.get('allow_close_manifest','manifest')
    response_data['data']['roles']['permissions']['scan_picklist_option'] = request_misc_dict.get('scan_picklist_option', "scan_sku_location")
    response_data['data']['roles']['permissions']['invoice_split_on_invoice_group'] = request_misc_dict.get('invoice_split_on_invoice_group', 'false')
    response_data['data']['idle_timeout'] = request_misc_dict.get('idle_timeout', 600)
    response_data['data']['roles']['permissions']['tab_view_in_mobile'] = sub_user_warehouse_misc_dict.get('tab_view_in_mobile', 'false')
    response_data['data']['roles']['permissions']['rapid_picklist_confirmation'] = sub_user_warehouse_misc_dict.get('rapid_picklist_confirmation', 'false')
    response_data['data']['roles']['permissions']['selected_order_types'] = sub_user_warehouse_misc_dict.get('selected_order_types', 'false')
    if response_data['data']['roles']['permissions']['selected_order_types'] == 'false':
        response_data['data']['roles']['permissions']['selected_order_types'] = request_misc_dict.get('sale_order_types', '')
    response_data['data']['roles']['permissions']['selected_cluster_names'] = sub_user_warehouse_misc_dict.get('selected_cluster_names', '')
    if response_data['data']['roles']['permissions']['selected_cluster_names'] == 'false':
        response_data['data']['roles']['permissions']['selected_cluster_names'] = request_misc_dict.get('selected_cluster_names', '')
    response_data['data']['roles']['permissions']['invoice_to_picklist_quantity'] = warehouse_misc_dict.get('invoice_to_picklist_quantity', 'false')

    # Fetching user assigned zones
    user_assigned_zone_dict = get_user_assigned_zones(user, request.user, source='APP')
    response_data['data']['roles']['permissions']['user_assigned_zones'] = user_assigned_zone_dict.get(request.user.username, {}).get('zones', [])

    misc_type_split_dict = {
        "picking_screen_attributes": list(PICKING_SCREEN_ATTRIBUTES.values()),
        "picking_summary_attributes": list(PICKING_SUMMARY_ATTRIBUTES.values()),
    }
    split_misc_values = get_multiple_misc_value_split(list(misc_type_split_dict.keys()), user.id)
    for misc_key, default_value in misc_type_split_dict.items():
        if split_misc_values.get(misc_key) is None:
            response_data['data']['roles']['permissions'][misc_key] = default_value
        else:
            response_data['data']['roles']['permissions'][misc_key] = split_misc_values.get(misc_key, [])

    filter_params = {"misc_detail__user": user.id, "status": 1}
    misc_details = list(MiscDetailOptions.objects.filter(**filter_params).values('misc_key', 'misc_value'))
    misc_detail_options = {}
    for misc in misc_details:
        misc_detail_options.setdefault(misc['misc_key'], [])
        misc_detail_options[misc['misc_key']].append(misc['misc_value'])
    # Framing default list for order type configs
    for order_type_config in CONFIG_ORDER_TYPE_DICT:
        if misc_detail_options.get(order_type_config) in [None, '', 'false']:
            misc_detail_options[order_type_config] = []
    response_data['data']['roles']['permissions'].update(misc_detail_options)
    batch_character_mapping = MiscDetail.objects.filter(user=user.id, misc_type='batch_character_mapping')
    response_data['data']['roles']['permissions']['batch_character_mapping'] = batch_character_mapping[0].json_data if batch_character_mapping else {}
    company_name = ''
    if user_profile.company:
        company_name = user_profile.company.company_name
        if user_profile.company.logo:
            response_data['data']['parent']['logo'] = user_profile.company.logo.url
    warehouse_type_name = get_warehouse_type_name(user_profile)
    response_data['data']['user_profile'] = {'first_name': request.user.first_name, 'last_name': request.user.last_name,
                                             'registered_date': get_local_date(request.user,
                                                                               user_profile.creation_date),
                                             'email': request.user.email,
                                             'id': user_profile.id,
                                             'state': user_profile.state,
                                             'trail_user': status_dict[int(user_profile.is_trail)],
                                             'company_name': company_name,
                                             'wh_address': user_profile.wh_address,
                                             'industry_type': user_profile.industry_type,
                                             'user_type': user_profile.user_type,
                                             'request_user_type': request_user_profile.user_type,
                                             'warehouse_type': user_profile.warehouse_type,
                                             'warehouse_level': user_profile.warehouse_level,
                                             'multi_level_system': user_profile.multi_level_system,
                                             'company_id': user_profile.company_id,
                                             'warehouse_type_name': warehouse_type_name}

    setup_status = 'false'
    if 'completed' not in user_profile.setup_status:
        setup_status = 'true'
    response_data['data']['roles']['permissions']['setup_status'] = setup_status


    if user_profile.is_trail:
        time_now = datetime.datetime.now().replace(tzinfo=pytz.timezone('UTC'))
        exp_time = get_local_date(request.user, time_now, send_date=1) - get_local_date(request.user,
                                                                                        user_profile.creation_date,
                                                                                        send_date=1)
        response_data['data']['user_profile']['expiry_days'] = 30 - int(exp_time.days)
    user_type = 'default'
    user_type = request_user_profile.user_type
    response_data['data']['roles']['permissions']['user_type'] = user_type
    response_data['message'] = 'Success'
    return response_data

def add_user_type_permissions(user_profile):
    update_perm = False
    if user_profile.user_type == 'warehouse_user':
        exc_perms = ['qualitycheck', 'qcserialmapping', 'palletdetail', 'palletmapping', 'ordershipment',
                     'shipmentinfo', 'shipmenttracking', 'networkmaster', 'tandcmaster', 'enquirymaster',
                     'corporatemaster', 'corpresellermapping', 'staffmaster', 'barcodebrandmappingmaster',
                     'companymaster', 'pendingpr', 'pendingpo', 'userprefixes', 'uommaster', 'purchaseapprovalconfig']
        update_perm = True
    elif user_profile.user_type == 'marketplace_user':
        exc_perms = ['productproperties', 'sizemaster', 'pricemaster', 'networkmaster', 'tandcmaster', 'enquirymaster',
                    'corporatemaster', 'corpresellermapping', 'staffmaster', 'barcodebrandmappingmaster',
                     'companymaster', 'pendingpr', 'pendingpo', 'userprefixes', 'uommaster', 'purchaseapprovalconfig']
        update_perm = True
    if update_perm:
        exc_perms = exc_perms + PERMISSION_IGNORE_LIST
        perms_list = []
        for perm in exc_perms:
            perms_list.append('add_' + str(perm))
            perms_list.append('change_' + str(perm))
            perms_list.append('delete_' + str(perm))
            perms_list.append('view_' + str(perm))
        permissions = Permission.objects.exclude(codename__in=perms_list)
        user_profile.user.user_permissions.add(*permissions)
        
def get_user_clientcreds(user: User, response_data: dict = {}) -> dict:
    oauth_data = Application.objects.filter(name=user.username)
    if oauth_data.exists():
        oauth_data = oauth_data[0]
        response_data['client_id'] = oauth_data.client_id
        response_data['client_secret'] = oauth_data.client_secret
        response_data['grant_type'] = oauth_data.authorization_grant_type
        single_session_obj = MiscDetail.objects.filter(misc_type='multi_session', user=user.current_warehouse.id).values_list("misc_value", flat=True)
        if single_session_obj and single_session_obj[0] == "true" and not user.userprofile.integrations_user:
            if  AccessToken.objects.filter(application=oauth_data, expires__gt=timezone.now()).exists():
                response_data['active_token'] = True


    return response_data

def get_user_warehouses_details(user: User, response_data: dict = {}) -> dict:
    filter_params = {
        "staff__user": user,
        "warehouse__is_active": 1,
        "status": 1
    }
    warehouses_objs = StaffWarehouseMapping.objects.filter(**filter_params)

    #adding warehouses
    warehouses = list(warehouses_objs.values_list("warehouse__username", flat=True).order_by("warehouse__username"))
    response_data['warehouses'] = warehouses

    if (user.current_warehouse.username not in warehouses) or not (user.current_warehouse):
        user.current_warehouse_id = warehouses_objs.first().warehouse_id
        user.save()

    response_data['warehouse'] = user.current_warehouse.username
    response_data['company_code'] = user.current_warehouse.userprofile.company.company_code

    return response_data

def get_user_from_google_auth_token(googleAuthToken: str):
    try:
        decoded_token = auth.verify_id_token(googleAuthToken)
        email = decoded_token['email']
    except:
        return None

    try:
        user = User.objects.get(email=email)
        return user
    except:
        return None
    
def user_failed_attempts(username: str) -> None:
    try:
        PASS_MAX_ATTEMPTS = int(getattr(settings, "PASS_MAX_ATTEMPTS", 3))
        user = User.objects.get(username=username)

        # check for company level config
        company_level_config = CompanyLevelConfigs.objects.filter(config_key="PASS_MAX_ATTEMPTS", company=user.current_warehouse.userprofile.company)
        if company_level_config.exists():
            PASS_MAX_ATTEMPTS = int(company_level_config[0].config_value)

        user.failed_attempts += 1
        if user.failed_attempts >= PASS_MAX_ATTEMPTS:
            user.locked = True
        user.save()
    except:
        pass
    
def check_user_is_locked(username: str) -> bool:
    try:
        user = User.objects.get(username=username)
        return user.locked
    except:
        return False


def user_dashboards_metabase(user: User, response_data: None) -> dict:
    if not response_data:
        response_data = {}
    user_analytics_dashboard = getattr(settings, 'METABASE_DASHBOARD_ID', None)
    response_data['dashboard_url'] = None
    if user_analytics_dashboard:
        response_data['dashboard_url'] = signed_public_dashboard(
            user.current_warehouse.username, 
            user_analytics_dashboard
        )
    integration_dashboard_url = getattr(settings, 'METABASE_INTEGRATION_DASHBOARD_ID', None)
    response_data['integration_dashboard_url'] = None
    if integration_dashboard_url:
        response_data['integration_dashboard_url'] = signed_public_dashboard(
            user.current_warehouse.username, 
            integration_dashboard_url
        )
    integration_dashboard_url = getattr(settings, 'METABASE_PUTAWAY_DASHBOARD_ID', None)
    response_data['putaway_dashboard_url'] = None
    if integration_dashboard_url:
        response_data['putaway_dashboard_url'] = signed_public_dashboard(
            user.current_warehouse.username, 
            integration_dashboard_url
        )

    return response_data


def warehouse_security_checks(request, user:User, warehouse_name:str, response_data):

    if warehouse_name:
        #check if warehouse is valid and accessible warehouse
        if warehouse_name not in response_data['warehouses']:
            return True, "Invalid Warehouse"
        else:
            warehouse = User.objects.get(username=warehouse_name)
    else:
        warehouse = request.user.current_warehouse

    #check security conf
    user_ip = get_user_ip(request)
    log.info("Calling security with params with IP %s | request %s | user %s | warehouse %s"%(user_ip, request, user, warehouse))
    check, message = SecurityCheck(request).check_securityconf(request, user, warehouse)
    log.info("Security check response %s %s"%(check, message))
    return check, message

def check_security_aceesible_warehouses(request, user:User, response_data):
    #warehouse security checks

    message = "No Accessible Warehouses Found"
    user_warehouses = copy.deepcopy(response_data['warehouses'])

    # cheking the current warehouse at first
    current_warehouse = user.current_warehouse.username
    if current_warehouse in user_warehouses: user_warehouses.remove(current_warehouse)
    user_warehouses.insert(0, current_warehouse)

    #checking warehouse security confiigs
    for warehouse_name in user_warehouses:
        check, message = warehouse_security_checks(request, user, warehouse_name, response_data)
        if not check:
            continue

        #update user current warehouse
        user.current_warehouse = User.objects.get(username=warehouse_name)
        user.save()

        return True, True

    return False, message

def get_user(request, authtype, username, password, google_auth_token, response_data):
    """
    Retrieves the user based on the authentication type and credentials provided.

    Args:
        request (HttpRequest): The HTTP request object.
        authtype (str): The authentication type ('normal' or 'google').
        username (str): The username for normal authentication.
        password (str): The password for normal authentication.
        google_auth_token (str): The Google authentication token for Google authentication.
        response_data (dict): The dictionary to store response data.

    Returns:
        tuple: A tuple containing the user object, response data, and request status code.
    """
    request_status_code = 200
    if authtype == 'normal':
        user = authenticate(request, username=username, password=password)
        if not user:
            user_failed_attempts(username)
            request_status_code = 400

    elif authtype == 'google':
        user = get_user_from_google_auth_token(google_auth_token)
        if not user:
            response_data['message'] = "Invalid Email"
            request_status_code = 400

    return user, response_data, request_status_code


def wms_login(request, data):
    """
    Checks login conditions for user
    """
    response_data = {'data': {}, 'message': 'Invalid Username or Password', 'warehouses':[]}
    username = data.get('login_id')
    password = data.get('password')
    authtype = data.get('authtype', 'normal')
    googleAuthToken = data.get('googleAuthToken', '')
    packing_service_url = data.get('packing_service_url', '')
    approval_service_url = data.get('approval_service_url', '')


    notify_expiry_date = getattr(settings, "NOTIFY_EXPIRY_DAYS", 3)

    #check user lock
    if check_user_is_locked(username):
        response_data['message'] = "User is Locked please reset your password"
        return response_data, 400

    #validate username and password
    if username and (password or authtype == 'google'):
        user, response_data, request_status_code = get_user(request, authtype, username, password, googleAuthToken, response_data)
        if request_status_code != 200:
            return response_data, request_status_code

        #check if user is active
        if not user.is_active:
            response_data['message'] = "User is Inactive"
            return response_data, 400

        #check user is active in atleast one warehouse
        subuser_filter_params = { "staff__user": user, "warehouse__is_active": 1, "status": 1}
        warehouses = StaffWarehouseMapping.objects.filter(**subuser_filter_params).prefetch_related('warehouse')
        if not warehouses:
            response_data['message'] = "User has no accessable warehouses"
            return response_data, 400

        #check is user is new
        if user.password_reset_required and authtype == 'normal':
            response_data['message'] = "Please reset your password before login"
            passwordreset = RequestPasswordResetEmail()
            uidb64 = urlsafe_base64_encode(smart_bytes(user.id))
            token = PasswordResetTokenGenerator().make_token(user)
            activation_link = passwordreset.prepare_passsword_reset_link(request, user)
            response_data['uidb64'] = uidb64
            response_data["token"] = token
            response_data["activation_link"] = activation_link
            return response_data, 307

        # check for password expiry
        password_expired, remaining_days = check_password_expiry(user)
        if password_expired and authtype == 'normal':
            response_data['message'] = 'Password expired please reset your password'
            return response_data, 400

        if remaining_days <= 0 and authtype == 'normal':
            response_data["password_expiry_notification"] = "Password has expired, please reset password"
        elif remaining_days <= notify_expiry_date and authtype == 'normal':
            response_data["password_expiry_notification"] = f"Password is about expire in {remaining_days} days, please reset password"


        login(request, user)
        user.failed_attempts = 0
        user.save()

        #adding warehouse details
        response_data = get_user_warehouses_details(user, response_data)

        #check for warehouse level security
        check, message = check_security_aceesible_warehouses(request, user, response_data)
        response_data['warehouse'] = user.current_warehouse.username
        if not check:
            response_data['message'] = message
            return response_data, 403

        #adding permissions
        response_data = add_user_permissions(request, response_data, request.user.current_warehouse)

        #adding version number
        version_number= get_git_current_version_number()
        response_data["data"].update({"version_number":version_number})

        #adding oauth creds
        response_data = get_user_clientcreds(user, response_data)
        
        #adding dashboards
        response_data = user_dashboards_metabase(user, response_data)
        
        zone_type = 'zone'
        if response_data.get('data', {}).get('roles', {}).get('permissions', {}).get('user_sub_zone_mapping') == 'true':
            zone_type = 'sub_zone'

        response_data['user_mail'] = user.email
        response_data['employee_id'] = user.employee.id
        response_data["zones"] = list(ZoneMaster.objects.filter(user=user.current_warehouse.id, zone_type=zone_type).values_list("zone", flat=True).distinct())
        response_data['dev_rev'] = DevRev(
                user, user.current_warehouse
            ).get_user_session_token()

        # setting packing service origin
        new_packing_url, new_packing_origin = None, None
        new_approval_url, new_approval_origin = None, None
        dock_scheduling_service_url = getattr(settings, "DOCK_SCHEDULING_SERVICE_URL", 'https://dock-scheduling-dev.stockone.com/api/v1/')
        if getattr(settings,'DEV_MODE', False):
            new_packing_url, new_packing_origin = PackingService.set_packing_service_origin(request, user, user.current_warehouse, packing_service_url)
            new_approval_url, new_approval_origin = ApprovalService.set_approval_service_origin(request, user, user.current_warehouse, approval_service_url)
            
        response_data.update({
            "services": {
                "asn": {
                    "url": new_approval_url or getattr(settings, "APPROVAL_SERVICE_URL", 'https://approval-dev.stockone.com/api/v1/'),
                    "origin_name": new_approval_origin or getattr(settings, "APPROVAL_ORIGIN_NAME", 'dev')
                },
                "dock": {
                    "url": dock_scheduling_service_url,
                    "token": response_data.get("data", {}).get("service_details", {}).get("dock_scheduling", "")
                }
            },
            "packing_service_url": new_packing_url or getattr(settings, "PACKING_SERVICE_URL", 'https://pack-dev.stockone.com/api/v1/'),
            "packing_origin_name": new_packing_origin or getattr(settings, "PACKING_ORIGIN_NAME", 'dev')
        })

    return response_data, 200


class UserLogin(APIView):

    def post(self, request, *args, **kwargs):
        data = request.data

        #payload data validations
        serializer = LoginSerializer(data=data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as error:
            return JsonResponse({"error": error.detail}, status=400)

        #validated payload
        payload_data = serializer.validated_data

        response_data, status_code = wms_login(request, payload_data)

        return JsonResponse(response_data, status=status_code)

@get_warehouse
@require_http_methods(["GET"])
def status(request):
    """
    Checks if user is a valid user or not
    """
    response_data = {'data': {}, 'message': 'Fail'}
    status_dict = {1: 'true', 0: 'false'}

    if request.user.is_authenticated:
        response_data = add_user_permissions(request, response_data)
    version_number= get_git_current_version_number()
    response_data["data"].update({"version_number":version_number })
    return JsonResponse(response_data)


@require_http_methods(["POST"])
def user_warehouses(request):
    filter_params = {
        "staff__user": request.user,
        "warehouse__is_active": 1,
        "status": 1
    }
    warehouses = list(StaffWarehouseMapping.objects.filter(**filter_params).values_list('warehouse__username', flat=True).order_by('warehouse__username'))
    warehouse = request.POST.get('warehouse', '')
    response_data = {'data': {}, 'message': 'Fail', "warehouses": warehouses}
    if not warehouse:
        return JsonResponse({"message": "Warehouse Required"}, status=400)
    if warehouse not in warehouses:
        return JsonResponse({"message": "User Not Part Of Warehouse"}, status=400)

    check, message = warehouse_security_checks(request, request.user, warehouse, response_data)
    if not check:
        return JsonResponse({"message": f"Cannot Switch Warehouse {message}"}, status=400)


    wh_user = User.objects.get(username=warehouse)
    if wh_user and wh_user.is_active:
        # Adding selected WH group to sub user
        request.user.current_warehouse = wh_user
        request.user.save()

        response_data['company_code'] = wh_user.userprofile.company.company_code

        emp = EmployeeMaster.objects.get(user_id=request.user.id)
        emp.location = wh_user
        emp.save()
        response_data = add_user_permissions(request, response_data, wh_user)
        response_data['dev_rev'] = DevRev(
                request.user, request.user.current_warehouse
            ).get_user_session_token()

        response_data['message'] = 'Success'
        response_data = user_dashboards_metabase(request.user, response_data)

    else:
        response_data['message'] = 'Warehouse is inactive'
        return JsonResponse(response_data)

    return JsonResponse(response_data)





@require_GET
def get_user_details(request):

    # validating the user filters
    try:
        requestdata = request.GET
        serializer = UserFilterSerializer(data=requestdata)
        serializer.is_valid(raise_exception=True)
    except ValidationError as e:
        error_message = {"error": e.detail}
        return JsonResponse(error_message, status=400)

    # getting the user details
    filter_params = serializer.validated_data
    username = filter_params.get('username')
    email_id = filter_params.get('email_id')

    # checking if user exists
    user_data = User.objects.filter(userprofile__user_type="sub_user")
    if username:
        user_data = user_data.filter(username__iexact=username)
    if email_id:
        user_data = user_data.filter(email__iexact=email_id)

    #None when no user found
    if not user_data.exists():
        return JsonResponse({"data": None})

    user_dict = user_data.values('id', 'username', 'email', 'first_name').first()
    if user_dict:
        user_dict['roles'] = get_user_role_ids(user_data.first())
    return JsonResponse({"data": user_dict})


@get_warehouse
@require_GET
def get_user_generic(request, warehouse: User):
    requestdata = request.GET 
    param = requestdata.get('filter_on', 'email')
    param_value = requestdata.get('filter_value', None)
    user_data = User.objects.filter(userprofile__user_type="sub_user")
    if param_value is not None:
        query_dict = {param: param_value}
        user_data = user_data.filter(**query_dict)

    response = [userdict for userdict in user_data.values('id', 'username', 'email', 'first_name')]
    return JsonResponse(response, safe=False)

@get_warehouse
@require_GET
def get_session_details(request, warehouse: User):
    session_data = {}
    token_object = list(AccessToken.objects.filter(token=request.META.get('HTTP_AUTHORIZATION','')).values())
    if token_object:
        session_data = token_object[0]
        session_data.update({
            "warehouse": warehouse.username,
            "user": request.user.username,
            "user_id": request.user.id,
            "warehouse_id": warehouse.id
        })
    return JsonResponse(session_data)

@get_warehouse
@require_http_methods(["POST"])
def wms_logout(request, warehouse: User):
    token_objects = AccessToken.objects.filter(token=request.META.get('HTTP_AUTHORIZATION',''))
    if token_objects:
        token_object = token_objects[0]
        application = token_object.application

        single_session_obj = MiscDetail.objects.filter(misc_type='multi_session', user=warehouse.id).values_list("misc_value", flat=True)
        if single_session_obj and single_session_obj[0] == "true":
            # Invalidate all tokens associated with the application
            tokens = AccessToken.objects.filter(application=application)
            for token in tokens:
                token.expires = timezone.now() - timedelta(seconds=1)
                token.save()

        else:
            # Invalidate the current token
            token_object.expires = timezone.now() - timedelta(seconds=1)
            token_object.save()


    #update the last logout in staffMaster
    StaffMaster.objects.filter(user=request.user).update(last_logout=timezone.now())

    logout(request)
    return JsonResponse({"message": "Logged Out Successfully"}, safe=False)


class RequestPasswordResetEmail(generics.GenericAPIView):
    serializer_class = RequestPasswordResetEmailSerializer

    def post(self, request):
        email = request.data.get("email")

        users = User.objects.filter(email=email)
        if users.exists():
            for user in users:
                absolute_url = self.prepare_passsword_reset_link(request, user)
                html_template = self.prepare_html(user.first_name, user.username, user.email, absolute_url)
                if user.current_warehouse:
                    send_django_mail(user.current_warehouse, "Stockone Account Password Reset", "Password Reset Request", [user.email], html_message=html_template)
            return JsonResponse({"message": "An email has been sent to the email address associated with this account, containing further instructions to reset your password.."})
        else:
            return JsonResponse({"message": "No User Exists"}, status=400)

    def prepare_passsword_reset_link(self, request, user: User):
        uidb64 = urlsafe_base64_encode(smart_bytes(user.id))
        token = PasswordResetTokenGenerator().make_token(user)
        current_site = get_current_site(request=request).domain
        relative_link = reverse("auth:reset_password_confirm", kwargs={"uidb64": uidb64, "token": token})
        absolute_url = 'https://'+ current_site + relative_link
        if request.headers.get("Referer"):
            referer = request.headers.get("Referer")
            absolute_url = f"{referer}v2/#/reset-password/{uidb64}/{token}"

        return absolute_url

    def prepare_html(self, firstname, username, email, reset_link):
        current_year = datetime.datetime.now().year
        html = """\
            <html lang="en">
                <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f3f7f4;">
                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                        <tr>
                            <td align="center">
                                <table width="600" cellpadding="0" cellspacing="0" border="0" style="border-collapse: collapse; background-color: #ffffff; margin: 20px auto;">
                                    <tr>
                                        <td align="center" style="padding: 40px;">
                                            <img src="https://firebasestorage.googleapis.com/v0/b/stockone-auth.appspot.com/o/domain_images%2Fstockone-logo.png?alt=media&token=37b6057e-7646-4501-a0fc-e8b261e416a4" alt="Stockone Logo" width="150">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="padding: 40px;">
                                            <h2 style="color: #38a869;">Account Password Reset</h2>
                                            <p style="color: #333;">Hello {firstname},</p>
                                            <p style="color: #333;">We have received a request to reset the password for your Stockone account with username [ {username} ] linked to the email address <strong>{email}</strong>. If you did not initiate this action, feel free to disregard this email.</p>
                                            <p style="color: #333;">To proceed with the password reset process, kindly click on the button provided below. This will grant you access to modify your password. Please note that this link will become invalid after 60 minutes.</p>
                                            <p><a href="{reset_link}" style="display: inline-block; padding: 10px 20px; background-color: #38a869; color: #fff; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="padding: 20px;">
                                            <p style="color: #333;">If you have any questions or concerns, please contact our support team at <a href="mailto:<EMAIL>" style="color: #38a869;"><EMAIL></a>.</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" style="padding: 20px; background-color: #333;">
                                            <p style="color: #fff;">&copy; {current_year} Stockone. All rights reserved.</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </body>
            </html>
        """
        filters = {
            'document_type': 'USER AUTH',
            'invoice_format': 'password_reset',
            'status': 1
        }
        html_form = list(InvoiceForms.objects.filter(**filters).values('output_data'))
        if html_form:
            html = html_form[0].get('output_data')
        html = html.format(
            firstname=firstname,
            username=username,
            email=email,
            reset_link=reset_link,
            current_year=current_year
        )
        
        return html


class PasswordTokenCheckAPI(generics.GenericAPIView):
    def get(self, request, uidb64, token):

        try:
            user_id = smart_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(id=user_id)

            if not PasswordResetTokenGenerator().check_token(user, token=token):
                return JsonResponse({"error": "Token is invalid, please request new one"}, status=400)

            return JsonResponse({'message': 'Credentials Valid', "uidb64": uidb64, "token": token})

        except DjangoUnicodeDecodeError as e:
            log.info("token expiry" + str(e))
            return JsonResponse({"error": "Token is invalid, please request new one"}, status=400)


class SetNewPasswordAPIView(generics.GenericAPIView):
    serializer_class = SetNewPasswordAPIViewSerializer

    def patch(self, request):
        data = json.loads(request.body)
        #payload data validations
        serializer = self.serializer_class(data=data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as error:
            return JsonResponse({"error": error.detail}, status=400)

        #payload data
        uidb64 = data.get("uidb64")
        password = data.get("password")
        old_password = data.get("old_password")
        self_reset = data.get("self_reset", None)

        admin_reset = data.get("admin_reset", False)

        # if not self_reset: 
        #     try:
        #         user_id = smart_str(urlsafe_base64_decode(uidb64))
        #         user = User.objects.get(id=user_id)
        #     except:
        #         return JsonResponse({"error": "Invalid Reset Token"}, status=400)
        # else:
        #     user = self.get_user_id_from_token(uidb64)
        #     if not user:
        #         return JsonResponse({"error": "Invalid Token ID"}, status=400)

        #     if not user.check_password(old_password):
        #         return JsonResponse({"error": "Invalid Old Password"}, status=400)
        user = serializer.user
        user.set_password(password)
        user.locked = False
        user.failed_attempts = 0
        user.email_verified = True
        user.password_reset_required = self.check_password_reset_required(admin_reset)
        user.save()

        self.create_new_oauth(user)

        return JsonResponse({"message": "Password Reset Done"})
    

    def check_password_reset_required(self, admin_reset):
        return True if admin_reset else False

        

    def create_new_oauth(self, user: User) -> None:

        #deleting old oauth application
        oauth_data = Application.objects.filter(name=user.username)
        if oauth_data.exists():
            oauth_data.delete()

        #creating new oauth application
        new_app = Application(
            client_type='confidential',
            authorization_grant_type='client-credentials',
            name=user.username,
            user=user,
            skip_authorization=0
        )
        new_app.save()

class ChangeUserPassword(generics.GenericAPIView):
    serializer_class = RequestPasswordResetEmailSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def post(self, request):
        email = request.data.get("email")
        request_user = request.user

        passwordreset = RequestPasswordResetEmail()
        user = User.objects.get(email=email)
        if user:
            if not self.validate_permissions(request_user, user):
                return JsonResponse({"message": "User has no privileges to change password"}, status=400)

            if user.userprofile.integrations_user:
                return JsonResponse({"message": "Password changes are not permitted for integration users to ensure uninterrupted system functionality. For updates, contact support."}, status=400)

            reset_data = {}
            uidb64 = urlsafe_base64_encode(smart_bytes(user.id))
            token = PasswordResetTokenGenerator().make_token(user)
            activation_link = passwordreset.prepare_passsword_reset_link(request, user)

            reset_data['uidb64'] = uidb64
            reset_data["token"] = token
            reset_data["activation_link"] = activation_link
            return JsonResponse({"message": reset_data})
        else:
            return JsonResponse({"message": "No User Exists"}, status=400)

    def validate_permissions(self, request_user: User, updating_user: User):
        same_company = check_subusers_belongs_to_same_company(request_user, updating_user)
        has_permission = get_permission(request_user, 'change_user')

        if same_company and has_permission:
            return True

        return False