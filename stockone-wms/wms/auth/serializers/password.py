#django restgramework imports
from django.http import JsonResponse
from rest_framework import serializers

#django imports
from django.core.exceptions import ValidationError
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils.encoding import smart_str, force_str, smart_bytes, DjangoUnicodeDecodeError
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode

#auth imports
from auth.validations.password_validators import (
    LowercaseValidator, UppercaseValidator, 
    NumberValidator, SymbolValidator,
)
from oauth2_provider.models import Application, AccessToken

from auth.validations.helpers import validate_password_reuse

#wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger
log = init_logger('logs/auth_password_serializer.log')

class RequestPasswordResetEmailSerializer(serializers.Serializer):
    email = serializers.EmailField()
    
    class Meta:
        fields = ['email']
        
class SetNewPasswordAPIViewSerializer(serializers.Serializer):
    password = serializers.CharField(min_length=8)
    retype_password = serializers.CharField(min_length=8)
    token = serializers.CharField()
    uidb64 = serializers.CharField()
    self_reset = serializers.BooleanField(required=False)
    old_password = serializers.CharField(required=False)
    
    class Meta:
        fields = ['password', 'retype_password', 'token', 'uidb64', 'self_reset']


    def get_user_id_from_token(self, token):
        try:
            access_token_obj = AccessToken.objects.get(token=token)
            user_obj = User.objects.get(username=access_token_obj.application.name)
            return user_obj
        except Exception:
            return 0

    def validate(self, attrs):
        uidb64 = attrs.get("uidb64")
        token = attrs.get("token")
        password = attrs.get('password')
        retype_password = attrs.get('retype_password')
        old_password = attrs.get('old_password')
        self_reset = attrs.get('self_reset')

        if not self_reset: 
            try:
                user_id = smart_str(urlsafe_base64_decode(uidb64))
                user = User.objects.get(id=user_id)
                if not PasswordResetTokenGenerator().check_token(user, token=token):
                    raise serializers.ValidationError(
                        f"The Reset token is Invalid"
                    )
                self.user = user
            except Exception:
                raise serializers.ValidationError(
                    f"The Reset token is Invalid"
                )
        else:
            user = self.get_user_id_from_token(token)
            if not user:
                raise serializers.ValidationError(
                    f"Invalid Token"
                )
            
            if not user.check_password(old_password):
                raise serializers.ValidationError(
                    f"Invalid Old Password"
                )    
            self.user = user

        #password validations
        if password != retype_password:
            raise ValidationError(
                "Password Do not Match"
            )

        validators = [
            NumberValidator(),
            UppercaseValidator(),
            LowercaseValidator(),
            SymbolValidator(),
        ]

        for validator in validators:
            validator.validate(password)
            
        match = validate_password_reuse(user, password)
        if match:
            raise ValidationError(
               "The password cannot be the same as any of the last 3 passwords used."
            )

        return super().validate(attrs)