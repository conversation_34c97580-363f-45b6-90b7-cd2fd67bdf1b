#rest framework imports
from rest_framework import serializers

#django imports
from django.core import validators


# wms_base imports
from wms_base.models import User
from wms_base.wms_utils import init_logger
log = init_logger('logs/auth_password_serializer.log')


class LoginSerializer(serializers.Serializer):
    AUTHTYPE_CHOICES = (
        ('normal', 'normal'),
        ('google', 'google'),
    )
    login_id = serializers.CharField(allow_null=True, required=False)
    password = serializers.CharField(allow_null=True, required=False)
    authtype = serializers.ChoiceField(choices=AUTHTYPE_CHOICES, default='normal')
    googleAuthToken = serializers.CharField(allow_blank=True, required=False)
    packing_service_url = serializers.CharField(allow_blank=True, required=False)
    
    def validate(self, data):
        authtype = data.get('authtype')
        login_id = data.get('login_id')
        password = data.get('password')
        googleAuthToken = data.get('googleAuthToken')
        packing_service_url = data.get('packing_service_url')
        approval_service_url = data.get('approval_service_url')

        if authtype == 'google':
            if not googleAuthToken:
                raise serializers.ValidationError("googleAuthToken is required when authtype is google")
        else:
            if not login_id and not password:
                raise serializers.ValidationError("login_id and password are required when authtype is normal")

        if packing_service_url:
            url_validator = validators.URLValidator()
            try:
                url_validator(packing_service_url)
            except validators.ValidationError:
                raise serializers.ValidationError("Invalid packing_service_url")
        
        if approval_service_url:
            url_validator = validators.URLValidator()
            try:
                url_validator(approval_service_url)
            except validators.ValidationError:
                raise serializers.ValidationError("Invalid approval_service_url")

        return data


class UserFilterSerializer(serializers.Serializer):
    username = serializers.CharField(required=False)
    email_id = serializers.EmailField(required=False)

    def validate(self, attrs):
        """
        Check that at least one filter username or email_id is provided.
        """
        if not any(attrs.values()):
            raise serializers.ValidationError("At least one filter username or email_id is required.")
        return attrs


