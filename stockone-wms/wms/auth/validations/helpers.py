from wms_base.models import UserPassword
from django.contrib.auth.hashers import check_password

#wms_base imports
from wms_base.models import User
def validate_password_reuse(user: User, password: str) -> bool:
    stored_password_records = (
        UserPassword.objects.filter(user=user)[:2]
    )
    match = False
    for record in stored_password_records:
        match =  check_password(password, record.password)
        if match:
            break
    return match