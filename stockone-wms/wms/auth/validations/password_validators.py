import re


from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from .helpers import validate_password_reuse

class NumberValidator(object):
    def validate(self, password, user=None):
        if not re.findall(r'\d', password):
            raise ValidationError(
                self.get_help_text(),
                code='password_no_number',
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least 1 digit, 0-9."
        )


class UppercaseValidator(object):
    def validate(self, password, user=None):
        if not re.findall('[A-Z]', password):
            raise ValidationError(
                self.get_help_text(),
                code='password_no_upper',
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least 1 uppercase letter, A-Z."
        )


class LowercaseValidator(object):
    def validate(self, password, user=None):
        if not re.findall('[a-z]', password):
            raise ValidationError(
                self.get_help_text(),
                code='password_no_lower',
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least 1 lowercase letter, a-z."
        )


class SymbolValidator(object):
    def validate(self, password, user=None):
        if not re.findall(r'[()[\]{}|\\`~!@#$%^&*_\-+=;:\'",<>./?]', password):
            raise ValidationError(
                self.get_help_text(),
                code='password_no_symbol',
            )

    def get_help_text(self):
        return _(
            "Your password must contain at least 1 symbol: " +
            "()[]{}|\`~!@#$%^&*_-+=;:'\",<>./?"
        )


class PasswordReuseValidator(object):
    def validate(self, password, user=None):
        match = validate_password_reuse(user, password)
        if match:
            raise ValidationError(
               self.get_help_text(),
                code='password_no_reuse',
            )

    def get_help_text(self):
        return _("The password cannot be the same as any of the last 3 passwords used.")


class LengthValidator(object):
    def validate(self, password, user=None):
        if len(password) < 8:
            raise ValidationError(
                self.get_help_text(),
                code='password_too_short',
            )

    def get_help_text(self):
        return _(
            "Your password must be at least 8 characters long."
        )
