from django.db import models
from wms_base.models import TenantBaseModel
from django.db.models import ForeignKey

class SupplierMaster(TenantBaseModel):
    supplier_id = models.CharField(max_length=128, default='', db_index=True)
    user = models.PositiveIntegerField()
    name = models.CharField(max_length=256)
    supplier_reference = models.CharField(max_length=256, default='')
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=64)
    state = models.CharField(max_length=64)
    country = models.CharField(max_length=64)
    pincode = models.CharField(max_length=64)
    phone_number = models.CharField(max_length=32)
    email_id = models.EmailField(max_length=64)
    cst_number = models.CharField(max_length=64, default='')
    tin_number = models.CharField(max_length=64, default='')
    pan_number = models.Char<PERSON>ield(max_length=64, default='')
    status = models.IntegerField(default=1)
    tax_type = models.CharField(max_length=32, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    supplier_type = models.CharField(max_length=64, default='')
    po_exp_duration = models.IntegerField(default=0)
    spoc_name = models.CharField(max_length=256, default='')
    spoc_number = models.CharField(max_length=64, default='')
    spoc_email_id = models.EmailField(max_length=64, default='')
    lead_time = models.IntegerField(default=0)
    bank_name = models.CharField(max_length=256, default='')
    ifsc_code = models.CharField(max_length=64, default='')
    branch_name = models.CharField(max_length=256, default='')
    account_number = models.CharField(max_length=256, default='')
    account_holder_name = models.CharField(max_length=256, default='')
    markdown_percentage = models.FloatField(default=0)
    reference_id = models.CharField(max_length=64, default='')
    subsidiary = models.CharField(max_length=512, default='')
    place_of_supply = models.CharField(max_length=64, default='')
    currency_code = models.CharField(max_length=16, default='')
    qc_eligible = models.BooleanField(default=False)
    terms_and_conditions = models.CharField(max_length=64, default='')

    class Meta:
        db_table = 'SUPPLIER_MASTER'
        indexes = [
            models.Index(fields=['name', 'user']),
            models.Index(fields=['supplier_id', 'user']),
            models.Index(fields=['user']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        return '%s-%s' % (self.name, self.supplier_id)


class SupplierAttributes(TenantBaseModel):
    supplier = models.ForeignKey(SupplierMaster,on_delete=models.CASCADE, blank=True, null=True)
    attribute_name = models.CharField(max_length=64, default='')
    attribute_value = models.CharField(max_length=128, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SUPPLIER_ATTRIBUTES'
        unique_together = ('supplier', 'attribute_name')
        indexes = [
            models.Index(fields=['supplier', 'attribute_name']),
            models.Index(fields=['supplier']),
        ]

    def __str__(self):
        return str(self.supplier.name) + '-' + str(self.attribute_name)


class PaymentTerms(TenantBaseModel):
    
    payment_code = models.CharField(max_length=64, default='')
    payment_description = models.CharField(max_length=256, default='')
    supplier = ForeignKey(SupplierMaster,on_delete=models.CASCADE, blank=True, null=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    status = models.IntegerField(default=1) # 0-Inactive 1-Active

    class Meta:
        db_table = 'PAYMENT_TERMS'
        unique_together = ('payment_code', 'payment_description', 'supplier')

class NetTerms(TenantBaseModel):
    
    net_code = models.CharField(max_length=64, default='')
    net_description = models.CharField(max_length=256, default='')
    supplier = ForeignKey(SupplierMaster,on_delete=models.CASCADE, blank=True, null=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'NET_TERMS'
        unique_together = ('net_code', 'net_description', 'supplier')

class SKUSupplier(TenantBaseModel):
    user = models.PositiveIntegerField(default=0, db_index=True)
    attribute_type = models.CharField(max_length=64, default='')
    attribute_value = models.CharField(max_length=64, default='')
    supplier = ForeignKey(SupplierMaster,on_delete=models.CASCADE)
    supplier_type = models.CharField(max_length=32)
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE, blank=True, null=True)
    preference = models.CharField(max_length=32)
    moq = models.FloatField(default=0)
    supplier_reference = models.CharField(max_length=256, default='')
    supplier_code = models.CharField(max_length=128, default='')
    price = models.FloatField(default=0)
    costing_type = models.CharField(max_length=128, default='Price Based')
    lead_time = models.IntegerField(default=0)
    margin_percentage = models.FloatField(default=0)
    markup_percentage = models.FloatField(default=0)
    gatekeeper_margin = models.FloatField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    status = models.BooleanField(default=True)

    class Meta:
        db_table = 'SKU_SUPPLIER_MAPPING'
        indexes = [
            models.Index(fields=['supplier', 'sku']),
            models.Index(fields=['sku']),
        ]

    def __str__(self):
        return str(self.sku) + " : " + str(self.supplier)
