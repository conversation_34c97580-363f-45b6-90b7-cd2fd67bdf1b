import copy
import datetime
import urllib
import base64
import json
import traceback


from django.http import HttpResponse, JsonResponse

from django.template import loader
from django.shortcuts import render
from django.db.models import Q

from wms.settings.base import reports_database

#Model Imports
from wms_base.models import User, UserProfile, UserAddresses
from core.models import (
    SKUMaster, CurrencyExchangeMaster, SKUAttributes,
    MasterEmailMapping, TaxMaster, UserAttributes, MasterDocs
    )
from inbound.models import (
    SupplierMaster, SKUSupplier, OpenPO, PurchaseOrder, PendingPO, POHeader
    )
from outbound.models import OrderDetail, OrderFields, CustomerMaster
from inventory.models import SKUPackMaster

#Method Imports
from wms_base.mail_server import send_mail
from wms_base.wms_utils import (
    PO_SUGGESTIONS_DATA, PO_DATA, COMPANY_LOGO_PATHS, ISO_COMPANY_LOGO_PATHS,
    LEFT_SIDE_COMPNAY_LOGO
    )
from core_operations.views.common.main import (
    get_warehouse,
    get_utc_start_date, get_sku_ean_list,
    get_incremental, get_misc_value, get_purchase_company_address,
    number_in_words, get_po_company_logo, get_user_ip,
    get_user_prefix_incremental, get_multiple_misc_values,
    get_local_date_known_timezone, get_user_time_zone,
    get_local_date, get_custom_html_format,
    get_local_date_with_time_zone, get_uom_with_sku_code,
    get_sku_ean_numbers, generate_log_message, create_or_update_master_attributes, get_filtered_sku_pack_data
    )
from inbound.views.common.mail_pdf import write_and_mail_pdf
from inbound.views.common.common import get_max_pack_uom_quantity
from inbound.views.purchase_order.common import update_po_header
from .update_po import get_raisepo_group_data, get_raise_po_grouped_data
from .integrations import po_call_back_3p_integration
from .constants import (sku_code_const, unit_price_const, sku_size_const,
            csgt_pcnt_const,utgst_pcnt_const, sku_desc_const,
            sgst_pcnt_const, igst_pcnt_const, cess_pcnt_const,
            apmc_pcnt_const, mrp_const, wms_field_const)
from inbound.views.common.constants import AMT_CONSTANT

from .common import (
    check_purchase_order_created, save_or_update_po_extra_fields, frame_po_header_data
    )
from .validation import BasePOValidation

from .po_data import SKUWisePO
from .common import get_po_header_status_from_po_header_table

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/create_po' + today + '.log')
log_err = init_logger('logs/create_po.log')

TIMEZONE = 'Asia/Calcutta'
SUPPLIER_TYPE_MISMATCH_ERROR = "Selected PO type Doesn\'t match with Supplier Type Import"
PO_TYPE_MISMATCH_ERROR = "Selected Supplier Doesn\'t match with PO Type Import"
CURRENCY_EXCHANGE_ERROR = "Currency Exchange is not defined for this supplier on this date"

po_pdf_const = "templates/toggle/po_download.html"


def update_table_configuration(configurations, ean_flag, show_cess_tax, show_apmc_tax, display_remarks, misc_dict, industry_type, supplier_currency):
    table_headers = [sku_code_const, sku_desc_const, 'SKU Size', 'Supplier Code', 'Qty',
        'UOM', unit_price_const, 'Amt', 'SGST (%)', 'CGST (%)', igst_pcnt_const, utgst_pcnt_const, 'Total']
    if ean_flag:
        table_headers.insert(1, 'EAN')
        configurations['show_ean'] = True
    if show_cess_tax:
        table_headers.insert(table_headers.index(utgst_pcnt_const), 'CESS (%)')
        configurations['show_cess_tax'] = True
    if show_apmc_tax:
        table_headers.insert(table_headers.index(utgst_pcnt_const), 'APMC (%)')
        configurations['show_apmc_tax'] = True
    if display_remarks == 'true':
        table_headers.append('Remarks')
        configurations['display_remarks'] = True
    show_mrp = misc_dict.get('show_mrp_raise_po', 'false')
    if show_mrp == 'true' and industry_type == 'FMCG':
        table_headers.insert(table_headers.index(unit_price_const), 'MRP ({})'.format(supplier_currency))
        configurations['show_mrp'] = True
    table_headers[table_headers.index(unit_price_const)] = 'Unit Price ({})'.format(supplier_currency)
    table_headers[table_headers.index('Amt')] = AMT_CONSTANT.format(supplier_currency)
    table_headers[table_headers.index('Total')] = 'Total ({})'.format(supplier_currency)

    return table_headers

def get_po_suggestions(request, warehouse, misc_dict, key, value, supplier, terms_condition, supplier_currency, inco_terms, sku_pack_mappping):
    price = value['price']
    sku_id = SKUMaster.objects.filter(wms_code=key[0], user=warehouse.id)
    purchase_number = request.POST.get('purchase_number', '')
    po_suggestions = copy.deepcopy(PO_SUGGESTIONS_DATA)
    if not sku_id:
        sku_id = SKUMaster.objects.filter(wms_code='TEMP', user=warehouse.id)
        po_suggestions['wms_code'] = key[0]
        po_suggestions['supplier_code'] = value['supplier_code']
        mandate_supplier = misc_dict.get('mandate_sku_supplier', 'false')
        if sku_id[0].wms_code != 'TEMP' and mandate_supplier != 'true':
            supplier_mapping = SKUSupplier.objects.filter(sku=sku_id[0], supplier_id=supplier.id,
                                                            sku__user=warehouse.id)
            sku_mapping = {'supplier_id': supplier.id, 'sku': sku_id[0], 'preference': 1, 'moq': 0,
                            'supplier_code': value['supplier_code'], 'price': price,
                            'creation_date': datetime.datetime.now(),
                            'updation_date': datetime.datetime.now()}
            if supplier_mapping:
                supplier_mapping = supplier_mapping[0]
                if sku_mapping['supplier_code'] and supplier_mapping.supplier_code != sku_mapping[
                    'supplier_code']:
                    supplier_mapping.supplier_code = sku_mapping['supplier_code']
                    supplier_mapping.save()
            else:
                new_mapping = SKUSupplier(**sku_mapping, account_id=warehouse.userprofile.id)
                new_mapping.save()
    po_uom_qty = sku_pack_mappping.get(sku_id[0].sku_code, {}).get('pack_quantity', 1)
    po_suggestions['wms_code'] = purchase_number
    po_suggestions['sku_id'] = sku_id[0].id
    po_suggestions['supplier_id'] = supplier.id
    po_suggestions['order_quantity'] = float(value['order_quantity'])
    po_suggestions['free_quantity'] = float(value.get('free_quantity',0))
    po_suggestions['price'] = float(price)
    po_suggestions['status'] = 'Manual'
    po_suggestions['remarks'] = value['remarks']
    po_suggestions['measurement_unit'] = "UNITS"
    po_suggestions['sgst_tax'] = value['sgst_tax']
    po_suggestions['cgst_tax'] = value['cgst_tax']
    po_suggestions['igst_tax'] = value['igst_tax']
    po_suggestions['cess_tax'] = value['cess_tax']
    po_suggestions['utgst_tax'] = value['utgst_tax']
    po_suggestions['apmc_tax'] = value['apmc_tax']
    po_suggestions['ship_to'] = value['ship_to']
    po_suggestions['terms'] = terms_condition
    po_suggestions['po_name'] = value['po_name']
    po_suggestions['json_data'] ={
        "weight": value.get("weight", ""),
        "po_type": value.get("po_type", ""),
        "created_by": request.user.username,
        "created_from": "WEB",
        "po_currency": supplier_currency
    }
    if inco_terms:
        po_suggestions['json_data'].update({'inco_terms': inco_terms})
    if value['po_delivery_date']:
        po_suggestions['delivery_date'] = value['po_delivery_date']
    if value['measurement_unit']:
        po_suggestions['measurement_unit'] = value['measurement_unit']
    po_suggestions['account_id'] = warehouse.userprofile.id

    return po_suggestions, po_uom_qty

def get_potempdata(table_headers, industry_type, ean_flag, purchase_order, show_apmc_tax, show_cess_tax, po_temp_data, misc_dict, supplier_currency, sku_attributes_data_dict ):
    sku_attributes_keys = ['Make', 'Model', 'Variant', 'Year']
    wms_code = purchase_order.wms_code
    ean_number = ''
    if ean_flag:
        eans = get_sku_ean_list(purchase_order.sku)
        if eans:
            ean_number = eans[0]
        po_temp_data.insert(1, ean_number)
    if show_cess_tax:
        po_temp_data.insert(table_headers.index(cess_pcnt_const), purchase_order.cess_tax)
    if show_apmc_tax:
        po_temp_data.insert(table_headers.index(apmc_pcnt_const), purchase_order.apmc_tax)
    display_remarks = misc_dict.get('display_remarks_mail', 'false')
    if display_remarks == 'true':
        po_temp_data.append(purchase_order.remarks)
    show_mrp = misc_dict.get('show_mrp_raise_po', 'false')
    if show_mrp == 'true' and industry_type == 'FMCG':
        po_temp_data.insert(table_headers.index(mrp_const.format(supplier_currency)), purchase_order.mrp)
    if sku_attributes_data_dict:
        for attr in sku_attributes_keys:
            if attr in table_headers:
                po_temp_data.insert(table_headers.index(attr), sku_attributes_data_dict.get(wms_code, {}).get(attr, ''))
    return ean_number, po_temp_data

def update_purchase_order(request, inco_terms, supplier_payment_terms, sku_dict, key, value, terms_condition, warehouse, supplier_currency):
    sku_id = ''
    purchase_order = OpenPO.objects.get(id=value['data_id'], sku__user=warehouse.id)
    setattr(purchase_order, 'order_quantity', value['order_quantity'])
    if not value['price']:
        value['price'] = 0
    if purchase_order.sku.sku_code != key[0]:
        sku_id = sku_dict.get(key[0], '')
        if sku_id:
            setattr(purchase_order, 'sku_id', sku_id)
    setattr(purchase_order, 'price', value['price'])
    setattr(purchase_order, 'mrp', value['mrp'])
    setattr(purchase_order, 'po_name', value['po_name'])
    setattr(purchase_order, 'supplier_code', value['supplier_code'])
    setattr(purchase_order, 'remarks', value['remarks'])
    setattr(purchase_order, 'sgst_tax', value['sgst_tax'])
    setattr(purchase_order, 'cgst_tax', value['cgst_tax'])
    setattr(purchase_order, 'igst_tax', value['igst_tax'])
    setattr(purchase_order, 'cess_tax', value['cess_tax'])
    setattr(purchase_order, 'utgst_tax', value['utgst_tax'])
    setattr(purchase_order, 'apmc_tax', value['apmc_tax'])
    setattr(purchase_order, 'ship_to', value['ship_to'])
    setattr(purchase_order, 'terms', terms_condition)
    setattr(purchase_order, 'free_quantity', value.get('free_quantity', 0))
    setattr(purchase_order, 'measurement_unit', value['measurement_unit'])
    json_data = {
        "weight": value.get("weight", ""),
        "created_by": request.user.username,
        "created_from": "WEB",
        "po_currency": supplier_currency
    }
    if inco_terms:
        json_data['inco_terms'] =  inco_terms
    if supplier_payment_terms:
        json_data['supplier_payment_terms'] = supplier_payment_terms
    if purchase_order.json_data:
        po_uom_qty = purchase_order.json_data.get('pcf', 1)
    purchase_order.json_data.update(json_data)
    if value['po_delivery_date']:
        setattr(purchase_order, 'delivery_date', value['po_delivery_date'])
    purchase_order.account_id = warehouse.userprofile.id
    purchase_order.save()
    return po_uom_qty, purchase_order

def validate_po_params(request, warehouse):
    ean_flag = False
    po_dict = dict(request.POST.lists())
    supplier_id = request.POST.get('supplier_id', '')
    po_type = request.POST.get('po_type', '')
    base_currency = warehouse.userprofile.base_currency
    supplier = SupplierMaster.objects.get(user=warehouse.id, supplier_id=supplier_id)
    supplier_currency = supplier.currency_code
    if not supplier_currency:
        supplier_currency = base_currency
    timezone = TIMEZONE
    if warehouse.userprofile.timezone:
        timezone = warehouse.userprofile.timezone
    error = ''
    if po_type.lower() == "import" and supplier.supplier_type != 'import':
        error = PO_TYPE_MISMATCH_ERROR
    elif supplier.supplier_type == 'import' and po_type.lower() != 'import':
        error = SUPPLIER_TYPE_MISMATCH_ERROR
    elif po_type.lower() == "import" and supplier.supplier_type == 'import':
        error = CURRENCY_EXCHANGE_ERROR
        today = datetime.datetime.now().date()
        currency_exchange = CurrencyExchangeMaster.objects.filter(from_currency=supplier_currency, to_currency=base_currency, warehouse=warehouse, start_date__lte=today)
        if currency_exchange.exists():
            if currency_exchange.last().end_date:
                end_date = get_local_date_known_timezone(timezone, currency_exchange.last().end_date, send_date=True).date()
                if today <= end_date:
                    error = ''
            else:
                error = ''
    ean_data = SKUMaster.objects.filter(
            Q(ean_number__gt=0) | Q(eannumbers__ean_number__gt=0), wms_code__in=po_dict['wms_code'], user=warehouse.id
        ).values_list('ean_number')
    if ean_data:
        ean_flag = True   

    #Check if po is already created for the given ids
    open_po_ids = [po_id for po_id in po_dict['data-id'] if po_id]
    existing_skus = list(PurchaseOrder.objects.filter(open_po_id__in=open_po_ids, open_po__sku__user=warehouse.id).values_list('open_po__sku__sku_code', flat=True))
    if existing_skus:
        error = 'PO already confirmed for SKUs: %s' %(','.join(existing_skus))
    
    return supplier, supplier_currency, po_type, ean_flag, error

def get_sku_attributes(sku_attributes_data):
    sku_attributes_data_dict = {}
    for attribute_data in sku_attributes_data:
        sku_attr_data = attribute_data['sku__sku_code']
        sku_attr_name = attribute_data['attribute_name']
        sku_attr_value = attribute_data['attribute_value']
        if sku_attr_data in sku_attributes_data_dict:
            if sku_attr_name in sku_attributes_data_dict[sku_attr_data]:
                sku_attributes_data_dict[sku_attr_data][sku_attr_name] += ', ' + sku_attr_value
            else:
                sku_attributes_data_dict[sku_attr_data].update({sku_attr_name : sku_attr_value})
        else:
            sku_attributes_data_dict[sku_attr_data] = {sku_attr_name : sku_attr_value}
    return sku_attributes_data_dict


def get_tax_details(value, amount, purchase_order, tax, show_cess_tax):
    total = 0
    total_qty = 0
    if value['cess_tax']:
        show_cess_tax = True
        tax += value['cess_tax']
    if not tax:
        total += amount
    else:
        total += amount + ((amount / 100) * float(tax))
    total_qty += float(purchase_order.order_quantity)
    if purchase_order.sku.wms_code == 'TEMP':
        wms_code = purchase_order.wms_code
    else:
        wms_code = purchase_order.sku.wms_code

    total_tax_amt = (
        purchase_order.utgst_tax + purchase_order.sgst_tax + purchase_order.cgst_tax + purchase_order.igst_tax
            + purchase_order.cess_tax + purchase_order.utgst_tax + purchase_order.apmc_tax) * (amount/100)
    total_sku_amt = total_tax_amt + amount

    return wms_code, total_sku_amt, tax, total_tax_amt, show_cess_tax, total_qty, total

def create_po_file_mapping(warehouse, purchase_number, full_po_number, transact_type):
    # Filter MasterDocs objects
    master_objs = MasterDocs.objects.filter(
        master_type=transact_type,
        user=warehouse.id,
        master_id=purchase_number
    )

    # Update master_id with full_po_number
    master_objs.update(master_id=full_po_number)

def validate_sku_pack_data(dest_user, po_type, misc_dict, sku_codes, measurement_units,
    input_data, is_items_data=True):
    """
    Validates that pack ids for SKUs exist in both purchase and sales UOM dictionaries.

    :param warehouse: Source warehouse
    :param dest_user: Destination warehouse/user
    :param po_type: PO type (e.g., 'stocktransfer')
    :param misc_dict: Miscellaneous config dictionary
    :param sku_codes: List of SKUs
    :param measurement_units: Measurement unit data
    :param sku_wise_measurement_units: SKU-wise mapping for measurement units
    :param input_data: Data to extract SKU from
    :param is_items_data: True if input_data is list of items, False if it's dict keys
    :return: (error messages, sku_po_uom_dict)
    """
    error_messages = []
    sales_uom_dict = {}
    purchase_uom = misc_dict.get('enable_purchase_uom', 'false')
    if purchase_uom == 'true' and po_type.lower() == 'stocktransfer' and dest_user:
        sales_uom_dict = get_filtered_sku_pack_data(
            dest_user, sku_codes, measurement_units, 'sales'
        )

        data_iter = input_data if is_items_data else input_data.keys()

        for entry in data_iter:
            sku_code = entry[0]
            measurement_unit = input_data[entry].get('measurement_unit')
            if measurement_unit and not sales_uom_dict.get(sku_code, {}).get(measurement_unit,''):
                error_messages.append(
                    f"Mentioned pack id for {sku_code} is not available in destination warehouse"
                )

    return error_messages

@get_warehouse
def confirm_po(request, warehouse):
    data = copy.deepcopy(PO_DATA)
    terms_condition = request.POST.get('terms_condition', '')
    po_data = []
    code = ''
    po_data_list = []
    configurations = {}
    industry_type = warehouse.userprofile.industry_type
    po_dict = dict(request.POST.lists())
    misc_types = ['display_remarks_mail', 'raise_po', 'allow_secondary_emails', 'PO', 'show_mrp_raise_po', 'enable_purchase_uom']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    display_remarks = misc_dict.get('display_remarks_mail', 'false')
    inco_terms = po_dict.get('inco_terms', [])
    supplier_payment_terms = request.POST.get('supplier_payment_terms', '')
    timezone = get_user_time_zone(warehouse)
    base_currency = warehouse.userprofile.base_currency
    create_order_dict = {}
    request_user = request.user.username
    is_stocktransfer = False
    destnation_sku_obj = {}

    supplier, supplier_currency, po_type, ean_flag, error = validate_po_params(request, warehouse)
    if error:
        return HttpResponse(error)

    create_so_dict = {'create_sale_order': True, 'supplier_id': supplier.supplier_id, 'po_type': po_type}
    po_type, create_so_for_st_po, reverse_order_create, _ = get_po_type(create_so_dict, sales_data={}, api_call=False, internal_call=True)

    error, userwise_st_customer, customer_data_check, dest_user = po_type_validations(
        create_so_dict, base_currency, supplier_currency, po_type, create_so_for_st_po,
        reverse_order_create, timezone, warehouse)
    if error:
        return JsonResponse({'message': error}, status=400)

    all_data, show_cess_tax, show_apmc_tax = get_raisepo_group_data(warehouse, po_dict, mrp_check=True, timezone=timezone)

    sku_code = list(all_data.keys())[0][0]
    po_id, prefix, full_po_number, check_prefix, inc_status = get_user_prefix_incremental(warehouse, 'po_prefix', sku_code)
    if inc_status:
        return HttpResponse("Prefix not defined")

    # SKU Attributes data
    sku_attributes_data = SKUAttributes.objects.using(reports_database).filter(
        sku__user=warehouse.id, sku__sku_code__in=po_dict['wms_code']
        ).values('sku__sku_code', 'attribute_name', 'attribute_value')
    
    sku_attributes_data_dict = get_sku_attributes(sku_attributes_data)

    table_headers = update_table_configuration(configurations, ean_flag, show_cess_tax, show_apmc_tax, display_remarks, misc_dict, industry_type, supplier_currency)

    sku_codes = [key[0] for key in all_data.keys()]
    sku_dict = dict(SKUMaster.objects.filter(sku_code__in=sku_codes, user=warehouse.id).values_list('sku_code', 'id'))
    
    #Fetching sku uom details to validate if same pack id is available in destination warehouse
    sku_po_uom_dict, measurement_units = get_max_pack_uom_quantity(warehouse, sku_codes, misc_dict)

    error_messages = validate_sku_pack_data(dest_user, po_type, misc_dict,
            sku_codes, measurement_units,
            all_data, is_items_data=True)

    if error_messages:
        return JsonResponse({'message': error_messages[0]}, status=400)

    total_taxable_amt = 0
    total_tax_amt_ = 0

    if customer_data_check and dest_user\
                    and reverse_order_create and create_so_for_st_po:
        is_stocktransfer =True
        destnation_sku_obj = {
        sku.sku_code: sku
        for sku in SKUMaster.objects.filter(sku_code__in=sku_codes, user=dest_user.id)
    }

    for key, value in all_data.items():
        if not value.get('po_name',''):
            value['po_name'] = full_po_number
        if value['data_id']:
            sup_id = value['data_id']
            po_uom_qty, purchase_order = update_purchase_order(request, inco_terms, supplier_payment_terms, sku_dict, key, value, terms_condition, warehouse, supplier_currency)
        else:
            po_suggestions, po_uom_qty = get_po_suggestions(request, warehouse, misc_dict, key, value, supplier, terms_condition, supplier_currency, inco_terms, sku_po_uom_dict)
            data1 = OpenPO(**po_suggestions)
            data1.save()

            purchase_order = OpenPO.objects.get(id=data1.id, sku__user=warehouse.id)
            sup_id = purchase_order.id
        data['open_po_id'] = sup_id
        data['order_id'] = po_id
        data['ship_to'] = value['ship_to']
        data['prefix'] = prefix
        data['po_number'] = full_po_number
        data['po_type'] = po_type
        data['account_id'] = warehouse.userprofile.id
        data['pcf'] = po_uom_qty
        data['line_reference'] = value.get('line_reference', '')
        order = PurchaseOrder(**data)
        order.save()

        amount = float(purchase_order.order_quantity) * float(purchase_order.price)
        tax = value['sgst_tax'] + value['cgst_tax'] + value['igst_tax'] + value['utgst_tax'] + value['apmc_tax']

        wms_code, total_sku_amt, tax, total_tax_amt, show_cess_tax, total_qty, total  = get_tax_details(value, amount, purchase_order, tax, show_cess_tax)

        total_taxable_amt += amount
        total_tax_amt_ += total_tax_amt
        measurement_unit = ''
        if is_stocktransfer:
            create_order_dict, measurement_unit = frame_order_payload(key, value, value['price'], value['mrp'], full_po_number,
                    userwise_st_customer, create_order_dict, dest_user, request_user, destnation_sku_obj.get(key[0]), sku_po_uom_dict)


        po_sku_data = {}
        sku = purchase_order.sku
        # removed value['supplier_code'] for now
        if industry_type == 'FMCG':
            po_temp_data = [
                            wms_code, purchase_order.sku.sku_desc, purchase_order.sku.sku_size,
                            purchase_order.order_quantity, value['measurement_unit'], purchase_order.price,
                            round(amount, 2), round(purchase_order.sgst_tax, 2),
                            round(purchase_order.cgst_tax, 2),
                            round(purchase_order.igst_tax, 2), round(purchase_order.utgst_tax, 2),
                            round(total_sku_amt, 2)]
        else:
            po_temp_data = [
                            wms_code, purchase_order.sku.sku_desc, purchase_order.sku.sku_size, value['supplier_code'],
                            purchase_order.order_quantity,value['measurement_unit'],
                            purchase_order.price, round(amount, 2), round(purchase_order.sgst_tax, 2),
                            round(purchase_order.cgst_tax, 2),
                            round(purchase_order.igst_tax, 2), round(purchase_order.utgst_tax, 2),
                            round(total_sku_amt, 2)]

        ean_number, po_temp_data = get_potempdata(
        table_headers, industry_type, ean_flag, purchase_order, show_apmc_tax,
        show_cess_tax, po_temp_data, misc_dict, supplier_currency, sku_attributes_data_dict
        )
        po_data.append(po_temp_data)

        total_sgst = purchase_order.sgst_tax * (amount/100)
        total_cgst = purchase_order.cgst_tax * (amount/100)
        total_igst = purchase_order.igst_tax * (amount/100)
        po_sku_data = {
            'sku_code': wms_code,
            'sku_desc': sku.sku_desc,
            'sku_size': sku.sku_size,
            'ean_number': ean_number,
            'hsn_code': sku.hsn_code,
            'supplier_code': value['supplier_code'],
            'delivery_schedule': '',
            'quantity': purchase_order.order_quantity,
            'uom': measurement_unit or value['measurement_unit'],
            'weight': value.get('weight', ''),
            'mrp': round(purchase_order.mrp, 2),
            'unit_price': round(float(purchase_order.price), 2),
            'amount': round(amount, 2),
            'sgst_tax': purchase_order.sgst_tax, 'sgst_amt': round(total_sgst, 2),
            'cgst_tax': purchase_order.cgst_tax, 'cgst_amt': round(total_cgst,2),
            'igst_tax': purchase_order.igst_tax, 'igst_amt': round(total_igst,2),
            'cess_tax': purchase_order.cess_tax, 'apmc_tax': purchase_order.apmc_tax,
            'total': round(total_sku_amt, 2),
            'remarks': purchase_order.remarks,
            'sku_attributes_dict': sku_attributes_data_dict.get(wms_code, {}),
        }
        po_data_list.append(po_sku_data)

        suggestion = OpenPO.objects.get(id=sup_id, sku__user=warehouse.id)
        setattr(suggestion, 'status', 0)
        suggestion.save()

    extra_fields = {}
    po_attributes = [field for field in request.POST if field.startswith('attr_')]
    for field in po_attributes:
        field_key = field.split('attr_')[1]
        extra_fields[field_key] = request.POST.get(field)
    # Saving PO extra fields
    if extra_fields:
        save_or_update_po_extra_fields(warehouse.id, extra_fields, po_number=full_po_number)

    # Fetching Sale Order extra fields of mapped Order
    so_extra_fields = {}
    po_order_ref_field = list(UserAttributes.objects.filter(attribute_model='purchase_order',
                attribute_type=wms_field_const, attribute_values='order_reference', status=1, user=warehouse.id
                ).values_list('attribute_name', flat=True))
    if po_order_ref_field:
        po_order_ref_field = po_order_ref_field[0]
        # Getting mapped Order Reference
        order_reference = extra_fields.get(po_order_ref_field, '')
        original_order_id = list(OrderDetail.objects.filter(order_reference=order_reference, user=warehouse.id).values_list('original_order_id', flat=True))
        original_order_id = original_order_id[0] if original_order_id else ''
        so_extra_fields = dict(OrderFields.objects.filter(original_order_id=original_order_id, user=warehouse.id).values_list('name', 'value'))

    address = purchase_order.supplier.address
    address = '\n'.join(address.split(','))
    if purchase_order.ship_to:
        ship_to_address = purchase_order.ship_to
        company_address = warehouse.userprofile.address
    else:
        ship_to_address, company_address = get_purchase_company_address(warehouse.userprofile)
    ship_to_address = '\n'.join(ship_to_address.split(','))
    wh_telephone = warehouse.userprofile.wh_phone_number
    wh_pan = warehouse.userprofile.pan_number
    telephone = purchase_order.supplier.phone_number
    wh_gstin = warehouse.userprofile.gst_number,
    name = purchase_order.supplier.name
    code = purchase_order.supplier.supplier_id
    supplier_email = purchase_order.supplier.email_id
    supplier_pan = purchase_order.supplier.pan_number
    secondary_supplier_email = list(MasterEmailMapping.objects.filter(
        master_id=supplier, user=warehouse.id, master_type='supplier').values_list('email_id',flat=True).distinct())
    supplier_email_id =[]
    supplier_email_id.insert(0,supplier_email)
    supplier_email_id.extend(secondary_supplier_email)
    gstin_no = purchase_order.supplier.tin_number
    order_id = po_id
    order_date = get_local_date(request.user, order.creation_date)
    vendor_name = ''
    vendor_address = ''
    vendor_telephone = ''

    po_reference = order.po_number

    profile = UserProfile.objects.get(user=warehouse.id)

    company_name = profile.company.company_name
    title = 'Purchase Order'
    receipt_type = request.POST.get('receipt_type', '')
    company_logo = get_po_company_logo(warehouse, COMPANY_LOGO_PATHS, request)
    iso_company_logo = get_po_company_logo(warehouse, ISO_COMPANY_LOGO_PATHS, request)
    left_side_logo = get_po_company_logo(warehouse, LEFT_SIDE_COMPNAY_LOGO , request)
    total_amt_in_words = number_in_words(round(total)) + ' ONLY'
    round_value = float(round(total) - float(total))
    data_dict = {'table_headers': table_headers, 'data': po_data, 'data_dict': po_data_list, 'address': address,
                 'order_id': order_id, 'telephone': str(telephone),
                 'name': name, 'code':code, 'order_date': order_date, 'total': total,
                 'supplier_pan': supplier_pan,
                 'po_reference': po_reference, 'company_name': company_name,
                 'po_number': po_reference,
                 'location': profile.location, 'vendor_name': vendor_name,
                 'vendor_address': vendor_address, 'vendor_telephone': vendor_telephone,
                 'total_qty': total_qty, 'receipt_type': receipt_type,
                 'title': title, 'ship_to_address': ship_to_address,
                 'gstin_no': gstin_no, 'w_address': ship_to_address,
                 'wh_telephone': wh_telephone, 'terms_condition' : terms_condition,
                 'wh_email': warehouse.email, 'wh_pan': wh_pan, 'wh_gstin': wh_gstin,
                 'total_amt_in_words' : total_amt_in_words,
                 'company_address': company_address,
                 'company_logo': company_logo, 'iso_company_logo': iso_company_logo,'left_side_logo':left_side_logo,
                 'total_taxable_amt': total_taxable_amt, 'total_tax_amt': total_tax_amt_,
                 'po_currency': supplier_currency, 'inco_terms': ','.join(inco_terms),
                 'supplier_payment_terms': supplier_payment_terms,
                 'extra_fields': extra_fields, 'configurations': configurations,
                 'so_extra_fields': so_extra_fields,
                }
    # netsuite_po(order_id, user, purchase_order, data_dict, po_reference)
    if round_value:
        data_dict['round_total'] = "%.2f" % round_value
    # Config based PO doc
    rendered_dict = {'po_data_list':[data_dict]}
    invoice_format = misc_dict.get('PO', 'false')
    filter_dict = {'document_type': 'PO', 'invoice_format': invoice_format}
    rendered_data = get_custom_html_format(filter_dict, rendered_dict, warehouse.id)
    if rendered_data:
        rendered = rendered_data
    else:
        t = loader.get_template(po_pdf_const)
        rendered = t.render(data_dict)
    if misc_dict.get('raise_po', 'false') == 'true':
        data_dict_po = {
            'contact_no': profile.wh_phone_number, 'contact_email': warehouse.email,
            'gst_no': profile.gst_number,'supplier_name':purchase_order.supplier.name,
            'billing_address': profile.address, 'shipping_address': profile.wh_address,
            'table_headers': table_headers, 'po_currency': supplier_currency,
        }
        write_and_mail_pdf(
            po_reference, rendered, warehouse, supplier_email,
            telephone, po_data, str(order_date).split(' ')[0],
            ean_flag=ean_flag, data_dict_po=data_dict_po,
            )
 
    check_purchase_order_created(warehouse, po_id, check_prefix)
    update_po_header([full_po_number], warehouse.id)
    #Save PO File mapping 
    create_po_file_mapping(warehouse, purchase_order.wms_code, full_po_number, 'PO')

    response_dict = {'message': 'Success', 'po_number':str(full_po_number), 'show_pdf': True}

    #Create Sale Order
    check_and_create_sale_order(dest_user, create_so_for_st_po, create_order_dict )

    return JsonResponse(response_dict, status=200)

def check_customer_data(warehouse, supplier, reverse_order_create):
    error= ""
    customer_data_check= False
    dest_user = ''
    userwise_st_customer = {}
    if not customer_data_check :
        dest_user_obj= User.objects.filter(username=supplier.supplier_id)
        if dest_user_obj.exists():
            dest_user= dest_user_obj[0]
            if reverse_order_create:
                customer_objs = CustomerMaster.objects.filter(user=dest_user.id, customer_type="Stock Transfer", customer_code__in = [warehouse.username])
                if customer_objs.exists():
                    each_customer = customer_objs[0]
                    userwise_st_customer ={
                        "customer_id": each_customer.customer_id,
                        "name": each_customer.name,
                        "email_id": each_customer.email_id,
                        "phone_number": each_customer.phone_number,
                        "customer_code": each_customer.customer_code,
                        "supplier_tax_type": supplier.tax_type,
                        "id": each_customer.id

                    }
                    customer_data_check =True
                else:
                    error= "Stock Transfer Customer is not Available in Source Warehouse"
                    log.info("Customer is not created in Source Warehouse")
        else:
            error= "Please select Stock Transfer Supplier"
    else:
        error= "Please select Stock Transfer Supplier"
    return error, userwise_st_customer, customer_data_check, dest_user


def po_type_validations(po_dict, base_currency, supplier_currency, po_type, create_so_for_st_po, reverse_order_create, timezone, warehouse):
    '''
        Validating Supplier, CustomerMaster for STPO, Currency Exchange Master for Import PO
    '''
    dest_user = ''
    supplier_id_input= po_dict.get("supplier_id")
    error= ""
    customer_data_check= False
    userwise_st_customer = {}
    if supplier_id_input:
        supplier = SupplierMaster.objects.get(user=warehouse.id, supplier_id=supplier_id_input)
        if not supplier_currency:
            supplier_currency = supplier.currency_code
        if not supplier_currency:
            supplier_currency = base_currency
        if create_so_for_st_po:
            error, userwise_st_customer, customer_data_check, dest_user = check_customer_data(warehouse, supplier, reverse_order_create)
        elif po_type.lower() == "stocktransfer" and not reverse_order_create:
            dest_user_obj = User.objects.filter(username=supplier.supplier_id)
            if dest_user_obj.exists():
                dest_user = dest_user_obj[0]
            else:
                dest_user = None
        elif po_type.lower() == "import" and supplier.supplier_type != 'import':
            error = PO_TYPE_MISMATCH_ERROR
        elif supplier.supplier_type == 'import' and po_type.lower() != 'import':
            error = SUPPLIER_TYPE_MISMATCH_ERROR
        elif po_type.lower() == "import" and supplier.supplier_type == 'import':
            error = CURRENCY_EXCHANGE_ERROR
            today = datetime.datetime.now().date()
            currency_exchange = CurrencyExchangeMaster.objects.filter(
                from_currency=supplier_currency, to_currency=base_currency, warehouse=warehouse, start_date__lte=today)
            if currency_exchange.exists():
                if currency_exchange.last().end_date:
                    end_date = get_local_date_known_timezone(
                        timezone, currency_exchange.last().end_date, send_date=True).date()
                    if today <= end_date:
                        error = ''
                else:
                    error = ''

    return error, userwise_st_customer, customer_data_check, dest_user


def frame_order_payload(key, value, price, mrp, full_po_number, userwise_st_customer, create_order_dict, dest_user, request_user, destnation_sku_obj=None, sku_po_uom_dict={}):
    today_date= datetime.datetime.now()
    if destnation_sku_obj:
        if userwise_st_customer.get("supplier_tax_type", "") == "intra_state":
            cgst_tax = sgst_tax = igst_tax = utgst_tax = cess_tax = 0
        else:
            cgst_tax, sgst_tax, igst_tax, utgst_tax, cess_tax = (
                value['cgst_tax'],
                value['sgst_tax'],
                value['igst_tax'],
                value['utgst_tax'],
                value['cess_tax']
            )
        measurement_unit = value.get('measurement_unit', 'UNITS')
        sku_code = destnation_sku_obj.sku_code
        pack_data = sku_po_uom_dict.get(sku_code, {})
        
        if pack_data:
            pack_id_mapping = pack_data.get(measurement_unit,{})
            if pack_id_mapping:
                pack_id = measurement_unit
                pack_quantity = pack_id_mapping
            else:
                max_value = max(pack_data.values())
                pack_quantity = max_value
                pack_id = [k for k, v in pack_data.items() if v == max_value][0]
        else:
            pack_id = ''
            pack_quantity = 1
        measurement_unit = pack_id
        aux_data = value.get("aux_data", {})
        line_reference = aux_data.get("line_reference", "")
        order_json_data = {
            "po_line_reference": str(line_reference),
            "line_reference": str(line_reference),
            "source": 'WEB-UI',
            'weight': value.get("weight", ""),
            "request_user": request_user
        }
        if pack_id:
            order_json_data['pack_id'] = pack_id
            order_json_data['pack_uom_quantity'] = pack_quantity
        order_dict_payload={
            "order_detail": {
                'user': dest_user.id,
                'status': '1',
                'customer_identifier_id' : userwise_st_customer.get('id'),
                'customer_id': userwise_st_customer.get("customer_id", ""),
                'customer_name': userwise_st_customer.get("name", ""),
                'email_id': userwise_st_customer.get("email_id", ""),
                'telephone': userwise_st_customer.get("phone_number", ""),
                'sku_id': destnation_sku_obj.id,
                'title': destnation_sku_obj.sku_desc,
                'quantity': value['order_quantity'],
                'original_quantity': value['order_quantity'],
                'marketplace': '',
                'unit_price': float(price),
                'mrp': float(mrp),
                'order_reference': full_po_number,
                'slot_from': today_date,
                'slot_to': today_date,
                'shipment_date': today_date,
                'order_type': 'StockTransfer',
                'json_data': order_json_data,
                "line_reference": str(line_reference),
                "sku_code": sku_code
            },
            'customer_order_summary': {
                "order_id": None,
                'cgst_tax': cgst_tax,
                'sgst_tax': sgst_tax,
                'igst_tax': igst_tax,
                'utgst_tax': utgst_tax,
                'cess_tax':  cess_tax,
                "mrp": float(mrp),
            },
            'seller_order':{
                "seller_id": None,
                "order_id":  None,
                "quantity": value['order_quantity'],
                "creation_date": today_date,
                'order_status': 'PENDING',
            },
            'order_header': {
                'warehouse_id': dest_user.id,
                'order_reference': full_po_number,
                'order_type': 'StockTransfer',
                'status': '1',
                'customer_id': userwise_st_customer.get('id'),
            },
        }
        if (dest_user.id, full_po_number, "") in  create_order_dict:
            create_order_dict[(dest_user.id, full_po_number, "")].append(order_dict_payload)
        else:
            create_order_dict[(dest_user.id, full_po_number, "")]= [order_dict_payload]

    return create_order_dict, measurement_unit


def frame_po_headers(configurations, misc_dict):
    
    display_remarks = misc_dict.get('display_remarks_mail', 'false')
    table_headers = [
            sku_code_const, 'Desc', sku_size_const, 'Delivery Schedule', 'HSN Code', 'Supplier Code', 'Qty', 'UOM', unit_price_const, 'Amt',
            sgst_pcnt_const, 'SGST Amt', csgt_pcnt_const, 'CGST Amt', igst_pcnt_const, 'IGST Amt', 'Total']
        
    currency_headers = [unit_price_const, 'Amt', 'SGST Amt', 'CGST Amt', 'IGST Amt', 'Total']
    if misc_dict['show_cess_tax']:
        table_headers.insert(table_headers.index(igst_pcnt_const), cess_pcnt_const)
        configurations['show_cess_tax'] = True
    if misc_dict['show_apmc_tax']:
        table_headers.insert(table_headers.index(igst_pcnt_const), apmc_pcnt_const)
        configurations['show_apmc_tax'] = True
    if display_remarks == 'true':
        table_headers.append('Remarks')
        configurations['display_remarks'] = True
    if misc_dict['show_mrp'] == 'true':
        table_headers.insert(table_headers.index('Amt'), mrp_const.format(misc_dict['supplier_currency']))
        configurations['show_mrp'] = True

    for each_header in currency_headers:
        table_headers[table_headers.index(each_header)] = each_header + ' ({})'.format(misc_dict['supplier_currency'])

    return table_headers, configurations


def frame_po_temp_data(purchase_order, po_suggestions, value, sku_obj, table_headers, extra_dict):

    amount = extra_dict['amount']
    wms_code = extra_dict['wms_code']
    sku_attributes_keys = extra_dict['sku_attributes_keys']
    sku_attributes_data_dict = extra_dict['sku_attributes_data_dict']
    
    po_sku_data = {}
    total_tax_amt = (purchase_order.utgst_tax + purchase_order.sgst_tax + purchase_order.cgst_tax + purchase_order.igst_tax + purchase_order.cess_tax + purchase_order.apmc_tax + purchase_order.utgst_tax) * (amount/100)
    total_sgst = purchase_order.sgst_tax * (amount/100)
    total_cgst = purchase_order.cgst_tax * (amount/100)
    total_igst = purchase_order.igst_tax * (amount/100)
    total_sku_amt = total_tax_amt + amount
    hsn_code= str(purchase_order.sku.hsn_code).split("_")[0] if (purchase_order.sku.hsn_code and purchase_order.sku.hsn_code not in ["0"]) else ""
    # supplier_code, '' removing these two values for now
    po_temp_data = [
        wms_code, purchase_order.sku.sku_desc, purchase_order.sku.sku_size,'', hsn_code, 'sup_code',
        purchase_order.order_quantity, po_suggestions['measurement_unit'],
        round(purchase_order.price, 2), round(amount, 2), purchase_order.sgst_tax, round(total_sgst,2),
        purchase_order.cgst_tax, round(total_cgst,2),
        purchase_order.igst_tax, round(total_igst, 2),
        round(total_sku_amt, 2)
        ]

    if extra_dict.get('show_cess_tax'):
        po_temp_data.insert(table_headers.index(cess_pcnt_const), purchase_order.cess_tax)
    if extra_dict.get('show_apmc_tax'):
        po_temp_data.insert(table_headers.index(apmc_pcnt_const), purchase_order.apmc_tax)
    if extra_dict.get('display_remarks') == 'true':
        po_temp_data.append(purchase_order.remarks)
    if extra_dict.get('show_mrp'):
        po_temp_data.insert(table_headers.index(mrp_const.format(extra_dict['supplier_currency'])), round(purchase_order.mrp, 2))
    if sku_attributes_data_dict:
        for attr in sku_attributes_keys:
            if attr in table_headers:
                po_temp_data.insert(table_headers.index(attr), sku_attributes_data_dict.get(wms_code, {}).get(attr, ''))

    po_sku_data = {
        'sku_code': wms_code,
        'sku_desc': sku_obj.sku_desc,
        'sku_size': sku_obj.sku_size,
        'ean_number': extra_dict['ean_number'],
        'hsn_code': hsn_code,
        'supplier_code': purchase_order.supplier.supplier_id,
        'delivery_schedule': '',
        'quantity': purchase_order.order_quantity,
        'uom': po_suggestions['measurement_unit'],
        'weight': value.get('weight', ''),
        'mrp': round(purchase_order.mrp, 2),
        'unit_price': round(purchase_order.price, 2),
        'amount': round(amount, 2),
        'sgst_tax': purchase_order.sgst_tax,
        'sgst_amt': round(total_sgst, 2),
        'cgst_tax': purchase_order.cgst_tax,
        'cgst_amt': round(total_cgst,2),
        'igst_tax': purchase_order.igst_tax,
        'igst_amt': round(total_igst,2),
        'cess_tax': purchase_order.cess_tax,
        'apmc_tax': purchase_order.apmc_tax,
        'total': round(total_sku_amt, 2),
        'remarks': purchase_order.remarks,
        'sku_attributes_dict': sku_attributes_data_dict.get(wms_code, {}),
    }
    return po_sku_data, po_temp_data, total_tax_amt
        
def frame_sku_attributes(sku_codes, warehouse):
   
    # SKU Attributes data
    sku_attributes_data = SKUAttributes.objects.using(reports_database).filter(
            sku__user=warehouse.id, sku__sku_code__in=sku_codes
        ).values('sku__sku_code', 'attribute_name', 'attribute_value')

    sku_attributes_data_dict = {}
    for attribute_data in sku_attributes_data:
        if attribute_data['sku__sku_code'] in sku_attributes_data_dict:
            if attribute_data['attribute_name'] in sku_attributes_data_dict[attribute_data['sku__sku_code']]:
                sku_attributes_data_dict[attribute_data['sku__sku_code']][attribute_data['attribute_name']] += ', ' + attribute_data['attribute_value']
            else:
                sku_attributes_data_dict[attribute_data['sku__sku_code']].update({attribute_data['attribute_name'] : attribute_data['attribute_value']})
        else:
            sku_attributes_data_dict[attribute_data['sku__sku_code']] = {attribute_data['attribute_name'] : attribute_data['attribute_value']}
    
    return sku_attributes_data_dict


def frame_po_suggestions(warehouse, key, value, extra_dict, misc_dict, po_type, full_po_number , sku_obj=None):
    """
        Frame PO Suggestions
    """
    po_suggestions = copy.deepcopy(PO_SUGGESTIONS_DATA)
    key = key[0]
    ean_number = ''
    if sku_obj:
        eans = get_sku_ean_list(sku_obj)
        if eans:
            ean_number = eans[0]

    if not sku_obj:
        sku_obj = SKUMaster.objects.filter(wms='TEMP', user=warehouse.id)
        po_suggestions['wms_code'] = key

    price = value['price']
    if not price:
        price = 0

    mrp = value['mrp']
    if not mrp:
        mrp = 0

    po_name = value.get('po_name', '')
    if not po_name:
        po_name = full_po_number

    aux_data = value.get('aux_data',{})
    line_extra_fields = value.get('line_extra_fields',{})
    supplier = SupplierMaster.objects.get(user=warehouse.id, supplier_id=value['supplier_id'])
    inter_state=0
    if supplier.tax_type=="inter_state":
        inter_state=1
    po_suggestions['sku_id'] = sku_obj.id
    po_suggestions['supplier_id'] = supplier.id
    po_suggestions['order_quantity'] = value['order_quantity']
    po_suggestions['po_name'] = po_name
    po_suggestions['supplier_code'] = value['supplier_code']
    po_suggestions['price'] = float(price)
    po_suggestions['status'] = 'Manual'
    po_suggestions['remarks'] = value['remarks']
    po_suggestions['mrp'] = float(mrp)
    po_suggestions['free_quantity'] = value.get('free_quantity', 0)
    po_suggestions['json_data'] ={
        "weight": value.get("weight", ""),
        "created_by": extra_dict['request_user'],
        "created_from": "API" if extra_dict['api_call'] else extra_dict['source'],
        "po_currency": extra_dict['supplier_currency'],
        "po_type" : po_type
    }
    if extra_dict['inco_terms']:
        po_suggestions['json_data'].update({'inco_terms': extra_dict['inco_terms']})
    if extra_dict['supplier_payment_terms']:
        po_suggestions['json_data'].update({'supplier_payment_terms': extra_dict['supplier_payment_terms']})
    
    po_suggestions['json_data'].update(aux_data)
    if not (value['sgst_tax'] and value['cgst_tax']) and (not value['igst_tax']) and (not po_type.lower() == 'import'):
        tax_obj = TaxMaster.objects.filter(
            product_type=sku_obj.product_type,
            user=warehouse.id,
            max_amt__gte=sku_obj.price,
            min_amt__lte=sku_obj.price,
            inter_state=inter_state
            )
        if tax_obj.exists():
            value['cgst_tax'] = float(tax_obj[0].cgst_tax)
            value['sgst_tax'] = float(tax_obj[0].sgst_tax)
            value['igst_tax'] = float(tax_obj[0].igst_tax)
    
    po_suggestions['sgst_tax'] = value['sgst_tax']
    po_suggestions['cgst_tax'] = value['cgst_tax']
    po_suggestions['igst_tax'] = value['igst_tax']
    po_suggestions['cess_tax'] = value['cess_tax']
    po_suggestions['utgst_tax'] = value['utgst_tax']
    po_suggestions['apmc_tax'] = value['apmc_tax']
    po_suggestions['ship_to'] = value['ship_to']
    po_suggestions['terms'] = extra_dict['terms_condition']
    if value['po_delivery_date']:
        po_suggestions['delivery_date'] = value['po_delivery_date']
    
    po_suggestions['measurement_unit'] = value.get('measurement_unit', 'UNITS')
    if misc_dict.get('enable_purchase_uom') == 'true':
        pack_data = extra_dict.get('sku_po_uom_dict',{}).get(sku_obj.sku_code, {})
        measurement_unit = pack_data.get(po_suggestions['measurement_unit'],'')
        if not measurement_unit:
            if pack_data:
                max_value = max(pack_data.values())
                pack_id = [k for k, v in pack_data.items() if v == max_value][0]
                po_suggestions['measurement_unit'] = pack_id
            else:
                po_suggestions['measurement_unit'] = ''

    po_suggestions['account_id'] = warehouse.userprofile.id
    if extra_dict['userwise_st_customer'].get("supplier_tax_type", "") == "intra_state" or (supplier.tax_type == "intra_state" and po_type.lower() == 'stocktransfer'):
        po_suggestions['cgst_tax'] = 0
        po_suggestions['sgst_tax'] = 0
        po_suggestions['igst_tax'] = 0
        po_suggestions['cess_tax'] = 0
        po_suggestions['utgst_tax'] = 0
        po_suggestions['apmc_tax'] = 0

    return po_suggestions, price, mrp, ean_number, aux_data, sku_obj, line_extra_fields

def po_creation(all_data, po_type, po_creation_date, full_po_number, extra_dict, misc_dict, warehouse):
    po_data = []
    po_data_list = []
    create_order_dict = {}
    po_header_dict = {}
    sku_obj = ''
    total_taxable_amt = 0
    total = 0
    total_qty = 0
    total_tax_amt_ = 0
    line_extra_list = []
    item_id_list = []
    destnation_sku_obj = {}
    is_stocktransfer = False
    failed_status = {}

    sku_codes = [key[0] for key in all_data.keys()]
    sku_master_map = {
        sku.sku_code: sku
        for sku in SKUMaster.objects.filter(sku_code__in=sku_codes, user=warehouse.id)
    }
    if extra_dict['customer_data_check'] and extra_dict['dest_user'] \
                    and extra_dict['reverse_order_create'] and extra_dict['create_so_for_st_po']:
        is_stocktransfer = True
        destnation_sku_obj = {
        sku.sku_code: sku
        for sku in SKUMaster.objects.filter(sku_code__in=sku_codes, user=(extra_dict['dest_user'].id))
    }
    
    for key, value in all_data.items():
        data = copy.deepcopy(PO_DATA)
        po_suggestions, price, mrp, ean_number, aux_data, sku_obj, line_extra_fields  = frame_po_suggestions(warehouse, key, value, extra_dict, misc_dict, po_type, full_po_number, sku_master_map[key[0]])

        measurement_unit = ""
        data1 = OpenPO(**po_suggestions)
        data1.save()
        if extra_dict['request_data'].get('is_purchase_request'):
            pending_po_objs = PendingPO.objects.filter(wh_user=warehouse.id, full_po_number=full_po_number)
            if pending_po_objs.exists():
                pending_po_objs.update(open_po_id=data1.id)
        if is_stocktransfer:
            sku_po_uom_dict = extra_dict.get('sku_po_uom_dict',{})
            create_order_dict, measurement_unit = frame_order_payload(key, value, price, mrp, full_po_number, 
                    extra_dict['userwise_st_customer'], create_order_dict, extra_dict['dest_user'], extra_dict['request_user'], destnation_sku_obj.get(key[0]),sku_po_uom_dict)

        purchase_order = OpenPO.objects.get(id=data1.id, sku__user=warehouse.id)
        sup_id = purchase_order.id
        po_sku_code = purchase_order.sku.sku_code
        pcf = 1
        measurement_unit =  measurement_unit or value.get('measurement_unit', '')
        if misc_dict.get('enable_purchase_uom') == 'true':
            pack_data = extra_dict.get('sku_po_uom_dict',{}).get(po_sku_code, {})
            if pack_data:
                pcf = pack_data.get(measurement_unit,None)
                if not pcf:
                    max_value = max(pack_data.values())
                    pcf = max_value
        
        data['open_po_id'] = sup_id
        data['order_id'] = int(extra_dict['po_id'])
        data['prefix'] = extra_dict['prefix']
        data['po_number'] = full_po_number
        data['po_type'] = po_type
        data['account_id'] = warehouse.userprofile.id
        data['line_reference'] = aux_data.get('line_reference')
        data['pcf'] = pcf

        if not value['order_quantity'] and not value.get('free_quantity', 0):
            data['status'] = 'location-assigned'
        if value.get('po_date'):
            data['po_date'] = value['po_date']
        order = PurchaseOrder(**data)
        order.save()

        item_id_list.append(str(order.id))
        if po_creation_date:
            PurchaseOrder.objects.filter(id=order.id).update(
                creation_date=po_creation_date,
                updation_date=po_creation_date,
                po_date=po_creation_date
                )
            order = PurchaseOrder.objects.get(id=order.id)
        po_header_dict = frame_po_header_data(po_header_dict, order, warehouse)
        amount = float(purchase_order.order_quantity) * float(purchase_order.price)
        total_taxable_amt += amount
        tax = value['sgst_tax'] + value['cgst_tax'] + value['igst_tax'] + value['utgst_tax'] + \
                value['apmc_tax'] + value['cess_tax']
        if not tax:
            total += amount
        else:
            total += amount + ((amount / 100) * float(tax))
        
        total_qty += purchase_order.order_quantity
        
        if purchase_order.sku.wms_code == 'TEMP':
            wms_code = purchase_order.wms_code
        else:
            wms_code = purchase_order.sku.wms_code
        
        # For Cars 24
        sku_attributes_keys = ['Make', 'Model', 'Variant', 'Year']
        temp_extra_dict = {
            'amount' : amount,
            'wms_code' : wms_code,
            'display_remarks' : misc_dict.get('display_remarks_mail', 'false'),
            'supplier_currency' : misc_dict['supplier_currency'],
            'sku_attributes_keys' : sku_attributes_keys,
            'sku_attributes_data_dict' : extra_dict['sku_attributes_data_dict'],
            'ean_number' : ean_number,
            **extra_dict['configurations']
        }
        po_sku_data, po_temp_data, total_tax_amt = frame_po_temp_data(
            purchase_order, po_suggestions, value, sku_obj, extra_dict['table_headers'], temp_extra_dict)
        
        total_tax_amt_ += total_tax_amt
        po_data.append(po_temp_data)
        po_data_list.append(po_sku_data)
    
        suggestion = OpenPO.objects.get(id=sup_id, sku__user=warehouse.id)
        setattr(suggestion, 'status', 0)
        suggestion.save()  

        line_extra_dict = {str(order.id):line_extra_fields}
        if line_extra_fields:
            line_extra_list.append(line_extra_dict)
    return po_data, order, purchase_order, total, total_qty, total_tax_amt_, total_taxable_amt, create_order_dict, list(po_header_dict.values()), line_extra_list, item_id_list


def get_sale_order_details(extra_fields, warehouse):
    #Fetching Sale Order extra fields of mapped Order
    so_extra_fields = {}
    po_order_ref_field = list(UserAttributes.objects.filter(
        attribute_model='purchase_order', attribute_type=wms_field_const, attribute_values='order_reference',
            status=1, user=warehouse.id).values_list('attribute_name', flat=True))
    if po_order_ref_field:
        po_order_ref_field = po_order_ref_field[0]
        # Getting mapped Order Reference
        order_reference = extra_fields.get(po_order_ref_field, '')
        original_order_id = list(OrderDetail.objects.filter(
            order_reference=order_reference, user=warehouse.id).values_list('original_order_id', flat=True))
        original_order_id = original_order_id[0] if original_order_id else ''
        so_extra_fields = dict(OrderFields.objects.filter(
            original_order_id=original_order_id, user=warehouse.id).values_list('name', 'value'))
    
    return so_extra_fields

def frame_ship_to(request_data, warehouse):
    error_message = ''
    if not request_data.get('ship_to'):
        user_address = UserAddresses.objects.filter(user=warehouse.id)
        if user_address.exists():
            user_address = user_address[0]
            request_data['ship_to'] = '%s %s, %s' % (
                user_address.address_name, user_address.address, user_address.pincode)
        else:
            request_data['ship_to'] = ''
            error_message = 'No Address found, please add Address in Profile'
    return request_data, error_message


def get_po_type(request_data, sales_data, api_call, internal_call):
    if sales_data:
        po_dict = sales_data
    elif api_call or internal_call:
        po_dict = request_data
    else:
        po_dict = request_data

    po_type = 'Normal'
    po_type_list = po_dict.get('po_type')
    if po_type_list:
        if type(po_type_list) == list:
            po_type = po_type_list[0]
        else:
            po_type = po_type_list
            
    create_so_for_st_po = False
    if po_type.lower() == 'stocktransfer' and po_dict.get('create_sale_order'):
        #FOR RUBICON WE HAVE ORDER SHOULD NOT CREATE, SO MAKING IT FALSE DEFAULLY
        create_so_for_st_po = True

    reverse_order_create = True
    if po_type.lower() == 'stocktransfer':
        if po_dict.get('so_order_create','true') == 'false':
            reverse_order_create = False

    return po_type, create_so_for_st_po, reverse_order_create, po_dict

def fetch_po_prefix_details(po_id, warehouse, sales_data, sku_code, prefix, full_po_number):
    check_prefix, inc_status = '', '',
    if not po_id:
        if not sales_data or sales_data['po_order_id'] == '':
            po_id, prefix, full_po_number, check_prefix, inc_status = \
                get_user_prefix_incremental(warehouse, 'po_prefix', sku_code,create_default="PO")
        else:
            po_id = int(sales_data['po_order_id'])
    
    return po_id, prefix, full_po_number, check_prefix, inc_status

def frame_extra_fields(request_data, po_dict, warehouse, full_po_number):
    extra_fields = po_dict.get('extra_fields', {})
    if not extra_fields:
        po_attributes = [field for field in request_data if field.startswith('attr_')]
        for field in po_attributes:
            field_key = field.split('attr_')[1] 
            extra_fields[field_key] = request_data.get(field)
    # Saving PO extra fields
    if extra_fields:
        save_or_update_po_extra_fields(warehouse.id, extra_fields, full_po_number)
    
    return extra_fields


def get_company_ship_address(warehouse, last_po_rec):
    if last_po_rec.ship_to:
        if get_utc_start_date(datetime.datetime.strptime('2021-02-12', '%Y-%m-%d')) < last_po_rec.creation_date:
            ship_to_address = last_po_rec.ship_to
        if warehouse.userprofile.wh_address:
            company_address = warehouse.userprofile.wh_address
        else:
            company_address = warehouse.userprofile.address
    else:
        ship_to_address, company_address = get_purchase_company_address(warehouse.userprofile)
    
    if not ship_to_address:
        wh_ship_to = UserAddresses.objects.filter(address_type = 'Shipment Address', user=warehouse.id).order_by('creation_date')
        if wh_ship_to.exists():
            wh_ship_to = wh_ship_to[0]
            ship_to_address = "%s - %s" % (wh_ship_to.address, wh_ship_to.pincode)

    return company_address, ship_to_address


def get_company_logo(request, profile):
    company_logo=""
    if profile.company.logo:
        try:
            _logo_url = profile.company.logo.url.replace('/static/', 'static/')
            _logo_url =  urllib.url2pathname(_logo_url)
            with open(_logo_url, "rb") as image_file:
                company_logo = base64.b64encode(image_file.read())
            log.info("Email PDF " + str(company_logo))
        except Exception as e:
            try:
                company_logo = "".join([request.build_absolute_uri('/'), profile.company.logo.url.lstrip("/")])
            except Exception:
                company_logo = ""
    return company_logo

def frame_terms_conditions(terms_condition, last_po_rec, timezone, order):
    if last_po_rec.supplier.lead_time:
        lead_time_days = last_po_rec.supplier.lead_time
        replace_date = get_local_date_known_timezone(timezone, order.creation_date + datetime.timedelta(days=int(lead_time_days)),send_date='true')
        date_replace_terms = replace_date.strftime("%d-%m-%Y")
        terms_condition= terms_condition.replace("%^PO_DATE^%", date_replace_terms)
    else:
        terms_condition= terms_condition.replace("%^PO_DATE^%", '')

    return terms_condition

def get_delivery_date(pending_obj):
    if pending_obj.delivery_date:
        return pending_obj.delivery_date.strftime('%d-%m-%Y') 
    else:
        return None

def get_api_call_check(api_call, internal_call):
    if api_call or internal_call:
        return True
    else:
       return False

def update_pending_po_object(warehouse, pr_number, po_number, po_id, last_po_rec):
    if pr_number:
        # saved pending PO's without Approvars defined will be 'auto-approved'
        saved_po = PendingPO.objects.filter(po_number=pr_number, final_status='saved', wh_user_id=warehouse.id)
        if saved_po.exists():
            # If saved Pending PO, updating all the data
            saved_po = saved_po[0]
            saved_po.po_number = po_id
            saved_po.full_po_number = po_number
            saved_po.open_po_id = last_po_rec.id
            saved_po.final_status = 'auto-approved'
            saved_po.save(update_fields=['po_number', 'full_po_number', 'open_po_id', 'final_status', 'updation_date'])

def check_and_create_sale_order(dest_user, create_so_for_st_po,create_order_dict):
    if create_so_for_st_po and create_order_dict:
        try:
            from outbound.views.orders.stock_transfer import create_order_new
            create_order_new(dest_user, create_order_dict, po_creation=False)
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info("Create PO to SO failed for params " + str(create_order_dict) + " and error statement is " + str(e))


def send_po_call_back(warehouse, po_number):
    try:
        po_call_back_3p_integration(warehouse, po_number)
    except Exception as e:
        log.info(generate_log_message("Exception Raised on PO CallBack",
                    warehouse_name = warehouse.username, po_number = po_number, error=str(e)))

def send_po_callback(warehouse, po_numbers):
    if not isinstance(po_numbers, list):
        po_numbers = [po_numbers]
    data_dict = {}
    po_status_dict = get_po_header_status_from_po_header_table([warehouse.id], po_numbers, [])
    data_list = SKUWisePO().get_data(po_numbers, po_status_dict, warehouse, {})
    if data_list:
        data_dict = data_list
    
    return data_dict


def get_company_details(profile):
    company_details = {}
    if profile.company:
        company_details['company_address'] = ''
        if profile.company.address:
            company_details['company_address'] = profile.company.address
        company_details['phone'] = profile.company.phone_number
        company_details['email'] = profile.company.email_id
        company_details['gstin_number'] = profile.company.gstin_number
        company_details['cin_number'] = profile.company.cin_number
        company_details['pan_number'] = profile.company.pan_number

    return company_details

def get_expiry_date(order, po_exp_duration):
    if po_exp_duration:
        return order.creation_date + datetime.timedelta(days=po_exp_duration)
    else:
        return ''

def get_rendered_data(rendered_data, data_dict):
    if rendered_data:
        rendered = rendered_data
    else:
        t = loader.get_template(po_pdf_const)
        rendered = t.render(data_dict)

    return rendered

def frame_po_grouped_data(po_dict, show_mrp, timezone):
    mrp_check = True if show_mrp == 'true' else False
    all_data, show_cess_tax, show_apmc_tax = get_raise_po_grouped_data(po_dict, mrp_check=mrp_check, timezone=timezone)
    return all_data, show_cess_tax, show_apmc_tax

@get_warehouse
def confirm_add_po(request, warehouse, sales_data='', api_call = False, internal_call=False, source='WEB', callback = True):
    """ Create Purchase Order """
    request_data = json.loads(request.body)
    # internal call for Pending PO flow but Approval Matrix not defined     
    ean_flag = False
    status = ''
    ship_to_address = ''
    terms_condition = request_data.get('terms_condition', '')
    api_call_check = get_api_call_check(api_call, internal_call)
    po_type = request_data.get('po_type', '')
    supplier_payment_terms = request_data.get('supplier_payment_terms', '')
    if po_type.lower() != 'stocktransfer' and not request_data.get('ship_to'):
        return JsonResponse({'message': 'Ship to address mandatory !'}, status=400)

    if not request_data:
        return JsonResponse({'error': 'Missing request data'}, status=400)
    
    request_data, error_message = frame_ship_to(request_data, warehouse)
    if error_message:
        return JsonResponse({'message': error_message}, status=400)

    if BasePOValidation(request_data, warehouse.id).validate_po_reference():
        po_reference = request_data.get('po_reference') or ''
        return JsonResponse({"message": "%s PO Reference already exists" %(po_reference)}, status=400)

    misc_types = [
        'display_remarks_mail', 'raise_po', 'enable_purchase_uom',
        'allow_secondary_emails', 'PO', 'show_mrp_raise_po'
    ]
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)

    po_creation_date = ''
    delivery_date = ''
    is_purchase_request = request_data.get('is_purchase_request', False)
    full_po_number = request_data.get('po_number', '')

    dest_user =""
    user_profile = warehouse.userprofile
    timezone = get_user_time_zone(warehouse)
    
    base_currency = user_profile.base_currency
    
    supplier_currency = ''
    po_id, prefix, check_prefix, po_remarks, po_reference = '', '', '', '', ''
    pending_pos, create_order_dict = [], {}

    try:
        if is_purchase_request:
            pr_number = request_data.get('pr_number')
            pending_pos = PendingPO.objects.filter(wh_user=warehouse.id, full_po_number=full_po_number)
            if pending_pos:
                pending_obj = pending_pos[0]
                po_creation_date = pending_obj.creation_date
                po_id = pending_obj.po_number
                full_po_number = pending_obj.full_po_number
                po_remarks = pending_obj.remarks
                prefix = pending_obj.prefix
                delivery_date = get_delivery_date(pending_obj)
                warehouse = pending_obj.wh_user
                supplier_currency = pending_obj.json_data.get('po_currency', '')
        
        po_type, create_so_for_st_po, reverse_order_create, po_dict = get_po_type(request_data, sales_data, api_call, internal_call)

        error, userwise_st_customer, customer_data_check, dest_user = po_type_validations(
            po_dict, base_currency, supplier_currency, po_type, create_so_for_st_po,
            reverse_order_create, timezone, warehouse)
        if error:
            if api_call_check:
                return {'message': error, 'po_number': ''}
            return JsonResponse({'message': error}, status=400)

        show_mrp = misc_dict.get('show_mrp_raise_po', 'false')
        
        all_data, show_cess_tax, show_apmc_tax = frame_po_grouped_data(po_dict, show_mrp, timezone)

        show_cess_tax , show_apmc_tax = False , False
        sku_code = request_data['items'][0]['sku']

        #Fetching sku uom details to validate if same pack id is available in destination warehouse
        sku_codes = [key[0] for key in all_data.keys()]
        sku_po_uom_dict, measurement_units = get_max_pack_uom_quantity(warehouse, sku_codes, misc_dict)

        error_messages = validate_sku_pack_data(
            dest_user, po_type, misc_dict,
            sku_codes, measurement_units,
            all_data, is_items_data=False
        )

        if error_messages:
            if api_call_check:
                return {'message': error_messages, 'po_number':''}
            return JsonResponse({"message": error_messages}, status=400)

        
        po_id, prefix, full_po_number, check_prefix, inc_status = fetch_po_prefix_details(po_id, warehouse, sales_data, sku_code, prefix, full_po_number)
        if inc_status:
            return JsonResponse({'message': 'Prefix not defined'}, status=400)
        

        po_data_list = []
        configurations = {}
        user_profile = UserProfile.objects.filter(user_id=warehouse.id)
        ean_data = SKUMaster.objects.filter(Q(ean_number__gt=0) | Q(eannumbers__ean_number__gt=0),
                                            sku_code__in=sku_codes, user=warehouse.id)
        if ean_data:
            ean_flag = True
            configurations['show_ean'] = True
            if ean_data[0].block_options == 'PO':
                return JsonResponse({'message': ean_data[0].wms_code + " SKU Code Blocked for PO"}, status=400)

        sku_attributes_data_dict = frame_sku_attributes(sku_codes, warehouse)
        misc_dict['show_cess_tax'] = show_cess_tax
        misc_dict['show_apmc_tax'] = show_apmc_tax
        misc_dict['supplier_currency'] = supplier_currency
        misc_dict['show_mrp'] = misc_dict.get('show_mrp_raise_po', 'false')

        table_headers, configurations = frame_po_headers(configurations, misc_dict)
        
        order_id = None
        inco_terms = po_dict.get('inco_terms', [])

        extra_dict = {
            'po_id' : po_id,
            'prefix' : prefix,
            'request_data' : request_data,
            'inco_terms' : inco_terms,
            'terms_condition' : terms_condition,
            'supplier_payment_terms' : supplier_payment_terms,
            'customer_data_check' : customer_data_check,
            'dest_user' : dest_user,
            'reverse_order_create' : reverse_order_create,
            'create_so_for_st_po' : create_so_for_st_po,
            'request_user' : request.user.username,
            'sku_po_uom_dict' : sku_po_uom_dict,
            'sales_data' : sales_data,
            'status' : status,
            'check_prefix' : check_prefix,
            'userwise_st_customer' : userwise_st_customer,
            'sku_attributes_data_dict' : sku_attributes_data_dict,
            'table_headers' : table_headers,
            'api_call' : api_call,
            'source' : source,
            'supplier_currency' : supplier_currency,
            'configurations' : configurations
        }
        po_data, order, last_po_rec, total, total_qty, total_tax_amt_, total_taxable_amt, create_order_dict, po_header_data, line_extra_list, item_id_list\
              = po_creation(all_data, po_type, po_creation_date, full_po_number, extra_dict, misc_dict, warehouse)
        
        if po_header_data:
            POHeader.objects.bulk_create_with_rounding(po_header_data)

        # Saving Extra fields of this PO
        extra_fields = frame_extra_fields(request_data, po_dict, warehouse, full_po_number)

        
        if line_extra_list:
            create_or_update_master_attributes(item_id_list, 'po_line_extra_fields', line_extra_list, warehouse)

        so_extra_fields = get_sale_order_details(extra_fields, warehouse)
        
        address = last_po_rec.supplier.address
        address = '\n'.join(address.split(',')) if address else ''
        
        company_address, ship_to_address = get_company_ship_address(warehouse, last_po_rec)
        
        wh_telephone = warehouse.userprofile.wh_phone_number
        wh_email = warehouse.email
        vendor_name = ''
        vendor_address = ''
        vendor_telephone = ''
        telephone = last_po_rec.supplier.phone_number
        name = last_po_rec.supplier.name
        code = last_po_rec.supplier.supplier_id
        order_id = po_id
        supplier_email = last_po_rec.supplier.email_id
        secondary_supplier_email = list(
            MasterEmailMapping.objects.filter(
                master_id=last_po_rec.supplier.id, user=warehouse.id, master_type='supplier').values_list('email_id',flat=True).distinct())
        
        supplier_email_id =[]
        supplier_email_id.insert(0,supplier_email)
        supplier_email_id.extend(secondary_supplier_email)
        phone_no = last_po_rec.supplier.phone_number
        gstin_no = last_po_rec.supplier.tin_number
        supplier_pan = last_po_rec.supplier.pan_number
        po_reference = last_po_rec.po_name
        po_exp_duration = last_po_rec.supplier.po_exp_duration
        order_date = get_local_date_known_timezone(timezone, order.creation_date)
        expiry_date = get_expiry_date(order, po_exp_duration)

        po_number = order.po_number
        profile = UserProfile.objects.get(user=warehouse.id)
        company_name = profile.company.company_name
        title = 'Purchase Order'
        receipt_type = request.GET.get('receipt_type', '')
        total_amt_in_words = number_in_words(round(total)) + ' ONLY'
        round_value = float(round(total) - float(total))
        company_logo = get_company_logo(request, profile)
        company_details = get_company_details(profile)
        iso_company_logo = get_po_company_logo(warehouse, ISO_COMPANY_LOGO_PATHS, request)
        left_side_logo = get_po_company_logo(warehouse, LEFT_SIDE_COMPNAY_LOGO , request)
        
        terms_condition = frame_terms_conditions(terms_condition, last_po_rec, timezone, order)

        data_dict = {
            'table_headers': table_headers, 'data': po_data, 'data_dict': po_data_list,
            'address': address, 'order_id': order_id,
            'telephone': str(telephone), 'ship_to_address': ship_to_address,
            'name': name, 'code':code, 'order_date': order_date, 'delivery_date': delivery_date, 'total': total, 'po_number': po_number ,
            'po_reference':po_reference, 'supplier_payment_terms': supplier_payment_terms, 'po_currency': supplier_currency,
            'user_name': request.user.username, 'total_amt_in_words': total_amt_in_words,
            'total_qty': total_qty, 'company_name': company_name, 'location': profile.location,
            'w_address': ship_to_address,
            'vendor_name': vendor_name, 'vendor_address': vendor_address,
            'vendor_telephone': vendor_telephone, 'receipt_type': receipt_type, 'title': title,
            'gstin_no': gstin_no, 'expiry_date': expiry_date,
            'wh_telephone': wh_telephone, 'wh_gstin': profile.gst_number, 'wh_pan': profile.pan_number,
            'wh_email': wh_email,
            'terms_condition': terms_condition,'supplier_pan':supplier_pan, 'remarks': po_remarks,
            'company_address': company_address, 'company_details': company_details,
            'company_logo': company_logo, 'iso_company_logo': iso_company_logo,'left_side_logo':left_side_logo,
            'total_tax_amt': total_tax_amt_,'total_taxable_amt': total_taxable_amt, 'inco_terms': ','.join(inco_terms),
            'extra_fields': extra_fields, 'configurations': configurations,
            'so_extra_fields': so_extra_fields
            }
        log_dict = data_dict.copy()
        log_dict.pop('table_headers')
        log_message = (("Response Create_Purchase_Order username %s, Ip Address %s, and response params are %s") % (str(warehouse),str(get_user_ip(request)),str(log_dict)))
        log.info(log_message)
        if round_value:
            data_dict['round_total'] = round_value #"%.2f" % round_value
        # Config based PO doc
        rendered_dict = {'po_data_list':[data_dict]}
        invoice_format = misc_dict.get('PO', 'false')
        filter_dict = {'document_type': 'PO', 'invoice_format': invoice_format}
        rendered_data = get_custom_html_format(filter_dict, rendered_dict, warehouse.id)
        rendered = get_rendered_data(rendered_data, data_dict)
        if misc_dict.get('raise_po', 'false') == 'true':
            data_dict_po = {
                'contact_no': profile.wh_phone_number, 'contact_email': warehouse.email,
                'gst_no': profile.gst_number, 'supplier_name':last_po_rec.supplier.name,
                'billing_address': profile.address, 'shipping_address': ship_to_address,
                'table_headers': table_headers, 'po_currency': supplier_currency
                }
            if misc_dict.get('raise_po', 'false') == 'true':
                write_and_mail_pdf(
                    po_number, rendered, warehouse, supplier_email,
                    phone_no, po_data, str(order_date).split(' ')[0],
                    ean_flag=ean_flag, data_dict_po=data_dict_po,
                    )
        user_profile = UserProfile.objects.filter(user_id=warehouse.id)
        check_purchase_order_created(warehouse, po_id, check_prefix)
        

    except Exception as e:
        log.debug(traceback.format_exc())
        log.info("Confirm Add PO failed for params " + str(po_dict) + " and error statement is " + str(e))
        if api_call_check:
            return {'message': 'Confirm Add PO Failed', 'po_number':''}
        return JsonResponse({"message": "Confirm Add PO Failed"}, status=400)

    #check and create sale order
    check_and_create_sale_order(dest_user, create_so_for_st_po,create_order_dict )

    #trigger po call back
    if callback:
        send_po_call_back(warehouse, po_number)

    #update pending po object
    pr_number = request_data.get('pr_number', '')
    response_dict = {'message': 'Success', 'po_number':str(po_number), 'po_reference': str(po_reference)}
    update_pending_po_object(warehouse, pr_number, po_number, po_id, last_po_rec)
    if api_call or internal_call and source != 'WEB':
        return response_dict
    response_dict['show_pdf'] = True
    return JsonResponse(response_dict, status=200)


@get_warehouse
def confirm_po1(request, user=''):
    data = copy.deepcopy(PO_DATA)
    industry_type = 'FMCG'
    status_dict = {'po_receipt': 'SR', 'Vendor Receipt': 'VR', 'Hosted Warehouse': 'HW'}
    po_id_dict = {}
    po_dict = dict(request.POST.lists())
    misc_types = ['display_remarks_mail', 'raise_po', 'allow_secondary_emails', 'PO', 'show_mrp_raise_po']
    misc_dict = get_multiple_misc_values(misc_types, user.id)
    display_remarks = misc_dict.get('display_remarks_mail', 'false')
    user_profile = UserProfile.objects.filter(user=user.id).values('company__phone_number', 'company__email_id', 'company__gstin_number', 'company__cin_number', 'company__address', 'base_currency',
                                                                   'company__pan_number', 'wh_phone_number', 'gst_number', 'company__company_name', 'location', 'address', 'wh_address', 'timezone', 'pan_number')
    time_zone=get_user_time_zone(user)
    company_logo = get_po_company_logo(user, COMPANY_LOGO_PATHS, request)
    iso_company_logo = get_po_company_logo(user, ISO_COMPANY_LOGO_PATHS, request)
    left_side_logo = get_po_company_logo(user, LEFT_SIDE_COMPNAY_LOGO , request)
    po_data_list=[]
    user_profile = user_profile[0] if user_profile.exists() else {}
    timezone = user_profile.get('timezone', time_zone)
    company_details = {'company_address': user_profile.get('company__address', ''), 'phone': user_profile.get('company__phone_number', ''),
                       'email':user_profile.get('company__email_id',''), 'gstin_number': user_profile.get('company__gstin_number', ''),
                       'cin_number':user_profile.get('company__cin_number',''), 'pan_number': user_profile.get('company__pan_number', ''),
                       'company_name':user_profile.get('company__company_name', '')}
    show_mrp = misc_dict.get('show_mrp_raise_po', 'false')
    po_numbers = []
    base_currency = user_profile.get('base_currency')
    for key, value in po_dict.items():
        for val in value:
            val = json.loads(val)
            total_qty = 0
            total_amount = 0
            total_taxable_amt = 0
            total_tax_amt = 0
            po_data = []
            po_list_data = []
            configurations = {}
            show_cess_tax, ean_flag, show_apmc_tax = False, False, False
            order_type = status_dict[key]
            open_pos = OpenPO.objects.filter(supplier__supplier_id=val['supplier_id'], status__in=['Manual', 'Automated', ''],
                                             order_type = order_type, sku__user=user.id, wms_code=val['purchase_number'])
            po_sku_ids = open_pos.values_list('sku_id', flat=True)
            purchase_orders = open_pos.values('id', 'supplier_id','supplier__supplier_id', 'ship_to', 'sku__sku_code', 'sku__sku_desc', 'order_quantity', 'sgst_tax', 'utgst_tax',
                                           'cgst_tax', 'igst_tax', 'apmc_tax', 'cess_tax', 'price', 'wms_code', 'sku__wms_code', 'sku__measurement_type', 'mrp', 'sku__sku_size',
                                           'supplier__address', 'ship_to', 'supplier__phone_number', 'supplier__name', 'supplier__email_id', 'supplier__tin_number', 'supplier__pan_number',
                                           'terms','order_type', 'po_name', 'json_data', 'remarks', 'sku__hsn_code')
            sku_codes = list(open_pos.values_list('sku__sku_code', flat=True))
            sku_ean_dict = get_sku_ean_numbers(sku_codes, user)
            if sku_ean_dict:
                ean_flag = True
            if open_pos.filter(cess_tax__gt=0).exists():
                show_cess_tax = True
            if open_pos.filter(apmc_tax__gt=0).exists():
                show_apmc_tax = True
            
            # SKU Attributes data
            sku_attributes_data = SKUAttributes.objects.using(reports_database).filter(sku__user=user.id, sku__sku_code__in=sku_codes)\
                                                        .values('sku__sku_code', 'attribute_name', 'attribute_value')
            sku_attributes_data_dict = {}
            for attribute_data in sku_attributes_data:
                if attribute_data['sku__sku_code'] in sku_attributes_data_dict:
                    if attribute_data['attribute_name'] in sku_attributes_data_dict[attribute_data['sku__sku_code']]:
                        sku_attributes_data_dict[attribute_data['sku__sku_code']][attribute_data['attribute_name']] += ', ' + attribute_data['attribute_value']
                    else:
                        sku_attributes_data_dict[attribute_data['sku__sku_code']].update({attribute_data['attribute_name'] : attribute_data['attribute_value']})
                else:
                    sku_attributes_data_dict[attribute_data['sku__sku_code']] = {attribute_data['attribute_name'] : attribute_data['attribute_value']}

            supplier_id = purchase_orders[0]['supplier__supplier_id']
            supplier = SupplierMaster.objects.get(user=user.id, supplier_id=supplier_id)
            supplier_currency = supplier.currency_code
            if not supplier_currency:
                supplier_currency = user_profile.get('base_currency')
            json_data = purchase_orders[0]['json_data']
            inco_terms = ','.join(json_data.get('inco_terms', []))
            supplier_payment_terms = json_data.get('supplier_payment_terms', '')
            po_type = json_data.get('po_type', 'Normal')
            if not po_type:
                po_type = 'Normal'
            if po_type.lower() == "import" and supplier.supplier_type == 'import':
                error = CURRENCY_EXCHANGE_ERROR
                today = datetime.datetime.now().date()
                currency_exchange = CurrencyExchangeMaster.objects.filter(from_currency=supplier_currency, to_currency=base_currency, warehouse=user, start_date__lte=today)
                if currency_exchange.exists():
                    if currency_exchange.last().end_date:
                        end_date = get_local_date_known_timezone(timezone, currency_exchange.last().end_date, send_date=True).date()
                        if today <= end_date:
                            error = ''
                    else:
                        error = ''
                if error:
                    return HttpResponse(error)
            #removing this 'Supplier Code' field for now
            table_headers = [sku_code_const, 'SKU Description', sku_size_const, 'Qty', 'UOM', unit_price_const, 'Amt', sgst_pcnt_const, csgt_pcnt_const, igst_pcnt_const, utgst_pcnt_const, 'Total']
            if ean_flag:
                table_headers.insert(table_headers.index(sku_desc_const), 'EAN')
                configurations['show_ean'] = True
            if industry_type == 'FMCG' and show_mrp == 'true':
                table_headers.insert(table_headers.index('Amt'), mrp_const.format(supplier_currency))
                configurations['show_mrp'] = True
            if show_cess_tax:
                table_headers.insert(table_headers.index(utgst_pcnt_const), cess_pcnt_const)
                configurations['show_cess_tax'] = True
            if show_apmc_tax:
                table_headers.insert(table_headers.index(utgst_pcnt_const), apmc_pcnt_const)
                configurations['show_apmc_tax'] = True
            if display_remarks == 'true':
                table_headers.append('Remarks')
                configurations['display_remarks'] = True
            
            table_headers[table_headers.index('Unit Price')] = 'Unit Price ({})'.format(supplier_currency)
            table_headers[table_headers.index('Amt')] = AMT_CONSTANT.format(supplier_currency)
            table_headers[table_headers.index('Total')] = 'Total ({})'.format(supplier_currency)

            sku_supplier = dict(SKUSupplier.objects.filter(sku__user=user.id, supplier__supplier_id=val['supplier_id'],
                                                          sku__sku_code__in=sku_codes).values_list('sku__sku_code', 'supplier_code'))
            for purchase_order in purchase_orders:
                tax = 0
                ean_number = ''
                supplier = purchase_order['supplier_id']
                ship_to = purchase_order['ship_to']
                supplier_id = purchase_order['supplier__supplier_id']
                supplier = (supplier, ship_to, order_type)
                if supplier not in po_id_dict:
                    sku_code = purchase_order['sku__sku_code']
                    po_id, prefix, full_po_number, check_prefix, inc_status = get_user_prefix_incremental(user, 'po_prefix', sku_code)
                    if inc_status:
                        return HttpResponse("Prefix not defined")
                    po_id_dict[supplier] = {'po_id': po_id, 'prefix': prefix, 'po_number': full_po_number}
                data['open_po_id'] = purchase_order['id']
                data['order_id'] = po_id_dict[supplier]['po_id']
                data['prefix'] = po_id_dict[supplier]['prefix']
                data['po_number'] = po_id_dict[supplier]['po_number']
                data['po_type'] = po_type
                data['account_id'] = user.userprofile.id
                order = PurchaseOrder(**data)
                order.save()

                amount = float(purchase_order['order_quantity']) * float(purchase_order['price'])
                tax = purchase_order['sgst_tax'] + purchase_order['cgst_tax'] + purchase_order['igst_tax']+\
                      purchase_order['utgst_tax'] + purchase_order['apmc_tax'] + purchase_order['cess_tax']
                tax_amt = (amount / 100) * float(tax)
                total_amount += amount + tax_amt
                total_qty += float(purchase_order['order_quantity'])
                total_sku_amt = amount + tax_amt
                total_tax_amt += tax_amt
                total_taxable_amt += amount
                total_sgst = purchase_order['sgst_tax'] * (amount/100)
                total_cgst = purchase_order['cgst_tax'] * (amount/100)
                total_igst = purchase_order['igst_tax'] * (amount/100)

                if purchase_order['sku__wms_code'] == 'TEMP':
                    wms_code = purchase_order['wmscode']
                else:
                    wms_code = purchase_order['sku__wms_code']
                supplier_code = sku_supplier.get(purchase_order['sku__sku_desc'], {}).get('supplier_code', '')
                #removing supplier_code
                po_temp_data = [wms_code, purchase_order['sku__sku_desc'], purchase_order['sku__sku_size'],
                                purchase_order['order_quantity'], purchase_order['sku__measurement_type'],
                                purchase_order['price'], round(amount, 2), round(purchase_order['sgst_tax'],2), round(purchase_order['cgst_tax'], 2),
                                round(purchase_order['igst_tax'], 2), round(purchase_order['utgst_tax'], 2), round(total_sku_amt, 2)]
                if ean_flag:
                    ean_number = sku_ean_dict.get(purchase_order['sku__sku_code'], [''])[0]
                    po_temp_data.insert(table_headers.index('EAN'), ean_number)
                if industry_type == 'FMCG' and show_mrp == 'true':
                    po_temp_data.insert(table_headers.index(mrp_const.format(supplier_currency)), round(purchase_order['mrp'], 2))
                if show_cess_tax:
                    po_temp_data.insert(table_headers.index(cess_pcnt_const), purchase_order['cess_tax'])
                if show_apmc_tax:
                    po_temp_data.insert(table_headers.index(apmc_pcnt_const), purchase_order['apmc_tax'])
                if display_remarks == 'true':
                    po_temp_data.append(purchase_order['remarks'])

                po_data.append(po_temp_data)

                po_sku_data = {
                    'sku_code': wms_code,
                    'sku_desc': purchase_order['sku__sku_desc'],
                    'sku_size': purchase_order['sku__sku_size'],
                    'ean_number': ean_number,
                    'hsn_code': purchase_order['sku__hsn_code'],
                    'supplier_code': supplier_code,
                    'delivery_schedule': '',
                    'quantity': purchase_order['order_quantity'],
                    'uom': purchase_order['sku__measurement_type'],
                    'weight': val.get('weight', ''),
                    'mrp': round(purchase_order['mrp'], 2),
                    'unit_price': round(purchase_order['price'], 2),
                    'amount': round(amount, 2),
                    'sgst_tax': purchase_order['sgst_tax'], 'sgst_amt': round(total_sgst, 2),
                    'cgst_tax': purchase_order['cgst_tax'], 'cgst_amt': round(total_cgst,2),
                    'igst_tax': purchase_order['igst_tax'], 'igst_amt': round(total_igst,2),
                    'cess_tax': purchase_order['cess_tax'], 'apmc_tax': purchase_order['apmc_tax'],
                    'total': round(total_sku_amt, 2),
                    'remarks': purchase_order['remarks'],
                    'sku_attributes_dict': sku_attributes_data_dict.get(wms_code, {}),
                }
                po_list_data.append(po_sku_data)

                open_po_obj = OpenPO.objects.get(id=purchase_order['id'], sku__user=user.id)
                setattr(open_po_obj, 'status', 0)
                open_po_obj.json_data.update({
                    "created_by": request.user.username,
                    "created_from": "WEB",
                    "po_currency": supplier_currency
                })
                open_po_obj.save()
            
            # Saving PO extra fields
            extra_fields = json_data.get('extra_fields', {})
            if extra_fields:
                save_or_update_po_extra_fields(user.id, extra_fields, po_id_dict[supplier]['po_number'])

            # Fetching Sale Order extra fields of mapped Order
            so_extra_fields = {}
            po_order_ref_field = list(UserAttributes.objects.filter(attribute_model='purchase_order', attribute_type=wms_field_const, \
                                                    attribute_values='order_reference', status=1, user=user.id). \
                                                    values_list('attribute_name', flat=True))
            if po_order_ref_field:
                po_order_ref_field = po_order_ref_field[0]
                # Getting mapped Order Reference
                order_reference = extra_fields.get(po_order_ref_field, '')
                original_order_id = list(OrderDetail.objects.filter(order_reference=order_reference, user=user.id).values_list('original_order_id', flat=True))
                original_order_id = original_order_id[0] if original_order_id else ''
                so_extra_fields = dict(OrderFields.objects.filter(original_order_id=original_order_id, user=user.id).values_list('name', 'value'))

            if purchase_orders.exists():
                supplier_address = purchase_orders[0]['supplier__address']
                supplier_address = '\n'.join(supplier_address.split(','))
                ship_to_address, company_address = '', ''
                if purchase_orders[0]['ship_to']:
                    ship_to_address = purchase_orders[0]['ship_to']
                    company_address = user_profile.get('company__address', '')
                ship_to_address = '\n'.join(ship_to_address.split(','))
                supplier_telephone = purchase_orders[0]['supplier__phone_number']
                supplier_pan = purchase_orders[0]['supplier__pan_number']
                supplier_name = purchase_orders[0]['supplier__name']
                supplier_email = purchase_orders[0]['supplier__email_id']
                secondary_supplier_email = list(MasterEmailMapping.objects.filter(master_id=supplier, user=user.id, master_type='supplier').values_list('email_id',flat=True).distinct())
                supplier_email_id =[]
                supplier_email_id.insert(0,supplier_email)
                supplier_email_id.extend(secondary_supplier_email)
                gstin_no = purchase_orders[0]['supplier__tin_number']
                vendor_name, vendor_address, vendor_telephone = '', '', ''
                terms_condition = purchase_orders[0]['terms']
            wh_telephone = user_profile.get('wh_phone_number', '')
            order_id = po_id_dict[supplier]['po_id']
            order_date = get_local_date_with_time_zone(time_zone, order.creation_date) if order.creation_date  else ''
            po_number = po_id_dict[supplier]['po_number']
            po_numbers.append(po_number)

            total_amt_in_words = number_in_words(round(total_amount)) + ' ONLY'
            round_value = float(round(total_amount) - float(total_amount))
            data_dict = {'table_headers': table_headers, 'data': po_data, 'data_dict': po_list_data, 'address': supplier_address, 'order_id': order_id,
                         'telephone': str(supplier_telephone), 'code': supplier_id,'name': supplier_name, 'order_date': order_date, 'total': total_amount,
                         'supplier_pan': supplier_pan,
                         'company_name': user_profile.get('company__company_name',''), 'location': user_profile.get('location', ''),
                         'po_reference': purchase_order['po_name'], 'po_number': po_number,
                         'total_qty': total_qty, 'vendor_name': vendor_name, 'vendor_address': vendor_address,
                         'vendor_telephone': vendor_telephone, 'gstin_no': gstin_no,
                         'w_address': ship_to_address, 'ship_to_address': ship_to_address,
                         'wh_telephone': wh_telephone, 'wh_gstin': user_profile.get('gst_number', ''),
                         'wh_email': user.email, 'wh_pan': user_profile.get('pan_number', ''),
                         'terms_condition' : terms_condition, 'total_amt_in_words' : total_amt_in_words,
                         'show_cess_tax': show_cess_tax, 'company_address': company_address, 'company_details': company_details,
                         'total_tax_amt': total_tax_amt, 'total_taxable_amt': total_taxable_amt,
                         'company_logo': company_logo, 'iso_company_logo': iso_company_logo,'left_side_logo':left_side_logo,
                         'po_currency': supplier_currency, 'inco_terms': inco_terms, 'supplier_payment_terms':supplier_payment_terms,
                         'extra_fields': extra_fields, 'configurations': configurations,
                         'so_extra_fields': so_extra_fields,
                        }
            if round_value:
                data_dict['round_total'] = "%.2f" % round_value
            po_data_list.append(data_dict)
            # Config based PO doc
            rendered_dict = {'po_data_list': [data_dict]}
            invoice_format = misc_dict.get('PO', 'false')
            filter_dict = {'document_type': 'PO', 'invoice_format': invoice_format}
            rendered_data = get_custom_html_format(filter_dict, rendered_dict, user.id)
            if rendered_data:
                rendered = rendered_data
            else:
                t = loader.get_template()
                rendered = t.render(data_dict)
            if misc_dict.get('raise_po', 'false') == 'true':
                data_dict_po = {
                    'contact_no': user_profile.get('wh_phone_number', ''),
                    'contact_email': user.email, 'gst_no': user_profile.get('gst_number', ''),
                    'supplier_name':purchase_order['supplier__name'],
                    'billing_address': user_profile.get('address', ''),
                    'shipping_address': user_profile.get('wh_address', ''),
                    'table_headers':table_headers, 'po_currency': supplier_currency
                    }
                write_and_mail_pdf(
                    po_number, rendered, user, supplier_email_id,
                    str(supplier_telephone), po_data, str(order_date).split(' ')[0],
                    ean_flag=ean_flag, data_dict_po=data_dict_po,
                    )
    check_purchase_order_created(user, po_id, check_prefix)    
    response_dict = {'message': 'Success', 'po_number':str(','.join(po_numbers)), 'show_pdf': True}
    return JsonResponse(response_dict, status=200)
