import pytz
import json
import datetime
from dateutil import parser
from collections import OrderedDict

from django.http import HttpResponse,JsonResponse
from django.db.models import F, Q


#Model Imports
from wms_base.models import User, UserAddresses
from core.models import SKUMaster, CurrencyExchangeMaster
from inbound.models import (
    SupplierMaster, PurchaseOrder, PendingPO, ASNSummary, OpenPO
    )
from outbound.models.picklist import Picklist

#Method Imports
from core_operations.views.common.main import (
    get_warehouse,
    update_error_message, get_misc_value,
    get_local_date_known_timezone,
    get_multiple_misc_values, get_user_time_zone, get_user_attributes
    )
from core_operations.views.common.user_attributes import validate_attributes
from inbound.views.supplier.main import validate_supplier
from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/po_validation' + today + '.log')
log_err = init_logger('logs/po_validation.log')

MULTIPLE_TAX_MESSAGE = 'Multiple Taxes mentioned for SKU Code'
PO_IGST_TAX_MESSAGE = 'For import PO, only IGST tax applicable. SKU Code'


class BasePOValidation:
    def __init__(self, req_data, wh_user):
        self.req_data = req_data
        self.wh_user = wh_user

    def validate_po_reference(self):
        purchase_number = self.req_data.get('purchase_number', '')
        po_name = self.req_data.get('po_name', '')
        exclude_dict = {}
        if purchase_number:
            exclude_dict = {'wms_code': purchase_number}
        if po_name:
            open_po_objs = OpenPO.objects.filter(po_name=po_name, sku__user=self.wh_user).exclude(**exclude_dict)
            return open_po_objs.exists()
        return False

def validate_dest_user_data(sku, sku_dict, dest_sku_dict, dest_user):
    """Validates destination user data."""
    wms_list = []
    if sku not in dest_sku_dict:
        wms_list = [f'SKU {sku} is not available in {dest_user.username}']
    else:
        dest_sku_data, source_sku_data = dest_sku_dict[sku], sku_dict[sku]
        for attr in ['batch_based', 'enable_serial_based']:
            if dest_sku_data.get(attr, 0) != source_sku_data.get(attr, 0):
                wms_list.append(f'SKU {sku} {attr.replace("_", " ")} attribute mismatch with destination warehouse')

    return wms_list

def get_wms_data(each_row, sku_dict, dest_sku_dict, dest_user):
    """Validates SKU data and checks for expense/non-expense conditions."""
    sku = each_row.get('sku', '')
    wms_list, wh_wms_list, sku_type_check = [], '', ''
    # Validate if SKU exists in the warehouse sku dict
    if sku not in sku_dict:
        wms_list = [f"Invalid WMS Codes are {sku}" if not wms_list else f"{wms_list}, {sku}"]
    else:
        if dest_user:
            wms_list = validate_dest_user_data(sku, sku_dict, dest_sku_dict, dest_user)

        sku_data = sku_dict.get(sku, {})
        sku_type = sku_data.get('sku_type', '')
        is_expense = sku_type == 'Expense'
        is_non_expense = sku_type != 'Expense'

        if is_expense and is_non_expense:
            sku_type_check = 'Cannot Raise Both Expense and Non Expense SKUs in PO'

    return wms_list, wh_wms_list, sku_type_check

def get_sku_data(sku_codes,warehouse):
    sku_dict = SKUMaster.objects.filter(sku_code__in=sku_codes, user=warehouse.id, status=1).values('sku_code', 'sku_type', 'batch_based', 'enable_serial_based')
    sku_dict = {item['sku_code']: item for item in sku_dict}
    return sku_dict

def validate_item_data(request_data, warehouse, import_po, misc_dict, dest_user):
    tax_list, tax_list2, wms_list= [], [], []
    price_mrp_check =''
    show_mrp = misc_dict.get('show_mrp_raise_po', 'false')
    sku_codes = [each_row['sku'] for each_row in request_data.get('items', [])]
    #Get sku data at bulk
    sku_dict = get_sku_data(sku_codes, warehouse)
    dest_sku_dict = {}
    if dest_user:
        dest_sku_dict = get_sku_data(sku_codes, dest_user)

    for each_row in request_data.get('items', []):
        if not each_row.get('sku'):
            continue
        sku_error, wh_wms_list, sku_type_check = get_wms_data(each_row, sku_dict, dest_sku_dict, dest_user)
        igst_tax = 0
        if sku_error:
            wms_list.extend(sku_error)

        if misc_dict.get('allow_unitprice_gt_mrp', '') != 'true' and show_mrp == 'true' and each_row.get('price') and each_row.get('mrp'):
            if float(each_row['mrp'])!=0 and float(each_row['price']) > float(each_row['mrp']):
                price_mrp_check = 'Unit Price Cannot be Greater than the MRP for ' + each_row['sku']
        if each_row.get('cgst_tax'):
            try:
                cgst_tax = float(each_row['cgst_tax'])
            except ValueError:
                cgst_tax = 0
                log_err.error(f"Invalid CGST tax value: {each_row.get('cgst_tax', '')} for SKU: {each_row.get('sku','')}")
            try:
                if each_row.get('igst_tax',0):
                    igst_tax = float(each_row.get('igst_tax',0))
            except ValueError:
                igst_tax = 0
                log_err.error(f"Invalid IGST tax value: {each_row.get('igst_tax','')} for SKU: {each_row.get('sku','')}")
            if cgst_tax and igst_tax:
                tax_list.append(each_row['sku'])

        if import_po and (each_row.get('cgst_tax') or each_row.get('sgst_tax')):
            tax_list2.append(each_row['sku'])
    
    return wms_list, tax_list, wh_wms_list, tax_list2, price_mrp_check, sku_type_check

def validate_po_delivery_date(request_data, warehouse):
    error_message = ''
    timezone = get_user_time_zone(warehouse)
    po_delivery_date = request_data.get('po_delivery_date', '')
    if po_delivery_date:
        try:
            if len(po_delivery_date.split()) == 1:  # Only date is provided
                po_delivery_date += " 23:59:59"
            ist_timezone = pytz.timezone(timezone)
            parsed_date = datetime.datetime.strptime(po_delivery_date, "%Y-%m-%d %H:%M:%S")
            po_delivery_date = ist_timezone.localize(parsed_date)
            current_time = get_local_date_known_timezone(timezone, datetime.datetime.now(), send_date=True)
            if po_delivery_date < current_time:
                error_message = 'PO Delivery Date cannot be a past date. Please pass Delivery Date as a future date'
        except ValueError:
            log_err.error(f"Invalid Delivery date format: {po_delivery_date}")
            error_message = 'Invalid Delivery Date Format'
    return error_message

@get_warehouse
def validate_po(request, warehouse: User):
    request_data = json.loads(request.body)
    receipt_type = request_data.get('receipt_type', '')
    wh_purchase_order = request_data.get('wh_purchase_order', '')
    is_actual_pr = request_data.get('is_actual_pr', '')
    purchase_number = request_data.get('purchase_number', '')
    misc_types = [ 'show_mrp_raise_po', 'allow_unitprice_gt_mrp']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    supplier_master = SupplierMaster.objects.filter(supplier_id=request_data.get('supplier_id', ''), user=warehouse.id, status=1)
    if not supplier_master and receipt_type != 'Hosted Warehouse' \
                and wh_purchase_order != 'true' and is_actual_pr != 'true':
        return JsonResponse({"message": "Invalid Supplier " + str(request_data.get('supplier_id'))}, status=400)

    po_name = request_data.get('po_name', '')
    #PO Reference Exists check
    if BasePOValidation(request_data, warehouse.id).validate_po_reference():
        return JsonResponse({"message": "%s PO Reference already exists" %(po_name)}, status=400)

    #PO Reference and Supplier group by check for PO Limit (500) Entries
    po_limit = 500
    if len(request_data.get('items',[])) > po_limit:
        return JsonResponse({"message": "Cannot Raise More than %s SKUs to Supplier in a PurchaseOrder" %(po_limit)}, status=400)

    import_po = False
    po_type = request_data.get('po_type', '')
    if po_type.lower() == 'import' and supplier_master[0].supplier_type == 'import':
        import_po = True

    dest_user = ''
    if po_type.lower() == 'stocktransfer':
        dest_user_obj = User.objects.filter(username=supplier_master[0].supplier_id)
        if dest_user_obj.exists():
            dest_user = dest_user_obj[0]

    wms_list, tax_list, wh_wms_list, tax_list2, price_mrp_check, sku_type_check = validate_item_data(request_data, warehouse, import_po, misc_dict, dest_user)
    message = 'success'
    if wms_list:
        message = wms_list
    if tax_list:
        tax_list = tax_list = f'{MULTIPLE_TAX_MESSAGE} {", ".join(tax_list)}'
        if message == 'success':
            message = tax_list
        else:
            message = f'{message} and {tax_list}'
    if wh_wms_list:
        if message == 'success':
            message = wh_wms_list
        else:
            message = f'{message} and {wh_wms_list}'
    if tax_list2:
        tax_list2 = f'{PO_IGST_TAX_MESSAGE} {", ".join(tax_list2)}'

        if message == 'success':
            message = tax_list2
        else:
            message = f'{message} and {tax_list2}'
    if sku_type_check:
        message = sku_type_check
    if price_mrp_check:
        message = price_mrp_check
    if message == 'success':
        status_code = 200
    else:
        status_code = 400
    return JsonResponse({'message':message}, status=status_code)

def get_po_status_dict(po_status_objs):
    """frame PO status dict and total receive qty and total qty"""
    po_sku_status_dict = {}
    po_sku_line_ref_dict = {}
    po_sku_line_id_dict = {}
    total_receive_qty, total_qty = 0, 0
    po_status_list = []
    for po in po_status_objs:
        sku_code = po['open_po__sku__sku_code']
        line_reference = po['line_reference']
        status = po['status']
        received_quantity = po['received_quantity']
        order_quantity = po['open_po__order_quantity']
        cancelled_quantity = po['cancelled_quantity']
        receivable_quantity = order_quantity - received_quantity - cancelled_quantity
        po_dict = {
            'status':status,
            'received_quantity':received_quantity,
            'order_quantity':order_quantity,
            'receivable_quantity':receivable_quantity,
            'cancelled_quantity':cancelled_quantity,
            'saved_quantity': po['saved_quantity']}
        po_sku_status_dict[sku_code] = po_dict
        po_sku_line_ref_dict[(sku_code, line_reference)] = po_dict
        po_sku_line_id_dict[po['id']] = po_dict
        total_receive_qty += received_quantity
        total_qty += order_quantity
        po_status_list.append(status)

    return po_sku_status_dict, po_sku_line_ref_dict, po_sku_line_id_dict, total_receive_qty, total_qty, po_status_list

def get_asn_status_dict(asn_objs):
    """frame ASN status dict and line reference dict"""
    asn_sku_list = []
    asn_sku_line_ref_list = []

    for asn in asn_objs:
        sku_code = asn['purchase_order__open_po__sku__sku_code']
        line_reference = asn['purchase_order__line_reference']

        asn_sku_list.append(sku_code)
        asn_sku_line_ref_list.append((sku_code, line_reference))

    return asn_sku_list, asn_sku_line_ref_list

def validate_po_status_for_update(po_key, po_key_name, po_status_list, total_receive_qty, total_qty, failed_status, po_reference):
    """Validate PO Status for Update"""
    if '' not in po_status_list and 'grn-generated' in po_status_list and total_receive_qty >= total_qty:
        #If PO already Received
        update_error_message(failed_status, 5024, "PO cannot be updated, GRN has already generated against this PO", po_key, field_key=po_key_name, reference=po_reference)
    elif not ('' in po_status_list or 'grn-generated' in po_status_list):
        if 'location-assigned' in po_status_list:
            #If PO already cancelled
            update_error_message(failed_status, 5024, "PO already Cancelled", po_key, field_key=po_key_name, reference=po_reference)

def validate_asn_status_for_update(po_key, po_key_name, status, asn_objs, reason, failed_status, po_reference):
    """Validate ASN Status for Update"""
    if status == 'cancel':
        if asn_objs.exists():
            # If ASN is pending for PO
            update_error_message(failed_status, 5024, "ASN is pending for this PO, please cancel ASN first", po_key, field_key=po_key_name, reference=po_reference)
        if not reason:
            update_error_message(failed_status, 5024, "Reason is mandatory to Cancel PO", po_key, field_key=po_key_name, reference=po_reference)

def validate_skus_with_replace_flag(po_key, po_key_name, request_data, po_data, replace_flag, failed_status, po_reference):
    if replace_flag:
        sku_list = []
        for record in po_data:
            if record['status'] in ['grn-generated', 'confirmed-putaway']:
                sku = record['open_po__sku__sku_code']
                po_id = record['id']
                line_reference = record['line_reference']
                found = False
                for item in request_data['items']:
                    item_aux_data = item.get('aux_data', {})
                    if item.get('id') == po_id or \
                        item_aux_data.get('line_reference') == line_reference or \
                        item['sku'] == sku:
                        found = True
                        break
                if not found:
                    sku_list.append(sku)
        if sku_list:
            update_error_message(failed_status, 5024, f"GRN generated SKUs cannot be replaced: {', '.join(sku_list)}", po_key, po_key_name, reference=po_reference)

def fetch_picklist_data_for_po(warehouse, po_reference):
    """Validate Stock Transfer PO Updation"""
    pick_list_objs = Picklist.objects.filter(order__order_reference = po_reference, sku__user=warehouse.id).exclude(status='cancelled').values('order__json_data', 'sku__sku_code')
    picklist_line_references, picklist_sku_codes = [], []
    for picklist in pick_list_objs:
        picklist_sku_codes.append(picklist['sku__sku_code'])
        if picklist['order__json_data']:
            picklist_line_references.append(picklist['order__json_data'].get('line_reference', ''))

    return picklist_line_references, picklist_sku_codes

def validate_po_updation(request_data, warehouse, delete_po=False):
    ''' Validate Update PO, returns failed statues list '''
    failed_status, sku_details = {}, {}
    failed_status_list = []
    misc_types = [ 'show_mrp_raise_po', 'allow_unitprice_gt_mrp']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    if isinstance(request_data, dict):
        request_data = [request_data]

    extra_data = {}
    for each_data in request_data:
        status = each_data.get('status', '').lower()
        po_number = each_data.get('po_number', '')
        po_reference = each_data.get('po_reference', '')
        reason = each_data.get('reason', '')
        supplier_id = each_data.get('supplier_id', '')
        replace_flag = each_data.get('replace', False)
        update_order_flag = each_data.get('update_order', True)
        if not (po_number or po_reference):
            update_error_message(failed_status, 5024, "Po Number or PO Reference required", po_number, reference=po_reference)

        po_key = po_number or po_reference
        po_key_name = 'po_number' if po_number else 'po_reference'
        if failed_status:
            failed_status_list.extend(failed_status['errors'])
            failed_status = {}
            continue

        po_search_params = {'open_po__sku__user': warehouse.id}
        asn_search_params = {}
        if po_reference:
            po_search_params['open_po__po_name'] = po_reference
            asn_search_params['purchase_order__open_po__po_name'] = po_reference
        if po_number:
            po_search_params['po_number'] = po_number
            asn_search_params['purchase_order__po_number'] = po_number

        purchase_order = PurchaseOrder.objects.filter(**po_search_params)
        if purchase_order.exists():
            picklist_line_references, picklist_sku_codes = [], []
            po_type = purchase_order[0].po_type
            po_reference = purchase_order[0].open_po.po_name
            supplier_id = purchase_order[0].open_po.supplier.supplier_id
            each_data['po_type'] = po_type
            each_data['po_reference'] = po_reference
            each_data['supplier_id'] = supplier_id
            if po_type.lower() == 'stocktransfer' and update_order_flag:
                dest_user = User.objects.filter(username = supplier_id).first()
                picklist_line_references, picklist_sku_codes = fetch_picklist_data_for_po(dest_user, po_reference)
                if delete_po and (picklist_line_references or picklist_sku_codes):
                    update_error_message(failed_status, 5024, "Item is in progress, hence not allowing to update!", po_number, reference=po_reference)

            #Getting PO Current status
            po_status_objs = purchase_order.filter(
                received_quantity__lte=F('open_po__order_quantity')
            ).values(
                'id','status', 'open_po__sku__sku_code', 'line_reference', 'received_quantity', 'open_po__order_quantity', 'cancelled_quantity',
                'saved_quantity'
            )
            po_sku_status_dict, po_sku_line_ref_dict, po_sku_line_id_dict, total_receive_qty, total_qty,po_status_list  = get_po_status_dict(po_status_objs)

            # Checking if ASN is pending for this PO
            asn_objs = ASNSummary.objects.filter(**asn_search_params, status__in=[1,2,5,6], asn_user=warehouse.id).values('purchase_order__open_po__sku__sku_code', 'purchase_order__line_reference')
            asn_sku_list, asn_sku_line_ref_list = get_asn_status_dict(asn_objs)
            # Validate PO Status for Update
            validate_po_status_for_update(po_key, po_key_name, po_status_list, total_receive_qty, total_qty, failed_status, po_reference)

            #validate ASN Status for Update
            validate_asn_status_for_update(po_key, po_key_name, status, asn_objs, reason, failed_status, po_reference)

            #validate SKUs with Replace Flag
            validate_skus_with_replace_flag(po_key, po_key_name, each_data, po_status_objs, replace_flag, failed_status, po_reference)

            if not delete_po:
                po = purchase_order.first()
                extra_data = {
                    'po_type': po.po_type,
                    'po_number' : po.po_number,
                    'po_reference' : po.open_po.po_name,
                    'supplier_id': supplier_id,
                    'ship_to': po.ship_to,
                    'supplier_obj' : po.open_po.supplier,
                    'show_mrp_raise_po' : misc_dict.get('show_mrp_raise_po', 'false'),
                    "po_sku_line_ref_dict": po_sku_line_ref_dict,
                    "po_sku_status_dict": po_sku_status_dict,
                    "asn_sku_list": asn_sku_list,
                    "asn_sku_line_ref_list": asn_sku_line_ref_list,
                    "po_sku_line_id_dict": po_sku_line_id_dict,
                    "allow_unitprice_gt_mrp": misc_dict.get('allow_unitprice_gt_mrp', 'false'),
                    "update_order_flat": update_order_flag,
                    "picklist_line_references": picklist_line_references,
                    "picklist_sku_codes": picklist_sku_codes
                }
                sku_data = each_data.get('items', [])
                #SKU Level PO Validation
                sku_details = sku_level_po_validation(warehouse, sku_data, failed_status,extra_data,True)

        else:
            update_error_message(failed_status, 5024, "Invalid PO Number or PO Reference", po_key, field_key=po_key_name, reference=po_reference)

        if failed_status:
            failed_status_list.extend(failed_status['errors'])
            failed_status = {}

    return failed_status_list, sku_details

def validate_save_po(po_data, warehouse):
    ''' Validates Save PO, does basic validation '''
    error_messages = []
    supplier_id = po_data.get('supplier_id', '')
    if not supplier_id:
        error_messages = ['Supplier ID required']
        return error_messages, po_data
    supplier = SupplierMaster.objects.filter(supplier_id=supplier_id, user=warehouse.id, status=1)
    if supplier.exists():
        po_data['sup_id'] = supplier[0].id
    else:
        error_messages = ['Invalid Supplier ID']
        return error_messages, po_data
    if po_data.get('po_delivery_date'):
        try:
            po_delivery_date = datetime.datetime.strptime(po_data.get('po_delivery_date'), "%m/%d/%Y")
            po_data['po_delivery_date'] = po_delivery_date
        except ValueError:
            log_err.error(f"Invalid PO delivery date format: {po_data.get('po_delivery_date')}")
            error_messages.append("Invalid PO Delivery Date format")
            return error_messages, po_data
    sku_codes = [data.get('sku', '') for data in po_data.get('items', [])]
    if not sku_codes:
        error_messages = ['sku is mandatory']
        return error_messages, po_data
    sku_dict = dict(SKUMaster.objects.filter(sku_code__in=sku_codes, user=warehouse.id).values_list('sku_code', 'id'))

    for each_data in po_data.get('items', []):
        if each_data.get('sku', '') in sku_dict:
            each_data['sku_id'] = sku_dict[each_data['sku']]
        else:
            error_messages.append('Invalid SKU code - %s' % each_data.get('sku', ''))
        if not each_data.get('order_quantity'):
            error_messages.append('Order quantity required for %s' % each_data.get('sku'))
    return error_messages, po_data

def po_delivery_date_validation(po_data, final_data_dict, failed_status, po_reference, timezone):
    ''' Validates PO Delivery Date '''
    po_delivery_date = po_data.get('po_delivery_date', '')
    if po_delivery_date:
        try:
            if len(po_delivery_date.split()) == 1:  # Only date is provided
                po_delivery_date += " 23:59:59"
            ist_timezone = pytz.timezone(timezone)
            parsed_date = parser.parse(po_delivery_date)
            po_delivery_date = ist_timezone.localize(parsed_date)
            final_data_dict['po_delivery_date'] = str(parsed_date)
        except ValueError:
            update_error_message(failed_status, 5024, 'Invalid po_delivery_date Format', po_reference, reference=po_reference)

def po_date_validation(po_data, final_data_dict, failed_status, po_reference, timezone):
    ''' Validates PO Date'''
    po_date = po_data.get('po_date', '')
    if po_date:
        try:
            if len(po_date.split()) == 1:  # Only date is provided
                po_date += " 00:00"
            ist_timezone = pytz.timezone(timezone)
            parsed_date = datetime.datetime.strptime(po_date, '%Y-%m-%d %H:%M')
            po_date = ist_timezone.localize(parsed_date)
            final_data_dict['po_date'] = str(parsed_date)
        except ValueError:
            update_error_message(failed_status, 5024, 'Invalid po_date Format', po_reference, reference=po_reference)

def validate_purchase_orders(orders, warehouse):
    NOW = datetime.datetime.now()
    final_data_dict = {}    
    ship_to = ''
    supplier_type = ''
    supplier_id = 0
    misc_types = [ 'show_mrp_raise_po' ]
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    timezone = get_user_time_zone(warehouse)
    try:
        failed_status = OrderedDict()
        if not orders:
            orders = {}
        if isinstance(orders, dict):
            orders = [orders]
        for ind, order in enumerate(orders):
            error_message = ''
            supplier_dict = {}
            po_reference = order.get('po_reference', '')
            is_purchase_request = order.get('is_purchase_request', '')
            is_actual_pr = order.get('is_actual_pr', '')
            create_supplier = order.get('create_supplier', True)
            for key in order.keys():
                if key == 'terms_condition':
                    final_data_dict[key] = ''
                    continue
                if not key == 'items':
                    final_data_dict[key] = ''
            final_data_dict['ship_to'] = ''
            ship_to = order.get('ship_to', '')
            
            #If Shipping Address Id is passed, getting Address of corresponding ID
            shipping_address_id = order.get('shipping_address_id', '')
            address_title = order.get('address_title', '')
            user_address = None
            if shipping_address_id:
                user_address = UserAddresses.objects.filter(shipping_address_id=shipping_address_id, user=warehouse.id)
                if user_address.exists():
                    user_address = user_address[0]
                else:
                    update_error_message(failed_status, 5024, "Invalid Shipping Address ID", po_reference, reference=po_reference)
            elif address_title:
                # If Address Title is passed, getting corresponding Address
                user_address = UserAddresses.objects.filter(address_name=address_title.lower(), user=warehouse.id)
                if user_address.exists():
                    user_address = user_address[0]
                else:
                    update_error_message(failed_status, 5024, "Invalid Address Title", po_reference, reference=po_reference)
            if user_address:
                ship_to = '%s %s, %s' % (user_address.address_name, user_address.address, user_address.pincode)

            if ship_to:
                final_data_dict['ship_to'] = ship_to
            else:
                user_address = UserAddresses.objects.filter(user=warehouse.id)
                if user_address.exists():
                    user_address = user_address[0]
                    final_data_dict['ship_to'] = '%s %s, %s' % (
                        user_address.address_name, user_address.address, user_address.pincode)
                else:
                    final_data_dict['ship_to'] = ''
                    error_message = 'No Address found, please add Address in Profile'
                    update_error_message(failed_status, 5024, error_message, po_reference, reference=po_reference)

            #PO Delivery Date Validation with Current Date
            po_delivery_date_validation(order, final_data_dict, failed_status, po_reference, timezone)
            #PO Date Validation
            po_date_validation(order, final_data_dict, failed_status, po_reference, timezone)
            #PO Extra Attributes validation
            extra_fields = order.get('extra_fields', {})
            error_messages = []
            #Skipping PO Extra Fields Validations, If key is passed
            if not order.get('skip_extra_fields_validation', False):
                error_messages, extra_fields = validate_attributes(warehouse, extra_fields, attribute_model='purchase_order')

            for error_msg in error_messages:
                update_error_message(failed_status, 5024, error_msg, po_reference, reference=po_reference)
            else:
                final_data_dict['extra_fields'] = extra_fields

            user_profile = warehouse.userprofile
            wh_currency = user_profile.base_currency
            timezone = 'Asia/Calcutta'
            if user_profile.timezone:
                timezone = user_profile.timezone

            final_data_dict['so_order_create'] = order.get('so_order_create','true')
            final_data_dict['po_type'] = order.get('po_type', 'Normal')
            final_data_dict['create_sale_order'] = order.get('create_sale_order')
            po_type = order.get('po_type', 'Normal')

            #Fetching Inco Terms for the Import PO
            if po_type.lower() == 'import':
                wh_inco_terms = get_misc_value('inco_terms', warehouse.id).split(',')
                inco_terms = [order.get('inco_terms', '')]
                if not set(inco_terms).issubset(wh_inco_terms):
                    error_message = "Inco Terms are not mapped to Warehouse - %s" %(
                        ','.join([inco for inco in inco_terms if inco not in wh_inco_terms]))
                    update_error_message(failed_status, 5024, error_message, po_reference, reference=po_reference)
                final_data_dict['inco_terms'] = order.get('inco_terms', '')
            
            #Supplier Validation
            supplier_id = order.get('supplier_id', '')
            supplier_reference = order.get('supplier_reference', '')
            supplier_address = order.get('supplier_address', '')
            if supplier_id or supplier_reference:
                if is_purchase_request != 'true' and is_actual_pr != 'true':
                    supplier_master = None
                    if supplier_id:
                        supplier_master = SupplierMaster.objects.filter(user=warehouse.id, supplier_id=supplier_id, status=1)
                    if supplier_reference and not supplier_master:
                        supplier_master = SupplierMaster.objects.filter(user=warehouse.id, supplier_reference=supplier_reference)
                    if supplier_master.exists():
                        supplier_master = supplier_master[0]
                    else:
                        supplier_master = None
                else:
                    supplier_master = None
                
                #Supplier Details Validation
                if supplier_master:
                    supplier_currency = supplier_master.currency_code
                    supplier_type = supplier_master.supplier_type
                    if po_type.lower() == "import" and supplier_type != 'import':
                        error_message = "Selected Supplier Doesn\'t match with PO Type Import"
                    elif supplier_type == 'import' and po_type.lower() != 'import':
                        error_message = "Selected PO type Doesn\'t match with Supplier Type Import"
                    elif po_type.lower() == "import" and supplier_type == 'import':
                        error_message = "Currency Exchange is not defined for this supplier on this date"
                        currency_exchange = CurrencyExchangeMaster.objects.filter(
                            from_currency=supplier_currency, to_currency=wh_currency, warehouse=warehouse, start_date__lte=NOW.date())
                        if currency_exchange.exists():
                            if currency_exchange.last().end_date:
                                end_date = get_local_date_known_timezone(timezone, currency_exchange.last().end_date, send_date=True).date()
                                if NOW.date() <= end_date:
                                    error_message = ''
                            else:
                                error_message = ''
                    else:
                        error_message = ''
                    if error_message:
                        update_error_message(failed_status, 5024, error_message, po_reference, reference=po_reference)
                    final_data_dict.setdefault('supplier_id', '')
                    final_data_dict['supplier_id'] = supplier_master.supplier_id

                    # Supplier ID or Supplier Reference can't be updated, so getting existing data
                    supplier_id = supplier_master.supplier_id
                    supplier_reference = supplier_master.supplier_reference
                else:
                    #Creating Supplier If Not Found in StockOne for NetSuite Integration
                    # With SupplierName and Supplier ID and Supplier Reference
                    if not create_supplier:
                        error_message = "Supplier does not exist"
                        update_error_message(failed_status, 5024, error_message, po_reference, reference=po_reference)
                    else:
                        if not supplier_reference:
                            error_message = 'Invalid Supplier Id, Supplier Reference is mandatory to create a new supplier'
                            update_error_message(failed_status, 5024, error_message, po_reference, reference=po_reference)
                        if not supplier_id:
                            # If Supplier ID is not passed, Supplier Reference will be Supplier ID
                            supplier_id = supplier_reference

                # Supplier data dict for Creation or Updation
                supplier_dict = {
                    'user' : warehouse.id,
                    'name' : order.get('supplier_name', ''),
                    'payment_terms' : order.get('supplier_payment_terms', ''),
                    'supplier_reference': supplier_reference,
                    'supplier_id' : supplier_id,
                }
                create_supplier_attributes = True if order.get('create_supplier_attributes', '') == 'true' else False
                if not supplier_master:
                    supplier_dict['address'] = supplier_address
                    supplier_dict['phone_number'] = order.get('supplier_phone', '')
                    supplier_dict['email_id'] = order.get('supplier_email', '')
                    supplier_dict['tin_number'] = order.get('supplier_tin', '')
                    if create_supplier_attributes and order.get('supplier_extra_fields', {}):
                        supplier_dict['attributes'] = order.get('supplier_extra_fields', {})
            else:
                error_message = 'Supplier Id Manadatory'
                update_error_message(failed_status, 5024, error_message, po_reference, reference=po_reference)

            if 'approval_remarks' in final_data_dict:
                final_data_dict['approval_remarks'] = order.get('approval_remarks')
            final_data_dict['terms_condition'] = order.get('terms_condition', '')

            sku_items = order.get('items')
            final_data_dict["po_name"] = ""
            sku_item_keys=['wms_code', 'order_quantity', 'measurement_unit',\
                 'price', 'mrp', 'sgst_tax', 'cgst_tax', 'igst_tax', 'utgst_tax', 'cess_tax', 'apmc_tax',
                'remarks', 'aux_data'
            ]
            for key in sku_item_keys:
                if key=='sku':
                    key = 'wms_code'
                final_data_dict[key] = ""
            if not sku_items:
                update_error_message(failed_status, 5024, 'Invalid Order format', po_reference, reference=po_reference)
                break

            #Checking PO Reference already exists
            if po_reference and supplier_id:
                po_exists_filter = {
                    'open_po__po_name' : po_reference,
                    'open_po__sku__user' : warehouse.id
                    }
                pending_po_exists_filter = {
                    'po_name' : po_reference,
                    'wh_user_id' : warehouse.id,
                }
                po_name_exists_check = PurchaseOrder.objects.filter(**po_exists_filter)
                po_name_exists_check_pending_po = PendingPO.objects.filter(
                    **pending_po_exists_filter).exclude(final_status__in=['saved', 'reverted'])
                open_po_exists_check = OpenPO.objects.filter(po_name=po_reference, sku__user=warehouse.id, purchaseorder__isnull=True)
                if po_name_exists_check.exists() or po_name_exists_check_pending_po.exists() or open_po_exists_check.exists():
                    update_error_message(failed_status, 5024, po_reference + " PO Reference already exists", po_reference, reference=po_reference)
            
            po_limit = 500
            if len(sku_items) > po_limit:
                update_error_message(failed_status, 5024, 'Cannot Raise More than %s SKUs to Supplier in a PurchaseOrder.' %po_limit, po_reference, reference=po_reference)
                break

            extra_data = {
                'po_reference' : po_reference,
                'supplier_type' : supplier_type,
                'po_type': final_data_dict.get('po_type', 'Normal'),
                'show_mrp_raise_po' : misc_dict.get('show_mrp_raise_po', 'false'),
                'supplier_id': supplier_id
            }
            sku_final_data_dict = sku_level_po_validation(warehouse, sku_items, failed_status, extra_data, False)
            final_data_dict['items'] = sku_final_data_dict
            for new_sku in sku_final_data_dict:
                for key,val in new_sku.items():
                    if key in final_data_dict:
                        final_data_dict[key] = val

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Update Order API failed for %s and params are %s and error statement is %s' % (
        str(warehouse.username), str(orders), str(e)))
        update_error_message(failed_status, 5020, "Create PO Failed", '', reference=po_reference)

    # Checking If any failed status
    if not failed_status and supplier_dict:
        try:
            # Creating or Updating Supplier with provided supplier data
            error_list = validate_supplier(supplier_dict, warehouse, on_po_raise=True, create_supplier_attributes = create_supplier_attributes)
            new_supplier = SupplierMaster.objects.filter(user=warehouse.id, supplier_id=supplier_id)
            if new_supplier.exists():
                supplier_master = new_supplier[0]
                final_data_dict.setdefault('supplier_id', '')
                final_data_dict['supplier_id'] = supplier_master.supplier_id
            else:
                # Returning any errors while creating or updating supplier
                for error in error_list['error']:
                    update_error_message(failed_status, 5024, error['message'], po_reference, reference=po_reference)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Supplier Creation or Updation on Raise PO API failed for %s and supplier params are %s and error statement is %s'
            %(str(warehouse.username), str(supplier_dict), str(e)))
            update_error_message(failed_status, 5024, 'Supplier Creation or Updation Failed', po_reference, reference=po_reference)

    return failed_status, final_data_dict, warehouse

def frame_sku_data(sku_items):
    all_sku_refs = []
    all_sku_codes = []
    line_refs = []
    for item in sku_items:
            sku_code = item.get('sku','')
            sku_ref = item.get('sku_reference','')
            line_reference = str(item.get('aux_data', {}).get('line_reference',''))

            if sku_code: all_sku_codes.append(sku_code)
            if sku_ref: all_sku_refs.append(sku_ref)
            if line_reference: line_refs.append(line_reference)

    return all_sku_refs, all_sku_codes, line_refs

def validate_taxlist(tax_list, tax_list2, negative_qty_list, negative_free_qty_list, message):
    if tax_list:
        tax_list = f'{MULTIPLE_TAX_MESSAGE} {", ".join(tax_list)}'
        if message:
            message = f'{message} and {tax_list}'
        else:
            message = tax_list

    if tax_list2:
        tax_list2 = f'{PO_IGST_TAX_MESSAGE} {", ".join(tax_list2)}'
        if message == 'success':
            message = f'{message} and {tax_list2}'
        else:
            message = tax_list

    if negative_qty_list:
        negative_qty_list = 'Quantities cannot be negative for SKU Code %s' % (', '.join(negative_qty_list))
        if message:
            message = f'{message} and {negative_qty_list}'
        else:
            message = negative_qty_list

    if negative_free_qty_list:
        negative_free_qty_list = 'PO not created. PO Free Quantity cannot be negative. Please pass a non negative PO Free Quantity for SKU Codes %s' % (', '.join(negative_free_qty_list))
        if message:
            message = f'{message} and {negative_free_qty_list}'
        else:
            message = negative_free_qty_list

    return message

def validate_tax_combination(final_data_dict, sku_item, tax_list, tax_list2, import_po):
    #Tax Combination Validation
    if final_data_dict['cgst_tax'] not in ['', None] and final_data_dict['igst_tax'] not in ['', None]:
        if float(final_data_dict['cgst_tax']) and float(final_data_dict['igst_tax']):
            tax_list.append(sku_item['sku'])
    if final_data_dict['sgst_tax'] not in ['', None] and final_data_dict['igst_tax'] not in ['', None]:
        if float(final_data_dict['sgst_tax']) and float(final_data_dict['igst_tax']):
            tax_list.append(sku_item['sku'])
    
    #Tax Validation for Import PO
    if final_data_dict['cgst_tax'] not in ['', None] and final_data_dict['sgst_tax'] not in ['', None]:
        if import_po and (float(final_data_dict['sgst_tax']) or float(final_data_dict['cgst_tax'])):
            tax_list2.append(sku_item['sku'])

    return tax_list, tax_list2

def validate_sku_key(sku_item, to_update, lr_sku_dict, po_aux_data):
    line_reference = str(sku_item.get('aux_data', {}).get('line_reference', ''))
    if line_reference:
        po_aux_data['line_reference'] = str(line_reference)
    sku_key = sku_item.get('sku')
    if not sku_key:
        sku_key = sku_item.get('sku_reference', '')
    if not sku_key and to_update and line_reference:
            sku_key = lr_sku_dict.get(line_reference)

    return sku_key, po_aux_data

def validate_duplicate_line_ref(line_reference, duplicate_line_ref, sku_key, failed_status, po_reference):
    if line_reference and line_reference in duplicate_line_ref:
        update_error_message(failed_status, 5020, "Duplicate Line Reference found", sku_key, 'sku_code', reference=po_reference)
    else:
        duplicate_line_ref.add(line_reference)
    
    return duplicate_line_ref

def get_mrp_price_values(sku_item, sku_master):
    unit_price = sku_item.get('price', 0)
    mrp = sku_item.get('mrp', 0)
    if sku_item.get('mrp', None) in [None, '']:
        mrp = sku_master['mrp']
    if sku_item.get('price', None) in [None, '']:
        unit_price = sku_master['cost_price']
    if unit_price:
        unit_price = float(unit_price)
    if mrp:
        mrp = float(mrp)

    return mrp, unit_price

def get_line_reference_skus(line_references):
    """
    Get the list of SKU codes corresponding to line references.
    """
    lr_skus = []

    lr_sku_dict = {}
    for line_reference, sku_code in line_references:
        lr_skus.append(sku_code)
        lr_sku_dict[line_reference] = sku_code
    return lr_skus, lr_sku_dict

def frame_sku_reference_dict(skus_list):
    """Frame SKU Line Reference Dict"""
    sku_dict = {}
    for sku in skus_list:
        code = sku.get('sku_code')
        ref = sku.get('sku_reference')
        sku_dict[code] = sku
        if ref:
            sku_dict[ref] = sku
    return sku_dict

def check_import_po_type(supplier_type, po_type):
    """ Check if PO Type and Supplier Type are matching for Import PO """
    if po_type.lower() == "import" and supplier_type == 'import':
        return True
    else:
        return False

def validate_expense_sku_type(sku_master, sku_key, sku_expense_check, sku_non_expense_check, failed_status, po_reference):
    """Validate Expense SKU Type in PO"""
    if sku_master['sku_type'] == 'Expense':
        sku_expense_check = True
    else:
        sku_non_expense_check = True
    if sku_expense_check and sku_non_expense_check:
        update_error_message(failed_status, 5020,
            "Cannot Raise Both Expense and Non Expense SKUs in PO", sku_key, 'sku_code', reference=po_reference)

def update_po_aux_data(line_reference, po_aux_data):
     """Update PO Aux Data"""
     if line_reference:
        po_aux_data['line_reference'] = str(line_reference)

def frame_negative_qty_list(to_update, final_data_dict, negative_qty_list, negative_free_qty_list, sku_item):
    """Frame Negative Quantity List"""
    if float(final_data_dict['order_quantity']) < 0:
        negative_qty_list.append(sku_item['sku'])
    if float(final_data_dict['free_quantity']) < 0:
        negative_free_qty_list.append(sku_item['sku'])
    return negative_qty_list, negative_free_qty_list

def validate_sku_level_po_status(line_reference, sku_item, po_sku_line_ref_dict, po_sku_status_dict, po_sku_line_id_dict, failed_status, sku_key, allow_po_edit_check, po_reference):
    po_sku_data = {}
    sku_code = sku_item.get('sku', '')
    if sku_item.get('id'):
        po_sku_data = po_sku_line_id_dict.get((sku_item['id']), {})
    if line_reference and not po_sku_data:
        po_sku_data = po_sku_line_ref_dict.get((sku_code, line_reference), {})
    elif not po_sku_data:
        po_sku_data = po_sku_status_dict.get(sku_code, {})
    continue_check = False
    cancelled_quantity = 0
    po_status = po_sku_data.get('status', '')
    update_quantity = float(sku_item.get('order_quantity', 0))
    received_quantity = float(po_sku_data.get('received_quantity', 0))
    order_quantity = float(po_sku_data.get('order_quantity', 0))
    cancel_qty = float(po_sku_data.get('cancelled_quantity', 0))
    saved_qty = float(po_sku_data.get('saved_quantity', 0))
    cancelled_quantity = order_quantity - update_quantity
    if po_status == 'grn-generated':
        if allow_po_edit_check == 'true' and saved_qty and update_quantity < saved_qty+received_quantity:
            update_error_message(failed_status, 5020, "Cannot Update Quantity less than Pending ASN Quantity and Received Quantity", sku_key, 'sku_code', reference=po_reference)
            continue_check = True
        elif allow_po_edit_check == 'false' and update_quantity > order_quantity:
            update_error_message(failed_status, 5020, "Cannot Update Quantity more than Order Quantity", sku_key, 'sku_code', reference=po_reference)
            continue_check = True
        elif update_quantity < received_quantity:
            update_error_message(failed_status, 5020, "Cannot Update Quantity less than Received Quantity", sku_key, 'sku_code', reference=po_reference)
            continue_check = True
    elif po_status == 'location-assigned' and update_quantity != order_quantity - cancel_qty:
            update_error_message(failed_status, 5020, "PO already cancelled for this SKU", sku_key, 'sku_code', reference=po_reference)
            continue_check = True
    elif po_status == 'confirmed-putaway' and update_quantity != order_quantity:
        update_error_message(failed_status, 5020, "Putaway done for this SKU", sku_key, 'sku_code', reference=po_reference)
        continue_check = True
    elif not po_status and allow_po_edit_check == 'true' and update_quantity < saved_qty:
            update_error_message(failed_status, 5020, "Cannot Update Quantity less than Pending ASN Quantity", sku_key, 'sku_code', reference=po_reference)
            continue_check = True
    return continue_check, cancelled_quantity

def validate_float_fields(float_fields, sku_item):
    invalid_fields = []

    for field in float_fields:
        try:
            # Attempt to convert each field to float
            if sku_item.get(field, 0):
                float(sku_item.get(field, 0))
        except ValueError:
            # If conversion fails, add to invalid_fields
            invalid_fields.append(field)

    return invalid_fields

def validate_picklist_status_for_update(sku_item, picklist_line_references, picklist_sku_codes):
    """Validate Picklist Status for Update"""
    line_reference = str(sku_item.get('aux_data', {}).get('line_reference', ''))
    sku_code = sku_item.get('sku', '')
    if line_reference in picklist_line_references or sku_code in picklist_sku_codes:
        return "Item is in progress, hence not allowing to update"
    return ''

def sku_level_po_validation(warehouse, sku_items=[], failed_status={}, extra_data={}, to_update=False):
    """
    Function to validate sku level details of PO

    Args:
        warehouse (User object): Warehouse user whose PO should be updated
        sku_items (list): PO - Item level details
        failed_status (dict, optional): To keep track of validation errors. Defaults to {}.
        extra_data (dict, optional): Holds header level data required for preparing final dict. Defaults to {}.
        to_update (bool, optional): Whether the PO is being updated or created. Defaults to False.

    Returns:
        dict: Returns final_data_dict which can be used to create new entries in OpenPO and PurchaseOrder
              assuming header level validations are already done
    """

    all_sku_refs = []
    all_sku_codes = []
    line_refs = []
    final_data_dict = {}
    tax_list = []
    tax_list2 = []
    final_data_list = []
    negative_qty_list = []
    negative_free_qty_list = []
    attributes = get_user_attributes(warehouse, "po_line_extra_fields")
    try:
        po_number = extra_data.get('po_number', '')
        po_type = extra_data.get('po_type', 'Normal')
        po_reference = extra_data.get('po_reference', '')
        supplier_type = extra_data.get('supplier_type','')
        supplier_obj = extra_data.get('supplier_obj')
        show_mrp_raise_po = extra_data.get('show_mrp_raise_po', 'false')
        po_sku_line_ref_dict = extra_data.get('po_sku_line_ref_dict', {})
        po_sku_status_dict = extra_data.get('po_sku_status_dict', {})
        po_sku_line_id_dict = extra_data.get('po_sku_line_id_dict', {})
        asn_sku_list = extra_data.get('asn_sku_list', [])
        asn_sku_line_ref_list = extra_data.get('asn_sku_line_ref_list', [])
        allow_unitprice_gt_mrp = extra_data.get('allow_unitprice_gt_mrp', '')
        supplier_id = extra_data.get('supplier_id', '')
        update_order_flag = extra_data.get('update_order_flat', True)
        picklist_line_references = extra_data.get('picklist_line_references', [])
        picklist_sku_codes = extra_data.get('picklist_sku_codes', [])

        allow_po_edit_check = get_misc_value('allow_po_update_with_open_asn_grn', warehouse.id)
        all_sku_refs, all_sku_codes, line_refs = frame_sku_data(sku_items)

        line_references = list(PurchaseOrder.objects.filter(
            Q(po_number= po_number) | Q(open_po__po_name=po_reference), 
            open_po__sku__user=warehouse.id, line_reference__in=line_refs,
            ).values_list('line_reference', 'open_po__sku__sku_code'))
        
        #Fetching sku line references
        lr_skus, lr_sku_dict = get_line_reference_skus(line_references)

        all_sku_codes.extend(lr_skus)
        sku_qs = SKUMaster.objects.filter(
            Q(sku_code__in=all_sku_codes) | Q(sku_reference__in=all_sku_refs),
            user=warehouse.id
        ).values(
            'id', 'sku_code','sku_reference',
            'sku_type','mrp','cost_price','product_type',
            'batch_based', 'enable_serial_based'
        )

        #Frame SKU
        skus_list = list(sku_qs)
        sku_dict = frame_sku_reference_dict(skus_list)
        import_po = check_import_po_type(supplier_type, po_type)
        sku_expense_check = False
        sku_non_expense_check = False
        duplicate_line_ref = set()
        float_fields = [
            'order_quantity','free_quantity', 'price', 'mrp',
            'sgst_tax', 'cgst_tax', 'igst_tax', 'utgst_tax', 'cess_tax'
        ]

        dest_user, dest_sku_dict = '', {}
        if po_type.lower() == 'stocktransfer':
            dest_user_obj = User.objects.filter(username=supplier_id)
            if dest_user_obj.exists():
                dest_user = dest_user_obj[0]
                dest_sku_dict = get_sku_data(all_sku_codes, dest_user)

        for sku_item in sku_items:
            po_aux_data = sku_item.get('aux_data', {})
            line_reference = str(sku_item.get('aux_data', {}).get('line_reference', ''))
            update_po_aux_data(line_reference, po_aux_data)

            if po_type.lower() == 'stocktransfer' and update_order_flag:
                sku_error, _, _ = get_wms_data(sku_item, sku_dict, dest_sku_dict, dest_user)
                if sku_error:
                    update_error_message(failed_status, 5020, sku_error, sku_item['sku'], 'sku_code', reference=po_reference)
                    continue
                error_msg = validate_picklist_status_for_update(sku_item, picklist_line_references, picklist_sku_codes)
                if error_msg:
                    update_error_message(failed_status, 5020, error_msg, sku_item['sku'], 'sku_code', reference=po_reference)
                    continue

            sku_key, po_aux_data = validate_sku_key(sku_item, to_update, lr_sku_dict, po_aux_data)

            if not sku_key:
                update_error_message(failed_status, 5024, 'SKU Code or Reference is Mandatory', po_reference, reference=po_reference)
                continue

            invalid_fields = validate_float_fields(float_fields, sku_item)
            if invalid_fields:
                update_error_message(failed_status, 5024, 'Invalid Fields: '+','.join(invalid_fields) , sku_key, 'sku_code', reference=po_reference)
                continue

            if all_sku_codes.count(sku_key) > 1 and not (line_reference or sku_item.get('id')):
                update_error_message(failed_status, 5020, "ID or PO Line Reference is Required for Duplicate SKU", sku_key, 'sku_code', reference=po_reference)
                continue

            sku_master = sku_dict.get(sku_key,'')
            if not sku_master:
                update_error_message(failed_status, 5020, "SKU Not found in Stockone", sku_key, 'sku_code', reference=po_reference)
                continue
            
            duplicate_line_ref = validate_duplicate_line_ref(line_reference, duplicate_line_ref, sku_key, failed_status, po_reference)

            # Line Reference Uniqueness in Warehouse Validation
            if line_reference in line_references:
                update_error_message(failed_status, 5020, "Line Reference Already Exists", sku_key, 'sku_code', reference=po_reference)

            #Expense SKU Validation in PO
            validate_expense_sku_type(sku_master, sku_key, sku_expense_check, sku_non_expense_check, failed_status, po_reference)
            
            mrp, unit_price = get_mrp_price_values(sku_item, sku_master)

            if allow_unitprice_gt_mrp != 'true' and unit_price and mrp !=0 and show_mrp_raise_po == 'true' and unit_price > mrp:
                update_error_message(failed_status, 5020,
                        "UnitPrice cannot be greater than MRP", sku_item['sku'], 'sku_code', reference=po_reference)
                continue

            #PO SKU Level Status Validation
            continue_check, cancelled_quantity = validate_sku_level_po_status(line_reference, sku_item, po_sku_line_ref_dict, po_sku_status_dict, po_sku_line_id_dict, failed_status, sku_key, allow_po_edit_check, po_reference)
            sku_item['cancelled_quantity'] = cancelled_quantity
            if continue_check:
                continue
            sku_asn_check = (sku_key, line_reference) in asn_sku_line_ref_list if line_reference else (sku_key in asn_sku_list)
            #ASN Exists Validation for SKU
            if sku_asn_check and allow_po_edit_check == 'false':
                update_error_message(failed_status, 5020, "ASN generated for this SKU cannot update the PO", sku_key, 'sku_code', reference=po_reference)
                continue

            supplier_code, supplier_id = '', ''
            if supplier_obj:
                supplier_id = supplier_obj.id
                supplier_code = supplier_obj.supplier_id
                supplier_type = supplier_obj.supplier_type


            final_data_dict= {
                'po_number' : po_number,
                'mrp' : mrp,
                'price' : unit_price,
                'po_reference' : po_reference,
                'po_type' : po_type,
                'supplier_code' : supplier_code,
                'supplier_id' : supplier_id,
                'supplier_type' : supplier_type,
                'sku_id' : sku_master.get('id',''),
                'sku': sku_master.get('sku_code',''),
                'wms_code' : sku_master.get('sku_code'),
                'sgst_tax' : sku_item.get('sgst_tax',0),
                'cgst_tax' : sku_item.get('cgst_tax',0),
                'igst_tax' : sku_item.get('igst_tax',0),
                'cess_tax' : sku_item.get('cess_tax',0),
                'apmc_tax' : sku_item.get('apmc_tax',0),
                'remarks' : sku_item.get('remarks',''),
                'aux_data' : po_aux_data,
                'utgst_tax' :  sku_item.get('utgst_tax',0),
                'tax_type' : sku_master.get('product_type',''),
                'order_quantity' : sku_item.get('order_quantity',0),
                'measurement_unit' : sku_item.get('measurement_unit',''),
                'free_quantity' : sku_item.get('free_quantity',0),
                'line_extra_fields' : sku_item.get('line_extra_fields',''),
            }
            tax_list, tax_list2 = validate_tax_combination(final_data_dict, sku_item, tax_list, tax_list2, import_po)

            error_messages = []
            if final_data_dict.get('line_extra_fields'):
                error_messages, extra_fields=validate_attributes(warehouse, final_data_dict['line_extra_fields'], "po_line_extra_fields", attributes)
                for error_msg in error_messages:
                    update_error_message(failed_status, 5024, error_msg, po_reference, reference=po_reference)

            #Quantity Validation for the Negative Quantities
            negative_qty_list, negative_free_qty_list = frame_negative_qty_list(to_update, final_data_dict, negative_qty_list, negative_free_qty_list, sku_item)
            final_data_dict['po_name'] = po_reference

            final_data_list.append(final_data_dict)

        message = ''
        message = validate_taxlist(tax_list, tax_list2, negative_qty_list, negative_free_qty_list, message)
        if message:
            update_error_message(failed_status, 5024, message, po_reference, reference=po_reference)
    except Exception:
        import traceback
        log.debug(traceback.format_exc())

    return final_data_list
