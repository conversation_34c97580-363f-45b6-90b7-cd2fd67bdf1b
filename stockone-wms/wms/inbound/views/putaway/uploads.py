# python imports
import traceback
from collections import OrderedDict
from typing import Dict, List, Tuple, Any, Union
# pandas is used in other parts of the code
import pandas as pd
from django.core.cache import cache
from wms.celery import app as celery_app

# wms base imports
from wms_base.wms_utils import init_logger, rename_dict_keys
from core_operations.views.common.main import get_misc_value
from wms_base.models import User

# inventory imports
from inventory.models import LocationMaster, StockDetail
from inventory.views.locator.validations import get_sku_master_dict

# inbound imports
from inbound.models import POLocation
from inbound.serializers.putaway import PutawaySerializer
from inbound.views.putaway.validation import get_existing_serials, fetch_serial_numbers_for_sps_id, validate_putaway
from inbound.views.putaway.confirmation import ConfirmPutaway
from inbound.views.putaway.suggestions import save_remaining_putaway_quantity

# typing imports
from typing import List, Dict, Tuple
from collections import defaultdict, OrderedDict

# Constants for putaway upload
PUTAWAY_UPLOAD_HEADERS = OrderedDict([
    ('GRN*', 'grn_number'),
    ('SKUCode*', 'sku_code'),
    ('Location*', 'location'),
    ('Batch(Mandatory if in GRN)', 'batch_number'),
    ('Quantity*', 'putaway_quantity'),
    ('LPN Id (Mandatory if in GRN)', 'lpn_number'),
    ('Serial Numbers(If SKU is serialised)', 'serial_number'),
    ('Auto Putaway(Yes/No)', 'auto_putaway')
])

log = init_logger('logs/putaway_upload.log')

def putaway_form(warehouse, extra_params={}):
    """
    Returns the headers for the putaway upload template.

    Args:
        warehouse: The warehouse user object
        extra_params: Additional parameters (optional)

    Returns:
        OrderedDict: Headers for the putaway upload template
    """
    log.info("Request for Putaway Form user {}, extra params {}".format(warehouse.username, extra_params))
    return PUTAWAY_UPLOAD_HEADERS

def putaway_upload(request, warehouse, data_list, extra_params={}):
    """
    Handles the upload of putaway data.

    Args:
        request: The HTTP request object
        warehouse: The warehouse user object
        data_list: List of putaway data from the uploaded file
        extra_params: Additional parameters (optional)

    Returns:
        str or list: 'Success' if successful, otherwise a list of dictionaries with original data and error status
    """
    try:
        log.info("Request for Putaway Upload for user {}, extra params {}".format(request.username, extra_params))

        # Process the data list
        # Rename the keys according to the mapping
        data_list = rename_dict_keys(PUTAWAY_UPLOAD_HEADERS, data_list)

        # Group data by GRN, SKU, batch, and LPN to track quantities
        grouped_data = group_data_by_key(data_list)

        # Validate data - use _ for unused variables
        auto_putaway_yes_data, auto_putaway_no_data, error_list, validation_data = validate_putaway_form(warehouse, data_list, grouped_data)

        # If there are errors, return the original data with error status
        if error_list:
            log.info(f"Putaway validation failed with {len(error_list)} errors")
            response_data = format_error_response(error_list, data_list)
            return response_data

        # Merge items with the same key attributes and combine their serial numbers
        merged_auto_putaway_yes_data = merge_by_polocation_id(auto_putaway_yes_data)
        merged_auto_putaway_no_data = merge_by_polocation_id(auto_putaway_no_data)

        # Handle auto_putaway_yes_data
        result = process_putaway_data(merged_auto_putaway_yes_data, validation_data, warehouse, request, extra_params)
        if result:
            return result

        # Handle auto_putaway_no_data - Split existing records
        split_polocation.apply_async(args = [warehouse.id, merged_auto_putaway_no_data])

        return 'Success'

    except Exception as e:
        log.error(f"Putaway Upload Failed with error: {str(e)}")
        log.error(traceback.format_exc())
        return f"Failed: {str(e)}"


# Helper functions for mapping items to POLocations
@celery_app.task
def split_polocation(warehouse_id, items: List[Dict]) -> Union[Dict, None]:
    """Split POLocation records into two based on the provided items data.
    
    Args:
        items: List of dictionaries, each containing polocation_id, putaway_quantity and location_id
    
    Returns:
        Dict with error message if split fails, None if successful
    """
    warehouse = User.objects.get(id = warehouse_id)

    if not items:
        return None
    
    # Get all polocation IDs
    polocation_ids = [item.get('id') for item in items]
    
    # Fetch all POLocation records in one query
    po_locs = (
        POLocation.objects
        .select_related('seller_po_summary')
        .filter(
            id__in=polocation_ids,
            status='1'  # Only get active records
        )
    )
    
    # Create a mapping of id to POLocation for quick access
    po_loc_map = {str(po_loc.id): po_loc for po_loc in po_locs}


    # Get all relevant stock objects
    stock_queryset = StockDetail.objects.filter(
        sku__user = warehouse.id, receipt_number__in = po_loc_map.keys(), quantity__gt = 0, receipt_type = 'grn_packing',
        status = 0
    )
    
    # Create mapping of receipt_number to list of stocks
    stock_objs = {}
    new_stocks_to_create = []
    for stock in stock_queryset:
        if stock.receipt_number not in stock_objs:
            stock_objs[stock.receipt_number] = [stock]
        else:
            stock_objs[stock.receipt_number].append(stock)
    for item in items:
        polocation_id = item.get('id')
        new_quantity = float(item.get('putaway_quantity', 0))
        
        # Get original record from map
        original_po_loc = po_loc_map.get(str(polocation_id))
        if not original_po_loc:
            error_msg = f"No active POLocation found for ID {polocation_id}"
            log.error(f"Split failed: {error_msg}")
        
        lpn_number = original_po_loc.json_data.get('lpn_number') if original_po_loc.json_data else None
        log.info(f"Processing split for POLocation {polocation_id}, LPN {lpn_number}:")
        log.info(f"Original record - Location: {original_po_loc.location}, Quantity: {original_po_loc.quantity}")
        
        # Validate quantity
        if new_quantity > original_po_loc.quantity:
            error_msg = f"Cannot split more quantity than available for LPN {lpn_number}"
            log.error(f"Split failed: {error_msg}")

        
        # Get original quantity and reduce it
        original_quantity = original_po_loc.quantity
        remaining_quantity = original_quantity - new_quantity
        
        # Update original record with reduced quantity
        original_po_loc.quantity = remaining_quantity
        original_po_loc.original_quantity = remaining_quantity

        if remaining_quantity <= 0:
            original_po_loc.status = 0
        original_po_loc.save(update_fields=['quantity', 'status', 'original_quantity'])
        log.info(f"Updated original record - Quantity reduced from {original_quantity} to {remaining_quantity}")
        
        # Create new record with the split quantity
        json_data = original_po_loc.json_data.copy() if original_po_loc.json_data else {}
        log.info(f"Creating new record - Location ID: {item['location_id']}, Quantity: {new_quantity}")
        
        # Use save_remaining_putaway_quantity to create new record
        new_po_loc = save_remaining_putaway_quantity(
            po_loc=original_po_loc,
            quantity=new_quantity,  # New quantity for split
            remaining_quantity=new_quantity,  # This becomes original_quantity
            location_id=item['location_id'],  # Get location_id from item
            json_data=json_data
        )

        # Handle staging stocks
        stocks = stock_objs.get(polocation_id)
        if stocks:
            remaining_to_reduce = new_quantity
            
            # Get first stock to use as template for new stock
            template_stock = stocks[0]
            
            # Create new stock dict for this item
            new_stock_dict = template_stock.__dict__.copy()
            for field in ['_state', 'id', 'created_at', 'updated_at']:
                new_stock_dict.pop(field, None)
            
            new_stock_dict.update({
                'receipt_number': str(new_po_loc.id),
                'quantity': new_quantity,
            })
            new_stocks_to_create.append(new_stock_dict)
            
            # Reduce quantity from existing stocks
            for stock in stocks:
                if remaining_to_reduce <= 0:
                    break

                reduction = min(stock.quantity, remaining_to_reduce)
                stock.quantity -= reduction
                remaining_to_reduce -= reduction
                stock.save()

        # Update the quantities after creation
        new_po_loc.quantity = new_quantity
        new_po_loc.status = 1  # Set to active
        new_po_loc.save(update_fields=['quantity', 'status'])

        # Validate the split was successful
        if not new_po_loc:
            error_msg = f"Failed to create new record for LPN {lpn_number}"
            log.error(f"Split failed: {error_msg}")
            return {
                'status': False,
                'error_message_dict': error_msg
            }
        
        log.info(f"Split successful - New record ID: {new_po_loc.id}")
    
    # Bulk create all new stocks
    if new_stocks_to_create:
        StockDetail.objects.bulk_create([StockDetail(**stock_dict) for stock_dict in new_stocks_to_create])
    
    return None

def format_error_response(error_list: List[Dict], data_list: List[Dict]) -> List[Dict]:
    """Format error response for putaway validation failures.
    
    Args:
        error_list: List of error dictionaries containing row numbers and error messages
        data_list: Original data list to be updated with error status
    
    Returns:
        List of dictionaries in original format with added error status
    """
    # Create a mapping of row numbers to errors
    error_map = {error['row']: error['errors'] for error in error_list}

    # Add status to each item in the data_list
    for index, item in enumerate(data_list):
        row_num = index + 2  # +2 for header row and 1-based indexing
        if row_num in error_map:
            # Store errors as a list in the Status column
            item['Status'] = error_map[row_num]
        else:
            item['Status'] = []

    # Convert back to original format using rename_dict_keys with reverse=True
    return rename_dict_keys(PUTAWAY_UPLOAD_HEADERS, data_list, reverse=True)

def process_putaway_data(data_list: List[Dict], validation_data: Dict, warehouse, request, extra_params: Dict) -> Union[Dict, List[Dict]]:
    """Process putaway data for confirmation."""
    if not data_list:
        return []
    putaway_data = format_putaway_data(data_list, validation_data)

    serializer = PutawaySerializer(data=putaway_data, many=True)
    if not serializer.is_valid():
        error_list = []
        for index, row_errors in enumerate(serializer.errors):
            formatted_errors = [f"{field}: {', '.join(errors)}" for field, errors in row_errors.items()]
            error_list.append({
                'row': index + 2,  # account for header + 1-based indexing
                'errors': formatted_errors
            })
        return format_error_response(error_list, data_list)

    validated_data_list = serializer.validated_data

    error_list = []
    successful_data = []

    for index, validated_data in enumerate(validated_data_list):
        validated_error_dict, po_loc_dict_data, putaway_result = validate_putaway(validated_data, warehouse=warehouse)

        if validated_error_dict:
            formatted_errors = [f"{field}: {error}" for field, error in validated_error_dict.items()]
            error_list.append({
                'row': index + 2,
                'errors': formatted_errors
            })
        else:
            successful_data.append((po_loc_dict_data, putaway_result))

    if error_list:
        return format_error_response(error_list, data_list)

    for po_loc_dict_data, putaway_data_list in successful_data:
        if po_loc_dict_data:
            putaway_extra_dict = {
                'request_user': request.username,
                'headers': {
                    'Warehouse': extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_WAREHOUSE', ''),
                    'Authorization': extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_AUTHORIZATION', '')
                },
                'request_meta': extra_params.get('json_data', {}).get('request_meta', {}),
                'request_scheme': extra_params.get('json_data', {}).get('request_meta', {}).get('request_scheme', '')
            }
            try:
                ConfirmPutaway().confirm(putaway_data_list, warehouse, putaway_extra_dict)
            except Exception as e:
                return {'error': f'Putaway confirmation failed: {str(e)}'}


def safe_float(value):
    """Safely convert a value to float, returning 0.0 if conversion fails."""
    try:
        return float(value)
    except (TypeError, ValueError):
        return 0.0

def create_mapped_item(base_item, pol_id, qty):
    """Create a mapped item with the given POLocation ID and quantities."""
    mapped = base_item.copy()
    mapped['id'] = pol_id
    mapped['putaway_quantity'] = qty

def format_putaway_data(items: List[Dict], validation_data: Dict) -> List[Dict]:
    """
    Format putaway data according to the required serializer format.
    
    Args:
        items: List of merged putaway items
        validation_data: Dictionary containing validation data including po_loc_data
    
    Returns:
        List of formatted putaway requests
    """
    # Group items by GRN number
    grn_groups = defaultdict(list)
    for item in items:
        grn_number = item.get('grn_number')
        grn_groups[grn_number].append(item)
    
    formatted_data = []
    
    # Process each GRN group
    for grn_number, grn_items in grn_groups.items():
        formatted_items = []
        
        # Get PO number and supplier info from validation data
        po_number = None
        grn_date = None
        
        for item in grn_items:
            sku_code = item.get('sku_code')
            grn_data = validation_data.get('grn_data', {}).get(grn_number, {})
            sku_data = grn_data.get(sku_code, {})
            if sku_data:
                po_number = sku_data.get('po_number')
                grn_date = sku_data.get('grn_date')
        
        # Format each item in the GRN group
        for item in grn_items:
            # Handle serial numbers - convert single serial_number to list if present
            serial_numbers = item.get('serial_numbers', [])
            if not serial_numbers and item.get('serial_number'):
                serial_numbers = [item['serial_number']]

            formatted_item = {
                'sku_code': item.get('sku_code'),
                'id': item.get('id'),
                'original_quantity': item.get('original_quantity', 0),
                'po_price': 0,
                'buy_price': 0,
                'mrp': 0,
                'batch_number': item.get('batch_number', ''),
                'putaway_quantity': item.get('putaway_quantity'),
                'zone': item.get('zone', 'DEFAULT'),
                'location': item.get('location'),
                'lpn_number': item.get('lpn_number', ''),
                'serial_numbers': serial_numbers,
                'po_number': po_number,
                'grn_reference': grn_number,
                'grn_type': 'PO',
                'putaway_type': 'po_putaway',
                'grn_date': grn_date
            }
            formatted_items.append(formatted_item)
        
        # Create the putaway request
        putaway_request = {
            'source': 'WEB',
            'putaway_type': 'po_putaway',
            'items': formatted_items,
            'grn_type': 'PO'
        }
        formatted_data.append(putaway_request)
    
    return formatted_data

def find_fallback_po_id(grn_key, validation_data):
    """Find a fallback POLocation ID when no exact match is found."""
    if 'po_loc_ids_by_key' not in validation_data:
        return 0
    for key, locations in validation_data['po_loc_ids_by_key'].items():
        if key[:3] == grn_key[:3]:  # Match on GRN, SKU, batch
            for loc, (pol_id, _) in locations.items():
                return pol_id
    return 0

def allocate_from_location(item, location, available_locations, remaining_quantities, grn_qty_key, quantity_to_allocate):
    """
    Allocate quantity from a specific location.

    Returns:
        Tuple of (mapped_item, quantity_allocated) if successful, (None, 0) otherwise
    """
    if location not in available_locations:
        return None, 0

    pol_id, available_qty = available_locations[location]
    if available_qty <= 0:
        return None, 0

    # Use the minimum of requested and available quantity
    qty_to_use = min(quantity_to_allocate, available_qty)
    remaining_quantities[grn_qty_key][location] = (pol_id, available_qty - qty_to_use)
    mapped_item = create_mapped_item(item, pol_id, qty_to_use, available_qty)

    return mapped_item, qty_to_use

def map_item_to_polocation(item: Dict, validation_data: Dict) -> List[Dict]:
    """
    Maps a single putaway item to its corresponding POLocation ID.
    No merging or splitting is performed, just simple ID mapping.
    
    Args:
        item: A single putaway data item from the upload
        validation_data: Dictionary containing validation data
        remaining_quantities: Dictionary to track remaining quantities

    Returns:
        List containing a single mapped item with POLocation ID
    """
    # Extract item details
    grn_number = item.get('grn_number')
    sku_code = item.get('sku_code')
    batch_number = item.get('batch_number', '')
    lpn_number = item.get('lpn_number', '')
    
    # Get POLocation data
    po_locations = validation_data.get('po_loc_ids_by_key', {})
    
    # Find matching POLocation based on SKU, batch, and LPN
    for key, locations in po_locations.items():
        _, sku_key, batch_key, lpn_key = key
        
        # Match SKU, batch, and LPN
        if (sku_key == sku_code and 
            batch_key == batch_number and 
            lpn_key == lpn_number):
            
            # Take the first location's POLocation ID and quantity
            # (since all locations in DB are DFLT2 anyway)
            for loc, (pol_id, quantity) in locations.items():
                mapped_item = item.copy()
                mapped_item['id'] = pol_id
                mapped_item['original_quantity'] = quantity
                return [mapped_item]
    
    # If no match found, use fallback
    fallback_po_id = find_fallback_po_id((grn_number, sku_code, batch_number), validation_data)
    mapped_item = item.copy()
    mapped_item['id'] = fallback_po_id
    mapped_item['original_quantity'] = 0.0
    
    return [mapped_item]

def safe_float(value):
    try:
        return float(value)
    except (TypeError, ValueError):
        return 0.0

def merge_by_polocation_id(items: List[Dict]) -> List[Dict]:
    """
    Merge items with the same POLocation ID and combine their serial numbers if present.
    Args:
        items: List of mapped items with POLocation IDs
    Returns:
        List of merged items with combined serial numbers
    """
    if not items:
        return []

    # Group items by POLocation ID
    grouped_items = defaultdict(list)
    for item in items:
        grouped_items[item.get('id') or id(item)].append(item)

    # Process each group
    merged_items = []
    
    # Process each group
    for items_group in grouped_items.values():
        # Skip merging if only one item
        if len(items_group) == 1:
            merged_items.append(items_group[0])
            continue

        # Create merged item from first item
        merged_item = items_group[0].copy()
        merged_item.update({
            'putaway_quantity': sum(safe_float(item.get('putaway_quantity', 0)) for item in items_group),
            'original_quantity': items_group[0].get('original_quantity', 0)
        })

        # Collect and merge serial numbers if they exist
        all_serials = set()
        for item in items_group:
            if item.get('serial_number'):
                all_serials.add(item['serial_number'])
            if isinstance(item.get('serial_numbers'), list):
                all_serials.update(sn for sn in item['serial_numbers'] if sn)

        if all_serials:
            merged_item['serial_numbers'] = list(all_serials)
            if not merged_item.get('serial_number'):
                merged_item['serial_number'] = next(iter(all_serials))

        merged_items.append(merged_item)

    return merged_items


def group_data_by_key(data_list: List[Dict]) -> Dict:
    """
    Group data by GRN, SKU, batch, and LPN to track quantities.

    Args:
        data_list: List of putaway data items

    Returns:
        Dictionary with (grn_number, sku_code, batch_number, lpn_number) keys and summed quantities
    """
    grouped_data = {}
    for item in data_list:
        grn_number = item.get('grn_number')
        sku_code = item.get('sku_code')
        batch_number = item.get('batch_number', '')
        lpn_number = item.get('lpn_number', '')

        key = (grn_number, sku_code, batch_number, lpn_number)
        grouped_data.setdefault(key, 0)

        try:
            quantity = float(item.get('putaway_quantity', 0))
        except (ValueError, TypeError):
            quantity = 0

        grouped_data[key] += quantity

    return grouped_data


def _extract_unique_values(data_list: List[Dict]) -> Dict:
    """Extract unique values from data list for validation."""
    values = {
        'sku_codes': set(),
        'grn_numbers': set(),
        'locations': set(),
        'batch_numbers': set(),
        'serial_numbers': set(),
        'lpn_numbers': set()
    }

    for item in data_list:
        values['sku_codes'].add(item.get('sku_code', ''))
        values['grn_numbers'].add(item.get('grn_number', ''))
        values['locations'].add(item.get('location', ''))

        if item.get('batch_number'):
            values['batch_numbers'].add(item.get('batch_number'))

        if item.get('serial_number'):
            values['serial_numbers'].add(item.get('serial_number'))

        if item.get('lpn_number'):
            values['lpn_numbers'].add(item.get('lpn_number'))

    # Remove empty values
    for key in values:
        values[key].discard('')

    return values

def _get_po_location_values() -> List[str]:
    """Return the list of fields to fetch from POLocation."""
    return [
        "seller_po_summary__batch_detail__batch_no",
        "seller_po_summary__batch_detail__mrp",
        "seller_po_summary__price",
        "seller_po_summary__grn_reference",
        "seller_po_summary_id",
        "id",
        "location__location",
        "quantity",
        "seller_po_summary__purchase_order__po_number",
        "sku__sku_code",
        "sku__sku_desc",
        "sku__batch_based",
        "sku__enable_serial_based",
        "json_data__lpn_number"
    ]

def _process_po_location_data(po_location_objs) -> Tuple[Dict, Dict, List, Dict]:
    """Process PO location objects to build GRN and batch data."""
    grn_data, batch_data, po_loc_ids_by_key = {}, {}, {}
    sps_ids = []
    for po_loc in po_location_objs:
        # Extract all values at once
        grn_number = po_loc.get('seller_po_summary__grn_reference')
        sku_code = po_loc.get('sku__sku_code')
        batch_no = po_loc.get('seller_po_summary__batch_detail__batch_no', '') or ''
        sps_id = po_loc.get('seller_po_summary_id')
        location = po_loc.get('location__location')
        quantity = po_loc.get('quantity', 0)
        po_loc_id = po_loc.get('id')
        lpn_number = po_loc.get('json_data__lpn_number', '') or ''

        # Collect sps_id and initialize data structures
        if sps_id: sps_ids.append(sps_id)
        if grn_number not in grn_data: grn_data[grn_number] = {}

        # Initialize SKU data if needed
        if sku_code not in grn_data[grn_number]:
            grn_data[grn_number][sku_code] = {
                'batch_nos': set(), 'is_batch_based': po_loc.get('sku__batch_based'),
                'is_serial_based': po_loc.get('sku__enable_serial_based'), 'total_quantity': 0,
                'locations': {}, 'lpn_numbers': set(), 'sku_desc': po_loc.get('sku__sku_desc'),
                'po_number': po_loc.get('seller_po_summary__purchase_order__po_number'),
                'mrp': po_loc.get('seller_po_summary__batch_detail__mrp'),
                'price': po_loc.get('seller_po_summary__price'), 'sps_ids': set()
            }

        # Update GRN data and store location data
        sku_data = grn_data[grn_number][sku_code]
        if sps_id: sku_data['sps_ids'].add(sps_id)
        if lpn_number: sku_data['lpn_numbers'].add(lpn_number)
        sku_data['total_quantity'] += quantity
        sku_data['locations'].setdefault(location, []).append({'id': po_loc_id, 'quantity': quantity})

        # Store POLocation ID by key - include LPN in the key only when available
        grn_qty_key = (grn_number, sku_code, batch_no, lpn_number)
        po_loc_ids_by_key.setdefault(grn_qty_key, {})[location] = (po_loc_id, quantity)

        # Process batch data if batch_no exists
        if batch_no:
            sku_data['batch_nos'].add(batch_no)
            batch_key = (grn_number, sku_code, batch_no, lpn_number)
            if batch_key not in batch_data:
                batch_data[batch_key] = {'total_quantity': 0, 'locations': {}, 'lpn_number': lpn_number}
            batch_data[batch_key]['total_quantity'] += quantity
            batch_data[batch_key]['locations'].setdefault(location, []).append({'id': po_loc_id, 'quantity': quantity})

    return grn_data, batch_data, sps_ids, po_loc_ids_by_key

def fetch_putaway_data(warehouse, data_list: List[Dict]) -> Dict:
    """
    Fetches data needed for putaway validation.

    Args:
        warehouse: The warehouse user object
        data_list: List of putaway data from the uploaded file

    Returns:
        Dict containing validation data
    """
    log.info(f"Fetching putaway data for warehouse {warehouse.username}")

    # Extract unique values
    values = _extract_unique_values(data_list)

    # Get SKU details
    sku_details_dict, _ = get_sku_master_dict(warehouse, values['sku_codes'])

    # Get location details
    location_data = dict(LocationMaster.objects.filter(
        location__in=values['locations'],
        status=1,
        zone__user=warehouse.id
    ).values_list('location', 'id'))

    # Get GRN details
    grn_filters = {
        'seller_po_summary__grn_reference__in': values['grn_numbers'],
        'purchase_order__id__isnull': False,
        'sku__sku_code__in': values['sku_codes'],
        'status': 1,
        'sku__user': warehouse.id, 
    }

    po_location_objs = POLocation.objects.filter(**grn_filters).values(*_get_po_location_values())

    # Process PO location objects
    grn_data, batch_data, sps_ids, po_loc_ids_by_key = _process_po_location_data(po_location_objs)

    # Get serial numbers if serialization is enabled
    serial_df = None
    sku_serialisation = get_misc_value('sku_serialisation', warehouse.id, 'false')
    if sku_serialisation == 'true' and sps_ids:
        serial_df = get_existing_serials(warehouse, 'grn', sps_ids)

    return {
        'sku_details_dict': sku_details_dict,
        'location_data': location_data,
        'grn_data': grn_data,
        'batch_data': batch_data,
        'serial_df': serial_df,
        'sku_serialisation': sku_serialisation,
        'po_loc_ids_by_key': po_loc_ids_by_key  # Add the new dictionary to the returned data
    }

def _validate_basic_fields(item: Dict, validation_data: Dict = None, batch_key: Tuple = None, sku_info: Dict = None) -> List[str]:
    """Validate the basic required fields including quantity validation."""
    errors = []

    # Extract data from validation_data
    sku_details_dict = validation_data.get('sku_details_dict', {})
    location_data = validation_data.get('location_data', {})
    grn_data = validation_data.get('grn_data', {})
    batch_data = validation_data.get('batch_data', {})

    # Extract common values from item
    grn_number = item.get('grn_number')
    sku_code = item.get('sku_code')
    location = item.get('location')

    # Validate GRN Number
    if not grn_number:
        errors.append('GRN Number is required')
    elif grn_number not in grn_data:
        errors.append(f"Invalid GRN Number: {grn_number}")

    # Validate SKU Code
    if not sku_code:
        errors.append('SKU Code is required')
    elif sku_code not in sku_details_dict:
        errors.append(f"Invalid SKU Code: {sku_code}")
    elif grn_number in grn_data and sku_code not in grn_data.get(grn_number, {}):
        errors.append(f"SKU {sku_code} not found in GRN {grn_number}")

    # Validate Location
    if not location:
        errors.append('Location is required')
    elif location not in location_data:
        errors.append(f"Invalid Location: {location}")
    else:
        # Add location_id to the item if location is valid
        item['location_id'] = location_data[location]

    # Validate Quantity
    try:
        putaway_quantity = float(item.get('putaway_quantity', 0))
    except (ValueError, TypeError):
        putaway_quantity = 0

    if putaway_quantity <= 0:
        errors.append('Putaway Quantity must be greater than 0')
    elif batch_data and batch_key and sku_info:
        available_quantity = batch_data.get(batch_key, {}).get('total_quantity', sku_info.get('total_quantity', 0))
        if putaway_quantity > available_quantity:
            errors.append(f"Putaway Quantity ({putaway_quantity}) exceeds available quantity ({available_quantity})")

    # Validate serial-based SKU quantity
    if sku_info and sku_info.get('is_serial_based') and item.get('serial_number') and putaway_quantity != 1:
        errors.append('Putaway Quantity must be 1 for serial-based SKUs')

    return errors

def _check_lpn_split_locations(data_list: List[Dict], current_item: Dict) -> bool:
    """Check if an LPN is being split across multiple locations."""
    lpn_number = current_item.get('lpn_number')
    if not lpn_number:
        return False

    # Check if this LPN is used in other items with different locations
    grn_number = current_item.get('grn_number')
    for item in data_list:
        if (item.get('lpn_number') == lpn_number and
            item.get('grn_number') == grn_number and
            item.get('location') != current_item.get('location')):
            return True
    return False

def _validate_batch(item: Dict, sku_info: Dict, batch_data: Dict = None, batch_key: Tuple = None) -> List[str]:
    """Validate batch details for the item."""
    errors = []
    batch_number = item.get('batch_number', '')
    lpn_number = item.get('lpn_number', '')
    is_batch_based = sku_info.get('is_batch_based', False)

    if not is_batch_based:
        if batch_number:
            errors.append('Batch details should not be provided for non-batch-based SKUs')
    else:
        if not batch_number:
            errors.append('Batch Number is required for batch-based SKUs')
        elif batch_number not in sku_info['batch_nos']:
            errors.append(f"Invalid Batch Number: {batch_number}")
        elif batch_data:
            # Check batch existence based on LPN availability
            if lpn_number and batch_key not in batch_data:
                errors.append(f"Batch {batch_number} not found for LPN {lpn_number}")
            elif not lpn_number and not any(k[:3] == batch_key[:3] for k in batch_data):
                errors.append(f"Batch {batch_number} not found")

    return errors


def _validate_serial_numbers(item: Dict, sku_info: Dict, sku_serialisation: str, serial_df) -> List[str]:
    """Validate serial numbers for the item."""
    errors = []
    serial_number = item.get('serial_number', '')
    lpn_number = item.get('lpn_number', '')
    is_serial_based = sku_info.get('is_serial_based', False)

    if not is_serial_based:
        if serial_number:
            errors.append('Serial Number should not be provided for non-serial-based SKUs')
    else:
        if sku_serialisation != 'true':
            errors.append('Inventory Serialization is not enabled')
        elif not serial_number:
            errors.append('Serial Number is required for serial-based SKUs')
        elif serial_df is not None and sku_info['sps_ids']:
            # Convert sps_ids to string for fetch_serial_numbers_for_sps_id
            sps_id_str = ','.join(str(sps_id) for sps_id in sku_info['sps_ids'])
            valid_serials = fetch_serial_numbers_for_sps_id(sps_id_str, serial_df, lpn_number, '')
            if serial_number not in valid_serials:
                errors.append(f"Invalid Serial Number: {serial_number}")
    return errors


def _validate_lpn(item: Dict, sku_info: Dict, data_list: List[Dict] = None) -> List[str]:
    """Validate LPN details for the item."""
    errors = []
    lpn_number = item.get('lpn_number', '')
    batch_number = item.get('batch_number', '')
    serial_number = item.get('serial_number', '')

    lpn_numbers = sku_info.get('lpn_numbers', set())
    is_lpn_based = bool(lpn_numbers)
    has_lpn = bool(lpn_number)
    has_batch = bool(batch_number)
    has_serial = bool(serial_number)

    if is_lpn_based and not has_lpn:
        errors.append('LPN Number is required for this SKU')
    elif has_lpn and lpn_numbers and lpn_number not in lpn_numbers:
        errors.append(f"Invalid LPN Number: {lpn_number}. Valid LPNs: {', '.join(lpn_numbers)}")

    # LPN split location validation
    if data_list and has_lpn and (has_batch or has_serial):
        if _check_lpn_split_locations(data_list, item):
            errors.append("LPN can't be split across multiple locations")

    return errors

def _validate_group_quantity(item: Dict, grouped_data: Dict = None, remaining_quantities: Dict = None) -> List[str]:
    """Validate group quantity for the item."""
    errors = []

    if not (grouped_data and remaining_quantities):
        return errors

    # Extract components for the key
    grn_number = item.get('grn_number')
    sku_code = item.get('sku_code')
    batch_number = item.get('batch_number', '')
    lpn_number = item.get('lpn_number', '')

    # Create the key based on LPN availability
    grn_qty_key = (grn_number, sku_code, batch_number, lpn_number)

    if grn_qty_key in remaining_quantities:
        total_available = sum(qty for _, qty in remaining_quantities[grn_qty_key].values())
        group_quantity = grouped_data.get(grn_qty_key, 0)

        if group_quantity > total_available:
            errors.append(f"Total putaway quantity ({group_quantity}) exceeds available quantity ({total_available})")

    return errors


def _validate_item(item: Dict, sku_info: Dict, batch_data: Dict = None, batch_key: Tuple = None,
                  sku_serialisation: str = None, serial_df = None, data_list: List[Dict] = None,
                  grouped_data: Dict = None, remaining_quantities: Dict = None) -> List[str]:
    """
    Unified validation function for batch, serial, LPN and quantity.

    Args:
        item: The item being validated
        sku_info: SKU information
        batch_data: Batch data dictionary (optional)
        batch_key: Tuple of (grn_number, sku_code, batch_number, lpn_number) (optional)
        sku_serialisation: SKU serialization setting (optional)
        serial_df: DataFrame containing serial numbers (optional)
        data_list: List of all putaway data items (for LPN split validation)
        grouped_data: Dictionary of grouped data for quantity tracking (optional)
        remaining_quantities: Dictionary to track remaining quantities (optional)

    Returns:
        List of error messages
    """
    errors = []

    # Call individual validation functions and collect errors
    errors.extend(_validate_batch(item, sku_info, batch_data, batch_key))
    errors.extend(_validate_serial_numbers(item, sku_info, sku_serialisation, serial_df))
    errors.extend(_validate_lpn(item, sku_info, data_list))
    errors.extend(_validate_group_quantity(item, grouped_data, remaining_quantities))

    return errors



def check_and_update_unique_sku_serials(sku_code, serial_number, unique_sku_serials):
    """
    Checks if (sku_code, serial_number) is a duplicate.
    Returns True if duplicate, False otherwise.
    """
    if serial_number:
        sku_serial_pair = (sku_code, serial_number)
        if sku_serial_pair in unique_sku_serials:
            return True
        unique_sku_serials.add(sku_serial_pair)
    return False


def validate_putaway_form(warehouse, data_list: List[Dict], grouped_data: Dict = None) -> Tuple[List[Dict], List[Dict], List[Dict], Dict]:
    """
    Validates the putaway upload form data.

    Args:
        warehouse: The warehouse user object
        data_list: List of putaway data from the uploaded file
        grouped_data: Dictionary of grouped data for quantity tracking (optional)

    Returns:
        Tuple containing:
        - List of validated putaway data with auto_putaway=Yes
        - List of validated putaway data with auto_putaway=No
        - List of error data
    """
    log.info(f"Validating putaway form data for warehouse {warehouse.username}")
    # Initialize result containers
    auto_putaway_yes_data, auto_putaway_no_data, error_list = [], [], []

    # Fetch all required data in a single call
    validation_data = fetch_putaway_data(warehouse, data_list)

    # Create a copy of po_loc_ids_by_key to track remaining quantities
    remaining_quantities = {}
    for grn_qty_key, locations in validation_data['po_loc_ids_by_key'].items():
        remaining_quantities[grn_qty_key] = {}
        for loc, (pol_id, qty) in locations.items():
            remaining_quantities[grn_qty_key][loc] = (pol_id, qty)

    # Track unique (sku_code, serial_number) pairs during processing
    unique_sku_serials = set()

    # Process each item
    for index, item in enumerate(data_list):
        # Setup error tracking
        error_item = {'row': index + 2, 'errors': []}  # +2 for header row and 1-based indexing
        error_item.update(item)  # Add item data to error item

        # Extract common values once
        grn_number = item.get('grn_number')
        sku_code = item.get('sku_code')
        batch_number = item.get('batch_number', '')
        lpn_number = item.get('lpn_number', '')
        serial_number = item.get('serial_number')

        # Unique (sku_code, serial_number) validation
        if check_and_update_unique_sku_serials(sku_code, serial_number, unique_sku_serials):
            error_item['errors'].append(
                f"Duplicate serial number '{serial_number}' found for SKU '{sku_code}' in multiple rows."
            )
            error_list.append(error_item)
            continue

        # Get SKU details
        sku_info = validation_data.get('grn_data', {}).get(grn_number, {}).get(sku_code)

        # Create keys for validation
        batch_key = (grn_number, sku_code, batch_number, lpn_number)

        # Perform basic validations
        basic_errors = _validate_basic_fields(item, validation_data, batch_key, sku_info)

        if basic_errors:
            error_item['errors'].extend(basic_errors)
            error_list.append(error_item)
            continue

        # Perform all validations using the unified validation function
        all_errors = _validate_item(
            item, sku_info, validation_data['batch_data'], batch_key, validation_data['sku_serialisation'],
            validation_data['serial_df'], data_list, grouped_data, remaining_quantities
        )
        if all_errors:
            error_item['errors'].extend(all_errors)
            error_list.append(error_item)
            continue

        # Map the item to one or more POLocation objects
        mapped_items = map_item_to_polocation(item, validation_data)

        # Process each mapped item
        for mapped_item in mapped_items:
            # Sort by auto_putaway value
            auto_putaway_value = mapped_item.get('auto_putaway', '').strip().lower()
            if auto_putaway_value == 'yes':
                auto_putaway_yes_data.append(mapped_item)
            else:
                auto_putaway_no_data.append(mapped_item)

    log.info(f"Validated {len(auto_putaway_yes_data) + len(auto_putaway_no_data)} items: {len(auto_putaway_yes_data)} with auto_putaway=Yes, {len(auto_putaway_no_data)} with auto_putaway=No")

    return auto_putaway_yes_data, auto_putaway_no_data, error_list, validation_data
