import datetime
from dateutil import parser
import json
import traceback
import os
import math
import pytz
import base64
import io
from django.conf import settings

from django.http import JsonResponse
from django.template import loader
from django.db import transaction
from django.core.cache import cache
from django.db.models import F
from django.core.files.uploadedfile import InMemoryUploadedFile

#Model Imports
from wms_base.models import User
from core.models import (
    MasterDocs, LabelConfig, MiscDetailOptions,
    TempJson
)
from inventory.models import (
    SerialNumberMapping, SerialNumberTransactionMapping,
    SKUPackMaster, BatchDetail, StockDetail
)
from inbound.models import SellerPOSummary

#production imports
from production.views.jo_grn.jo_grn import (
    update_stock_for_picked_quantity, create_jo_grn, save_rm_picking_details_for_jo,
    update_consumable_stock_for_picked_quantity, update_open_picklist_for_shortclosed_job_orders,
    auto_trigger_of_putaway_incase_of_extra_rms
)

from .grn_reversal import grn_reversal

#Method Imports
from core_operations.views.common.main import (
    get_warehouse,
    get_multiple_misc_values,
    get_misc_value, get_custom_html_format,
    get_user_prefix_incremental,update_incremental_value, scroll_data, 
    get_user_ip, get_uploaded_files_data,
    bulk_upload_master_file, WMSListView, get_decimal_value,
    generate_log_message, get_user_time_zone, create_default_zones
    )
from core_operations.views.integration.integration import webhook_integration_3p
from core_operations.serializers.main import get_serializer_error_message
from inbound.views.grn.common import get_grn_numbers_list
from inbound.views.putaway.suggestions import putaway_suggestions_for_grn
from inbound.views.putaway.putaway import async_auto_putaway_for_grn, async_order_fulfil

from inbound.views.common.constants import (
    DEFAULT_DATE_FORMAT, INVALID_PAYLOAD_CONST,
    TRUE_VALUES
    )
from inbound.views.purchase_order.common import (
    get_master_doc_details
)
from inbound.serializers.grn import GRNSerializer
from inbound.views.grn.integrations import grn_call_back_3p_integration, send_asn_call_back
from inbound.views.purchase_order.common import update_po_header

from .validation import SalesReturnGRNValidation, POGRNValidation, validate_jo_grn
from .common import (
    get_grn_sku_wise_jo_data, create_file_po_grn_mapping,
    SKUWiseGRN, get_grn_sku_wise_sr_data
    )
from inventory.views.move.move_inventory import create_destination_stocks

from .create_grn import CreateGRN

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/grn' + today + '.log')
log_err = init_logger('logs/grn.log')


def grn_extra_fields(warehouse):
    extra_grn_fields = []
    grn_field_obj = get_misc_value('grn_fields', warehouse.id)
    if grn_field_obj != 'false':
        extra_grn_fields = grn_field_obj.split(',')
    return extra_grn_fields

def serial_number_mapping(warehouse, serial_mapping_data):
    log.info("Request Create SerialNumberMapping username %s and params are %s"% (
        str(warehouse.username),str(serial_mapping_data)))
    serial_num_prefix= 'serial_number_prefix'
    sku_code = serial_mapping_data.get('sku_code', '')
    reference_number = serial_mapping_data.get('reference_number', '')
    reference_type = serial_mapping_data.get('reference_type', '')
    sku_details = serial_mapping_data.get('sku_details', [])
    user_prefix = serial_mapping_data.get('serial_num_prefix','')
    message = ''
    if not sku_details:
        return
    try:
        all_serial_nums = []
        serial_count = 0
        for sku_data in sku_details:
            if not sku_data.get('serial_numbers', []):
                serial_count += int(sku_data['quantity'])
            else:
                all_serial_nums.extend(sku_data.get('serial_numbers', []))
        
        if serial_count:
            with transaction.atomic('default'):
                #get serial number with user prefix
                serial_num_val, serial_num_prefix, generated_serial_number, check_serial_num_prefix, inc_status = \
                    get_user_prefix_incremental(warehouse, serial_num_prefix, sku_code, full_user_prefix=user_prefix)
                if not generated_serial_number:
                    message = "Serial Number Prefix Not Available !"
                    return message
                serial_count += serial_num_val
                # update incremental value
                update_incremental_value(int(serial_count)-1, warehouse, check_serial_num_prefix)
        
        for sku_data in sku_details:
            if serial_count and not sku_data.get('serial_numbers', []):
                for _ in range(int(sku_data['quantity'])):
                    serial_num_data = {'warehouse_id': warehouse.id, 'sku_id' : sku_data['sku_id'], 'serial_number' : generated_serial_number, 'batch_detail_id': sku_data['batch_detail_id']}
                    # create serial number
                    serial_num_obj = SerialNumberMapping.objects.create(**serial_num_data)

                    serial_num_val = serial_num_val+1
                    generated_serial_number = '%s%s' % (serial_num_prefix, str(serial_num_val).zfill(5))
                    # create serial number transaction mapping
                    SerialNumberTransactionMapping.objects.create(warehouse_id=warehouse.id,serial_id=serial_num_obj.id, reference_number=reference_number, reference_type=reference_type, status=0)
            else:
                for each_serial_number in sku_data.get('serial_numbers'):
                    serial_num_data = {
                        'warehouse_id':warehouse.id,
                        'sku_id' : sku_data['sku_id'],
                        'serial_number' : each_serial_number,
                        'batch_detail_id': sku_data['batch_detail_id'],
                        'carton_id': sku_data.get('carton_id', None)
                        }
                    # create serial number
                    serial_num_obj = SerialNumberMapping.objects.create(**serial_num_data)
                    serial_obj_id = serial_num_obj.id
                    # create serial number transaction mapping
                    SerialNumberTransactionMapping.objects.create(warehouse_id=warehouse.id,serial_id=serial_obj_id, reference_number=reference_number, reference_type=reference_type, status=0)

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Create Serial Numbers failed! Error message  %s' % (str(e)))

    return message

class GRNSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None

    def frame_date_filters(self, request_data):         
        date_filters = {}
        try:
            filter_date = {}
            date_keys = ['from_date', 'to_date', 'updated_at_gte', 'updated_at_lte']
            for key in date_keys:
                if request_data.get(key):
                    parsed_date = parser.parse(request_data[key])
                    localized_date = pytz.timezone(self.timezone).localize(parsed_date)
                    filter_date[key] = localized_date.astimezone(pytz.UTC)

        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('GET GRN API Date Filters Failed for %s and params are %s and error statement is %s' % (
                str(self.request.user.username), str(request_data), str(e)))

        if 'from_date' in filter_date:
            date_filters['creation_date__gt'] = filter_date['from_date']
            del request_data['from_date']

        if 'to_date' in filter_date:
            date_filters['creation_date__lt'] = filter_date['to_date']
            del request_data['to_date']
        
        if filter_date.get('updated_at_gte'):
            date_filters['updation_date__gte'] = filter_date['updated_at_gte']
            del request_data['updated_at_gte']

        if filter_date.get('updated_at_lte'):
            date_filters['updation_date__lte'] = filter_date['updated_at_lte']
            del request_data['updated_at_lte']

        return date_filters

    def get(self, *args, **kwargs):
        error_status = []
        limit = int(self.request.GET.get('limit', 10))
        offset = int(self.request.GET.get('offset', 0))

        request = self.request  
        request_data = self.request.GET.copy()  
        self.set_user_credientials()
        self.timezone = get_user_time_zone(self.warehouse)
        
        #Framing Date Filters
        date_filters = self.frame_date_filters(request_data)
        if date_filters:
            request_data.update(date_filters)
        label_id = ''
        label_list = []
        if 'send_label_id' in request_data:
            request_data.pop('send_label_id', None)
            labels = list(LabelConfig.objects.filter(
                warehouse=self.warehouse, status=1).values_list('id', 'used_at', 'reference', 'json_data'))
            for id, used_at, label_reference, json_data in labels:
                if 'grn' in used_at.split(','):
                    label_list.append({'label_id': id, 'label_name': label_reference, 'configs': json_data.get('configs', {})})
                    label_id = id
        true_values = [True, 'true', 'True']
        send_check_list = True if request_data.get('send_checklist_data', '') in true_values else False
        send_pack_wise = True if request_data.get('send_pack_wise', '') in true_values else False
        extra_data = {
            'send_check_list' : send_check_list,
            'send_pack_wise' : send_pack_wise
            }
        request_data.pop('send_checklist_data', '')
        request_data.pop('send_pack_wise', '')
        message, grn_data = get_grn_numbers_list(self.warehouse, request_data)
        if message:
            return JsonResponse({
                'data' : [],
                'paging' : {},
                'errors' : [message]
                }, status=400)

        grn_numbers_list = list(grn_data)
        asn_numbers = set(each_data.get('asn_number','') for each_data in grn_data.values())

        limit = limit if limit else len(grn_numbers_list)
        grn_numbers = list(grn_numbers_list)[offset:limit+offset]

        #Function Calling to Get GRN Data
        add_data = {}
        if label_id:
            add_data['label_id'] = label_id

        add_data['labels'] = label_list
        
        data_list = []
        if 'grn_type' in request_data and request_data.get('grn_type') != 'PO':
            if request_data.get('grn_type') == "job_order":
                #JO GRNs
                data_list = get_grn_sku_wise_jo_data(self.warehouse, request_data, add_data)
            elif request_data.get('grn_type') == 'SR':
                #Sales Return GRN
                data_list = get_grn_sku_wise_sr_data(self.warehouse, grn_numbers, add_data)
        else:
            if grn_numbers:
                extra_data['uploaded_files'] = get_master_doc_details(
                    request, [self.warehouse.id], grn_numbers, 'GRN_PO_NUMBER')
                extra_data['decimal_limit'] = get_decimal_value(self.warehouse.id)
                extra_data['asn_files'] = get_master_doc_details(
                    request, [self.warehouse.id], asn_numbers, 'ASN_PO_NUMBER')
                extra_data['decimal_limit'] = get_decimal_value(self.warehouse.id)
                sps_ids = list(SellerPOSummary.objects.filter(grn_number__in=grn_numbers).values_list('id', flat=True))
                extra_data['line_wise_uploaded_files'] = get_master_doc_details(
                        request, [self.warehouse.id], sps_ids ,'grn_rejected_docs')
                if request_data.pop('label_print', None):
                    extra_data['excludes'] = {'remarks': 'Shortage'}
                
            data_list = SKUWiseGRN().get_grn_sku_wise_sps_data(self.warehouse, grn_numbers, add_data, extra_data)

        page_info = scroll_data(request, data_list, limit=limit, request_type='GET')
        page_info['data'] = data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = len(data_list)
        page_info['error'] = [{'message': error_status}]
        return JsonResponse(page_info, safe=False)

    def check_asn_request(self):
        self.is_asn = True  if 'asn' in str(self.request) else False
    
    def process_request(self):
        try:
            self.check_asn_request()
            self.request_details = {
                'user' : self.request.user
                }
            self.request_data = json.loads(self.request.body)
        except Exception:
            try:
                self.request_data = json.loads(self.request.POST.get("data"))
            except Exception:
                self.request_data = ""

    def update_request_data(self):
        if self.grn_type in ('PO', 'SR'):
            self.request_details.update({
                'request_headers': self.request.headers,
                'request_meta': self.request.META,
            })

    def process_request_dates(self):
        for e_row in self.request_data.get("items"):
            if len(e_row.get("retest_date", ""))==10:
                e_row["retest_date"] = e_row["retest_date"] + " 23:59"
            if len(e_row.get("expiry_date", ""))==10:
                e_row["expiry_date"] = e_row["expiry_date"] + " 23:59"
            if len(e_row.get("manufactured_date", ""))==10:
                e_row["manufactured_date"] =  e_row["manufactured_date"] + " 00:00"
    
    def clear_cache(self, keys):
        for key in keys:
            cache_key = f'{self.warehouse.id}##{key}'
            cache.delete(str(cache_key))
    
    def frame_jo_grn_label_data(self, po_dict_data):
        sku_label_data = []
        for sku_data in po_dict_data:
            batch_detail_dict = sku_data.get('batch_details', {})
            sku_details_dict = sku_data.get('extra_details', {})
            sku_dict = {key : sku_details_dict.get(get_key, default) for key, get_key, default in self.label_keys}
            batch_dict = {key : batch_detail_dict.get(get_key, default) for key, get_key, default in self.batch_keys}
            sku_dict.update(batch_dict)
            for format_key in self.format_change_keys:
                sku_dict[format_key] = sku_dict[format_key].strftime(DEFAULT_DATE_FORMAT) if sku_dict.get(format_key) else ''
            sku_label_data.append(sku_dict)

        return sku_label_data
 
    def frame_po_grn_label_data(self, po_dict_data):
        sku_label_data = []
        for sku_data in po_dict_data: #grn_data_list
            batch_detail_dict = sku_data.get('batch_detail', {})
            sku_dict = {key : sku_data.get(get_key, default) for key, get_key, default in self.label_keys}
            batch_dict = {key : batch_detail_dict.get(get_key, default) for key, get_key, default in self.batch_keys}
            sku_dict.update(batch_dict)
            for format_key in self.format_change_keys:
                sku_dict[format_key] = sku_dict[format_key].strftime(DEFAULT_DATE_FORMAT) if sku_dict.get(format_key) else ''
            sku_label_data.append(sku_dict)
        if not self.is_asn:
            return sku_label_data

    def frame_label_data(self, po_dict_data):
        misc_name = {'JO' : 'JO GRN SKU LABEL', 'PO' : 'SKU BARCODE', 'SR': 'SKU BARCODE'}
        misc_dict = get_multiple_misc_values([misc_name[self.grn_type]], self.warehouse.id)
        invoice_format = misc_dict.get(misc_name.get(self.grn_type, ''), 'false')
        filter_dict = {'document_type': misc_name[self.grn_type], 'invoice_format': invoice_format}
        sku_label_html_format = get_custom_html_format(filter_dict, {}, self.warehouse.id)
        if not sku_label_html_format:
            sku_label_html_format = loader.get_template('templates/print/sku_barcode_form.html').render()

        self.label_keys = [
            ('sku_code', 'sku_code', ''), ('sku_desc', 'sku_desc', ''), ('quantity', 'quantity', 0),
            ('supplier_id', 'supplier_id', ''), ('supplier_name', 'supplier_name', '')
            ]
        self.batch_keys = [
            ('batch_no', 'batch_no',''), ('price', 'buy_price', 0), ('mrp', 'mrp', 0),
            ('manufactured_date', 'manufactured_date', ''), ('expiry_date', 'expiry_date', '')
            ]
        self.format_change_keys = ['manufactured_date', 'expiry_date']
        if self.grn_type == 'JO':
            sku_label_data = self.frame_jo_grn_label_data(po_dict_data)  
        else:
            sku_label_data = self.frame_po_grn_label_data(po_dict_data)
        
        label_response = {
            'sku_label_data' : sku_label_data,
            'sku_label_format' : sku_label_html_format
            }
        return label_response

    def deplete_stock(self, po_dict_data):
        update_open_picklist_for_shortclosed_job_orders.apply_async(
            args = [self.warehouse.id, po_dict_data]
        )
        stock_picking_details, bom_wastage_details = update_stock_for_picked_quantity(
                                self.warehouse, po_dict_data, is_deplete_stock = True
                            )
        rm_picklist_objs = save_rm_picking_details_for_jo(
            self.warehouse, self.grn_number, stock_picking_details,
            bom_wastage_details, is_deplete_stock = True
        )
        rm_picking_details = [rm.__dict__ for rm in rm_picklist_objs]
        log.info(("RM Picklist Details (Deplete Stock) for Username %s, GRN Number %s, RM Picklist objs %s") % (
            str(self.warehouse.username), self.grn_number, rm_picking_details)
        )

    def create_and_update_jo_grn(self, po_dict_data, sku_codes_list, grn_extra_dict, is_deplete_stock):
        consumable_data = grn_extra_dict.get('consumable_list')
        if not consumable_data:
            stock_picking_details, bom_wastage_details = update_stock_for_picked_quantity(
                self.warehouse, po_dict_data
            )
        else:
            stock_picking_details, bom_wastage_details = update_consumable_stock_for_picked_quantity(
                self.warehouse, consumable_data, po_dict_data
            )
        self.grn_number, status, message, rendered, serialized, serial_num_prefix = create_jo_grn(self.request,
            self.warehouse, po_dict_data, sku_codes_list, grn_extra_dict)

        if self.grn_number:
            rm_picklist_objs = save_rm_picking_details_for_jo(
                self.warehouse, self.grn_number, stock_picking_details, bom_wastage_details
            )
            rm_picking_details = [rm.__dict__ for rm in rm_picklist_objs]
            log.info(("RM Picklist Details for Username %s, GRN Number %s, RM Picklist objs %s") % (
                str(self.warehouse.username), self.grn_number, rm_picking_details)
            )
            if not consumable_data:
                #IN CASE OF SHORT CLOSED
                if is_deplete_stock:
                    self.deplete_stock(po_dict_data)
            else:
                #IN CASE OF CONSUMABLES
                auto_trigger_of_putaway_incase_of_extra_rms.apply_async(
                    args = [self.warehouse.id, po_dict_data]
                )
            self.response_dict['grn_number'] = self.grn_number

        return status, message, rendered, serialized, serial_num_prefix

    def send_grn_for_inspection(self, grn_inspection_data, asn_number):
        """Save User enterd data in tempjson for GRN isnpection"""
        temp_json_objs = []
        temp_dict = {
            'model_name': 'grn_inspection',
            'model_reference': asn_number,
            'warehouse_id': self.warehouse.id,
            'account_id': self.warehouse.userprofile.id
        }
        for sku_id, each_data in grn_inspection_data.items():
            temp_dict['model_id'] = sku_id
            temp_dict['model_json'] = json.dumps({'data': each_data})
            temp_json_objs.append(TempJson(**temp_dict))
        try:
            TempJson.objects.bulk_create_with_rounding(temp_json_objs)
        except Exception as e:
            log.info(f"Failed to save GRN inspection data: {e}")
            return False
        return True

    def grn_creation(self, data):
        #Validations of GRN/ASN
        status, grn_inspection, message, asn_cache_key = False, False, '', ''
        po_numbers_list, po_ref_list = [], []
        if self.grn_type == 'SR':
            self.return_id = data.get('return_id', '')
            validated_error_dict, po_dict_data, grn_data_list, po_numbers_list, grn_extra_dict = SalesReturnGRNValidation().validate(data, self.warehouse)
            log.info(generate_log_message("SRGRNValidationProcessedPayload",request_payload=data, grn_data_list=grn_data_list))    
        elif self.grn_type == 'JO':
            validated_error_dict, po_dict_data, sku_codes_list, grn_extra_dict, is_deplete_stock, po_numbers_list, shortage_data = validate_jo_grn(data, self.warehouse)
        else:
            validation_type = "po_grn_creation"
            if data.get('asn_number'):
                validation_type = "asn_grn_creation"

            validated_error_dict, po_dict_data, grn_data_list, po_numbers_list, grn_extra_dict, asn_cache_key, po_ref_list \
                = POGRNValidation().validate(data, self.warehouse, is_asn=self.is_asn, validation_type=validation_type, extra_params=self.request_details)
            log.info(generate_log_message("POGRNValidationProcessedPayload",request_payload=data, grn_data_list=grn_data_list))    

        rendered, _, _ = '', '', ''
        error_message_dict = {}
        if validated_error_dict:
            status= False
            error_message_dict = validated_error_dict
        elif po_dict_data:
            if self.grn_type == 'JO':
                status, message, rendered, serialized, serial_num_prefix = (
                    self.create_and_update_jo_grn(po_dict_data, sku_codes_list, grn_extra_dict, is_deplete_stock)
                )
            if not self.is_asn and self.grn_type in ['PO', 'SR'] and grn_data_list:
                self.grn_number, status, message, rendered, discrepency_rendered = CreateGRN().create_grn(
                        self.warehouse, data, grn_data_list, grn_extra_dict, self.request_details)
                                
                if discrepency_rendered:
                    self.response_dict['discrepency_rendered'] = discrepency_rendered
            
                self.response_dict['grn_number'] = self.grn_number
                self.response_dict['grn_display_key'] = self.grn_number
                try:
                    return_id = ''
                    if self.grn_type == 'SR':
                        return_id = self.return_id
                    callback_action = grn_call_back_3p_integration(self.warehouse, self.grn_number, return_id, self.grn_type)
                    if data.get('asn_number', '') and callback_action == 'asn_callback':
                        send_asn_call_back(self.warehouse, data.get('asn_number', ''), 'asn_creation')
                except Exception as e:
                    log.info('Exception Raised on GRN CallBack :  ' + self.warehouse.username + " grn_number "+ self.grn_number +" error"+str(e))

                #Auto Putaway based on request param  or Configured PO types in Auto Putaway
                auto_putaway = self.request_data.get('auto_putaway', '')
                auto_putaway_from_config = grn_extra_dict.get('auto_putaway', '')
                po_type =grn_data_list[0].get('po_type', '') if grn_data_list else ''
                if (auto_putaway in TRUE_VALUES) or (auto_putaway_from_config and po_type and po_type.lower() in auto_putaway_from_config.lower().split(',')):
                    putaway_type = 'po_putaway' if self.grn_type == 'PO' else 'sr_putaway'
                    async_auto_putaway_for_grn.apply_async(
                        args = [self.grn_number, putaway_type, self.request.META, self.request.user.id, self.warehouse.id]
                    )
                    if validation_type == 'asn_grn_creation' and self.grn_type == 'PO':
                        async_order_fulfil.apply_async(args=[self.request.user.id, self.warehouse.id, self.grn_number, data.get('asn_number')])
            grn_inspection_data = grn_extra_dict.get('grn_inspection_data', {})
            if grn_inspection_data and data.get('asn_number'):
                grn_inspection = True
                status = self.send_grn_for_inspection(grn_inspection_data, data.get('asn_number', ''))
            if not status:
                if not message:
                    message = "Something went Wrong"
                error_message_dict= {"header" : [message]}
        
        extra_data = {
            'rendered' : rendered,
            'grn_inspection': grn_inspection
        }
        if self.grn_type == 'JO' and shortage_data:
            extra_data.update({'shortage_data': shortage_data})

        return status, po_numbers_list, error_message_dict, po_dict_data, extra_data, asn_cache_key, po_ref_list

    def post(self, *args, **kwargs):
        try:
            self.process_request()
            if not self.request_data:
                return JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)
            log.info('Request params for GRN ' + self.request.user.username + 
                    ' is ' + str(self.request_data) + 'ip_address:' + str(get_user_ip(self.request)))
            self.process_request_dates()
            self.set_user_credientials()
            valid_ser = GRNSerializer(data=self.request_data)
            po_numbers_list, po_ref_list = [], []
            self.response_dict, data, extra_data = {}, {}, {}
            self.grn_type, asn_cache_key = '', ''
            if valid_ser.is_valid():
                data = valid_ser.validated_data

                self.grn_type = data.get('grn_type', '')
                self.update_request_data()
                status, po_numbers_list, error_message_dict,\
                     po_dict_data, extra_data, asn_cache_key, po_ref_list  = self.grn_creation(data)
                
            else:
                error_dict = valid_ser.errors
                error_message_dict = get_serializer_error_message(self.request_data, error_dict)
                status= False

            self.clear_cache(po_numbers_list + po_ref_list)
            if asn_cache_key:
                cache.delete(asn_cache_key)
            source_from = self.request_data.get('source', '')
            send_label_data = False #Keep it configurable

            label_id = []
            if self.grn_type == 'PO':
                update_po_header(po_numbers_list, self.warehouse.id)
            if self.grn_type in ('PO', 'SR'):
                label_name = 'grn' if self.grn_type == 'PO' else 'sr_grn_label'
                labels = list(LabelConfig.objects.filter(
                warehouse=self.warehouse, status=1).values_list('id', 'used_at', 'reference', 'json_data'))
                for id, used_at, label_reference, json_data in labels:
                    if label_name in used_at.split(','):
                        label_id.append({'label_id': id, 'label_name': label_reference, 'configs': json_data.get('configs', {})})
                self.response_dict['labels'] = label_id
                self.response_dict['grn_inspection'] = extra_data.get('grn_inspection', '')
                
            if status:
                if extra_data.get('rendered') and source_from == 'WEB':
                    if send_label_data:
                        label_response = self.frame_label_data(po_dict_data)
                        self.response_dict.update(label_response)
                    self.response_dict.update({
                        'serialized' : extra_data.get('serialized', ''),
                        'serial_num_prefix' : extra_data.get('serial_num_prefix', ''),
                        'rendered' : extra_data.get('rendered', ''), 
                        })
                    return JsonResponse(self.response_dict, status=200)                
                else:
                    return JsonResponse(self.response_dict, status=200)
            else:
                error_dict = {'error': [{'message': error_message_dict }]}
                if extra_data.get('shortage_data'):
                    error_dict.update({'shortage_data': extra_data.get('shortage_data')})
                return JsonResponse(error_dict, status=400)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('POST GRN failed for params '+str(self.request_data) + " error "+str(e))
            return  JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)
        
    def put(self, *args, **kwargs):
        """Update GRN."""
        # Validate the request body
        self.set_user_credientials()
        request_data, error_response = self.validate_request_body()
        if error_response:
            return JsonResponse({'message': error_response.get('message')}, status = error_response.get('status'))

        if not request_data or not all(key in request_data for key in ['grn_number', 'grn_reference']):
            return JsonResponse({'message': 'Invalid Request, GRN Number and GRN Reference are Mandatory'}, status=400)

        self.request_data = request_data
        self.grn_file_url = request_data.get('grn_file_url', '')
        self.encoded_grn_file = request_data.get('encoded_grn_file', '')
        self.grn_number = request_data.get('grn_number', '')
        self.grn_reference = request_data.get('grn_reference', '')
        try:
            # Validate GRN
            validation_response = self.validate_grn_details()
            if validation_response['status'] == 400:
                cache.delete(self.grn_number)
                return JsonResponse({'message': validation_response['message']}, status=400)
            elif validation_response['status'] == 409:
                return JsonResponse({'message': validation_response['message']}, status=409)

            #Validate GRN Line Details
            validation_response = self.validate_grn_line_details()
            if validation_response['status'] == 400:
                cache.delete(self.grn_number)
                return JsonResponse({'message': validation_response['message']}, status=400)

            # Update GRN
            update_response = self.update_grn()

            # Update grn document
            if self.grn_file_url or self.encoded_grn_file:
                self.update_encoded_grn_file()

            #Inventory Callback
            sku_codes = list(self.grn_objs.values_list('sku__sku_code', flat=True))
            filters = {'sku_codes': sku_codes}
            webhook_integration_3p(self.warehouse.id, "grn_creation", filters)

            cache.delete(self.grn_number)

            return JsonResponse({'message': update_response['message']}, status=update_response['status'])

        except Exception as e:
            cache.delete(self.grn_number)
            log.error(f"PUT GRN failed for {self.request_data}: {e}", exc_info=True)
            return JsonResponse({'error': [{'message': 'Invalid Payload'}]}, status=400)

    def validate_request_body(self):
        """validate request body as JSON."""
        try:
            self.request_details = request_details = {
                'user' : self.request.user,
                'request_headers': self.request.headers,
                'request_meta': self.request.META,
            }
            self.extra_params = {
                    "headers": {
                        "Warehouse": request_details.get('request_headers', {}).get("Warehouse"),
                        "Authorization": request_details.get('request_headers', {}).get('Authorization', ''),
                    },
                    "request_meta": request_details.get('request_meta', {}),
                    "request_scheme": request_details.get('request_scheme', '')
                }
            request_data = json.loads(self.request.body)
            misc_dict = get_multiple_misc_values(['enable_inbound_staging_lanes'], self.warehouse.id)
            self.inbound_staging_lanes = True if misc_dict.get('enable_inbound_staging_lanes', 'false') == 'true' else False
            if not isinstance(request_data, dict):
                return None, {'message': 'Request body must be a valid JSON object.', 'status': 400}
            return request_data, None
        except json.JSONDecodeError:
            return None, {'message': 'Invalid Request. Body must be a valid JSON object.', 'status': 400}


    def validate_grn_details(self):
        """Validate GRN details."""
        self.grn_number = self.request_data.get('grn_number')
        grn_reference = self.request_data.get('grn_reference')
        self.update_type = self.request_data.get('update_type', 'grn_reference')

        filter_dict = {}
        if self.update_type in ('grn_status', 'grn_with_qc', 'qc'):
            filter_dict = {'polocation__isnull':True}
        cache_status = cache.add(self.grn_number, "True", timeout=60)
        if not cache_status:
            return {'message': 'GRN Number is already being processed', 'status': 409}
        self.grn_objs = SellerPOSummary.objects.filter(user=self.warehouse.id, grn_number=self.grn_number, **filter_dict)
        if not self.grn_objs.exists():
            return {'message': 'GRN Number Not Found', 'status': 400}

        grn_obj = self.grn_objs[0]
        if grn_reference != grn_obj.grn_reference and grn_obj.grn_reference != grn_obj.grn_number:
            return {'message': 'GRN Reference Mismatch', 'status': 400}

        is_duplicate_reference = SellerPOSummary.objects.exclude(grn_number=self.grn_number).filter(
            user=self.warehouse.id,
            grn_reference=grn_reference
        ).exists()
        if is_duplicate_reference:
            return {'message': 'GRN Reference Already Exists', 'status': 400}

        return {'message': 'Success', 'status': 200}
    
    def validate_item_details(self, item_data):
        sps_data = list(self.grn_objs.values('id', 'quantity', 'status'))
        sps_dict = dict((sps['id'], sps) for sps in sps_data)
        error = False
        check_for_repeat = []
        for item in item_data:
            grn_id = item.get('id')
            if not grn_id:
                item['error'] = 'Id is Mandatory'
                error = True
                continue
            elif grn_id not in sps_dict:
                item['error'] = 'Invalid Id'
                error = True
                continue
            if grn_id in check_for_repeat:
                item['error'] = 'Duplicate Id'
                error = True
                continue
            check_for_repeat.append(grn_id)
            if self.update_type in ('grn_status', 'grn_with_qc'):
                inspection_lot_number = item.get('inspection_lot_number', '')
                self.lot_no_dict[item['id']] = inspection_lot_number
                if inspection_lot_number and self.update_type == 'grn_with_qc':
                    self.ids_for_staging.append(item['id'])
                else:
                    self.ids_for_suggestions.append(item['id'])
            elif self.update_type == 'qc':
                if sps_dict[grn_id]['status'] != 0:
                    item['error'] = 'GRN Status Update is Pending'
                    error = True
                    continue
                quantity = sps_dict.get(grn_id, {}).get('quantity', 0)
                accepted_quantity = item.get('accepted_quantity', 0)
                rejected_quantity = item.get('rejected_quantity', 0)
                if not accepted_quantity and not rejected_quantity:
                    item['error'] = 'Accepted Quantity or Rejected Quantity is Mandatory'
                    error = True
                elif accepted_quantity + rejected_quantity != quantity:
                    item['error'] = 'Accepted Quantity + Rejected Quantity should be equal to Quantity' 
                    error = True
                self.qc_dict[item['id']] = {'accepted_quantity': accepted_quantity, 'rejected_quantity': rejected_quantity}
                self.ids_for_suggestions.append(item['id'])
        if not item_data:
            self.ids_for_suggestions = list(sps_dict.keys())
        if error:
            return {'message': item_data, 'status': 400}
        return {'message': 'Success', 'status': 200}    
    
    def validate_grn_line_details(self):
        self.grn_ids, self.ids_for_suggestions, self.ids_for_staging, self.lot_no_dict, self.qc_dict = [], [], [], {}, {}
        if self.update_type not in ['grn_status', 'grn_reference', 'qc', 'grn_with_qc']:
            return {'message': 'Invalid Update Type', 'status': 400}
        if self.update_type in ['grn_status', 'qc', 'grn_with_qc']:
            item_data = self.request_data.get('items', [])
            if not item_data and self.update_type == 'qc':
                return {'message': 'No Items Found', 'status': 400}
            return self.validate_item_details(item_data)
        return {'message': 'Success', 'status': 200}

    def update_grn(self):
        """Update GRN references."""
        try:
            updated_objs, updated_batches = [], []
            for grn_obj in self.grn_objs:
                grn_obj.grn_reference = self.request_data['grn_reference']
                if self.update_type in ('grn_status', 'grn_with_qc'):
                    if self.ids_for_suggestions or self.ids_for_staging:
                        grn_obj.status = 0 if grn_obj.id in self.ids_for_suggestions or grn_obj.id in self.ids_for_staging else 3
                    else:
                        grn_obj.status = 0
                    batch = grn_obj.batch_detail if grn_obj.batch_detail else None
                    if batch:
                        lot_no = self.lot_no_dict.get(grn_obj.id, '')
                        if lot_no:
                            batch.inspection_lot_number = lot_no
                            updated_batches.append(batch)
                elif self.update_type == 'qc':
                    qc_data = self.qc_dict.get(grn_obj.id, {})
                    grn_obj.accepted_quantity = qc_data.get('accepted_quantity', 0)
                    grn_obj.damaged_quantity = qc_data.get('rejected_quantity', 0)
                updated_objs.append(grn_obj)

            if updated_objs:
                SellerPOSummary.objects.bulk_update_with_rounding(updated_objs, ['grn_reference', 'status', 'accepted_quantity', 'damaged_quantity'])
            if updated_batches:
                BatchDetail.objects.bulk_update_with_rounding(updated_batches, ['inspection_lot_number'])
            
            if self.ids_for_suggestions:
                po_type = grn_obj.purchase_order.po_type if grn_obj.purchase_order else grn_obj.sales_return.return_type
                self.extra_params.update({"transaction_type": grn_obj.grn_type})
                putaway_suggestions_for_grn(self.warehouse, self.user, grn_obj.grn_type, po_type, grn_obj.grn_number, self.ids_for_suggestions, extra_params=self.extra_params)
                if self.update_type == 'qc' and self.inbound_staging_lanes:
                    update_staging_stock_for_grn(self.warehouse.id, grn_obj.grn_number, self.ids_for_suggestions)
            if self.ids_for_staging and self.inbound_staging_lanes:
                create_staging_stock_for_grn(self.warehouse.id, grn_obj.grn_number, self.ids_for_staging)

            return {'message': 'Updated Successfully', 'status': 200}

        except Exception as e:
            log.error(f"Update GRN failed for {self.request_data}: {e}", exc_info=True)
            return {'message': 'Update Failed', 'status': 400}

    def update_encoded_grn_file(self):
        '''
        This method is used to update encoded grn file
        '''
        if self.encoded_grn_file:
            # Decode the base64 encoded string
            encoded_status = self.write_encoded_data()
            if not encoded_status:
                return "Error decoding or writing PDF file for GRN"+ self.grn_number
        if self.grn_file_url or self.encoded_grn_file:
            # Save the encoded PDF file
            encoded_status, uploaded_file = self.save_encoded_pdf_convert()
            # Save the encoded PDF file in MasterDocs
            if encoded_status:
                self.create_master_docs_dict('grn_document',uploaded_file)

            else:
                return "Error decoding or writing PDF file for GRN"+ self.grn_number
        return ""

    def write_encoded_data(self):
        '''This method is used to write encoded data to pdf'''
        try:
            reference = self.grn_number.replace("/", "-")
            # Decode the base64 encoded string
            decoded_data = base64.b64decode(self.encoded_grn_file)
            with open(f"{reference}{self.warehouse.id}.pdf", 'wb') as f: # Write the decoded data to a PDF file
                f.write(decoded_data)
            return True
        except Exception as e:
            log.info('Error decoding or writing PDF file for invoice %s and error statement is %s' % (reference, str(e)))
            return False

    def save_encoded_pdf_convert(self):
        '''This method is used to prepare and save invoice pdf'''
        try:
            # Assuming you have the normal PDF file named "inv001.pdf"
            # replace / with -
            reference = self.grn_number.replace("/", "-")
            normal_pdf_path = f"{reference}{self.warehouse.id}.pdf"

            # Read the content of the PDF file into memory
            uploaded_file = ''
            if os.path.exists(normal_pdf_path):
                with open(normal_pdf_path, "rb") as f:
                    file_content = io.BytesIO(f.read())

                # Create an InMemoryUploadedFile object
                uploaded_file = InMemoryUploadedFile(
                    file=file_content,
                    field_name=None,  # Field name from form, if applicable
                    name=normal_pdf_path,  # Desired filename
                    content_type="application/pdf",
                    size=len(file_content.getvalue()),
                    charset=None  # Character encoding, if applicable
                )
                os.remove(normal_pdf_path)
            return True, uploaded_file
        except Exception as e:
            if os.path.exists(normal_pdf_path):
                os.remove(normal_pdf_path)
            log.info('Error uploading PDF file for invoice %s and error statement is %s for %s' % (reference, str(e), str(self.warehouse.username)))
            return False,''

    def create_master_docs_dict(self, master_type, uploaded_file):
        '''
        This method is used to create master docs dict
        '''
        data_dict = {
            'master_id': self.grn_number,
            'master_type': master_type,
            'extra_flag': self.grn_reference,
            'document_url': self.grn_file_url,
            'user_id': self.warehouse.id,
            'account_id': self.warehouse.userprofile.id
        }
        master_docs_obj = MasterDocs.objects.filter(master_type=master_type, master_id=self.grn_number,user_id = self.warehouse.id)
        if master_docs_obj.exists():
            master_docs_obj = master_docs_obj[0]
            master_docs_obj.document_url = self.grn_file_url
            master_docs_obj.uploaded_file = uploaded_file
            master_docs_obj.save()
        else:
            master_docs_obj= MasterDocs(**data_dict)
            master_docs_obj.uploaded_file = uploaded_file
            master_docs_obj.save()

    def delete(self, *args, **kwargs):
        """ GRN Cancellation """
        request = self.request
        try:
            # Attempt to load JSON data from the request body
            request_data = json.loads(request.body)
        except json.JSONDecodeError:
            # If JSON decoding fails, try to get query parameters
            request_data = request.GET.dict()

        # Check if request_data is empty and return an error response if it is
        if not request_data:
            return JsonResponse({'message': 'Invalid Request'}, status=400)

        self.set_user_credientials()
        try:
            response = grn_reversal(self.warehouse, request_data, request)
            return JsonResponse({'message': response.get('message')}, status=response.get('status'))
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('GRN Reversal Failed for '+str(request_data) + " error "+str(e))
            return  JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)

def create_staging_stock_for_grn(warehouse_id, grn_number, grn_ids):
    """Create Staging for GRN."""
    try:
        warehouse = User.objects.get(id=warehouse_id)
        stock_creation_list = []
        values_list = ['id', 'sku_id', 'quantity', 'batch_detail_id', 'sku__is_barcode_required']
        grn_objs = list(SellerPOSummary.objects.filter(user=warehouse_id, grn_number=grn_number, id__in=grn_ids).values(*values_list))
        rec_loc = create_default_zones(warehouse, 'REC_ZONE1', 'REC_LOC1', 99999, 'inbound_staging', storage_type='REC')
        rec_loc = rec_loc[0].id
        for grn_obj in grn_objs:
            destination_details = {
                'sku_id' : grn_obj.get('sku_id'),
                'receipt_type' : "grn_packing",
                'receipt_number': grn_obj.get('id'),
                'grn_number' : grn_number,
                'status' : 0,
                'batch_detail_id' : grn_obj.get('batch_detail_id'),
                'quantity' :  grn_obj.get('quantity'),
                'is_barcode_required': grn_obj.get('sku__is_barcode_required'),
                'dest_loc_id': rec_loc
            }
            stock_creation_list.append(destination_details)
        create_destination_stocks(warehouse, stock_creation_list, staging_stock=True)
    except Exception as e:
        log.error(f"Failed to create staging stock for GRN {grn_number}: {e}", exc_info=True)

def update_staging_stock_for_grn(warehouse_id, grn_number, grn_ids):
    """Update Staging for GRN."""
    try:
        warehouse = User.objects.get(id=warehouse_id)
        grn_ids = list(SellerPOSummary.objects.filter(user=warehouse_id, grn_number=grn_number, id__in=grn_ids).values_list('id', flat=True))
        StockDetail.objects.filter(sku__user=warehouse.id, grn_number=grn_number, receipt_number__in=grn_ids, receipt_type='grn_packing').update(quantity=0) 
    except Exception as e:
        log.error(f"Failed to update staging stock for GRN {grn_number}: {e}", exc_info=True)


@get_warehouse
def grn_upload_preview(request, warehouse):
    transact_types = {
        'grn':'PO_TEMP',
        'asn':'ASN_PO_NUMBER',
        'asn_grn': 'ASN_GRN_Mapping',
        'po': 'PO',
        'rtv': 'RTV_NUMBER',
        'gate_pass': 'gate_pass'
        }
    try:
        request_data = json.loads(request.body)
    except:
        request_data = request.POST
    master_id = request_data.get('data_id', '')
    master_doc_id = request_data.get('id', '')
    master_type = request_data.get('master_type', '')
    
    request_transact_type = request_data.get('transact_type', 'grn')
    if master_type:
        transact_type = master_type
    else:
        transact_type = transact_types.get(request_data.get('transact_type', 'grn'), request_transact_type)

    transaction_type = request_data.get('transaction_types')
    if transaction_type:
        if isinstance(transaction_type, list):
            transaction_type_list = transaction_type
        else:
            transaction_type_list = [transaction_type]
        
        mapped_types = [transact_types.get(t.strip(), t.strip()) for t in transaction_type_list]
        transact_type = mapped_types
    file_upload = request.FILES.get('pdf_file', '')
    response = ''
    if master_id:
        filter_kwargs = {
            'id': master_id,
        }
        if transaction_type:
            filter_kwargs['master_type__in'] = transact_type
        master_docs_objs = MasterDocs.objects.filter(**filter_kwargs)
        if master_docs_objs.exists():
            master_docs_obj = master_docs_objs[0]
        else:
            return JsonResponse({'message' : 'File object not found'}, status=400)
        try:
            if not settings.CLOUD_STORAGE:
                if os.path.exists(master_docs_obj.uploaded_file.path):
                    os.remove(master_docs_obj.uploaded_file.path)
            MasterDocs.objects.filter(**filter_kwargs).delete()
        except Exception as e:
            pass
        return JsonResponse({'message' : 'Success'}, status=200)
    else:
        if not master_doc_id and file_upload:
            return JsonResponse({'message' : 'Fields are missing.'}, status=400)
        try:
            response = bulk_upload_master_file(request, warehouse, master_doc_id, transact_type)
        except Exception as e:
            log.info('Upload File is failed for user %s and params are %s and error statement is %s' % (
                str(request.user.username), str(request.POST.dict()), str(e)))
            return JsonResponse({'message' : 'File Upload Failed !!'}, status=400)
        
        if response == 'Uploaded Successfully' or  not isinstance(response, str):
            master_docs_filter = {
                'master_id': master_doc_id,
                'master_type': transact_type,
                'user_id': warehouse.id,
            }
            uploaded_file_list = get_uploaded_files_data(request, master_docs_filter, return_type='list')
            return JsonResponse({'uploaded_files' : uploaded_file_list})
        return JsonResponse({'message' : 'File Upload Failed !!'}, status=400)

@get_warehouse
def calculate_no_of_containers(request, warehouse):
    """
    Calculates the number of containers based on the given request and
    sku category based configuration.

    Args:
        request (HttpRequest): The HTTP request object.
        warehouse (Warehouse): The warehouse object.

    Returns:
        JsonResponse: The JSON response containing
        SKU pack data along with the calculated number of containers.

    """

    request_data = request.GET
    sku_code = request_data.get('sku_code', '')
    try:
        invoice_qty = float(request_data.get('quantity', 0))
    except ValueError:
        invoice_qty = 0

    #Fetch SKU Pack data
    sku_pack_details = {
        "data": list(SKUPackMaster.objects.filter(
            sku__user=warehouse.id, sku__sku_code=sku_code, status=1
        ).order_by('-pack_quantity').values(
            'pack_id', 'pack_quantity', sku_code=F('sku__sku_code'), sku_category=F('sku__sku_category'))
        )
    }
    if not sku_pack_details['data']:
        return JsonResponse(sku_pack_details, status=200)

    sku_category = sku_pack_details['data'][0].get('sku_category', '')
    #Get SKU weighing scale value for sku category
    grn_weighing_option = MiscDetailOptions.objects.filter(
        misc_detail__misc_type='enable_weighing',
        misc_detail__user=warehouse.id,
        misc_key=sku_category,
        status=1
    ).values('misc_value', 'json_data')

    grn_weighing = ''
    json_data = {}
    weighing_scale = 0
    #Fetch weighing scale for sku category
    if grn_weighing_option.exists():
        grn_weighing = grn_weighing_option[0].get('misc_value', '')
        json_data = grn_weighing_option[0].get('json_data')
        if json_data:
            weighing_scale = json_data.get('weighing_scale', '')
            if weighing_scale:
                weighing_scale = int(weighing_scale)
            else:
                weighing_scale = 0
    #calculate no_of containers based on pack qty and invoice qty
    for data in sku_pack_details['data']:
        no_of_containers = math.ceil(invoice_qty / data['pack_quantity'])

        #for sample weighing no of containers will be sqrt of no of containers + 1
        data['weighing_type'] = 'full_weighing'
        if grn_weighing == 'sample_weighing':
            if no_of_containers <= weighing_scale:
                data['no_of_containers'] = no_of_containers
            else:
                data['weighing_type'] = 'sample_weighing'
                data['no_of_containers'] = math.ceil(math.sqrt(no_of_containers)) + 1
        else:
            data['no_of_containers'] = no_of_containers

    return JsonResponse(sku_pack_details, status=200)
