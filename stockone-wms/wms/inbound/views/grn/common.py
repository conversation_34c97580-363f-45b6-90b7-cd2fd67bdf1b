import json
import datetime
import calendar
import traceback
from collections import defaultdict

from copy import deepcopy

from django.db.models import F, Sum, Q
from django.http import HttpResponse, JsonResponse

#Model Imports
from wms_base.models import User
from core.models import TaxMaster, MasterDocs, SKUMaster, GatePassItem
from inbound.models import (
    SellerPOSummary, QualityCheck, ASNSummary,
    POLocation, SupplierMaster, PurchaseOrder, Pofields
    )

#Inventory Imports
from inventory.models import StockDetail

#Method Imports
from inbound.views.purchase_order.common import save_or_update_po_extra_fields, get_master_doc_details
from core_operations.views.common.user_attributes import (
    FormFields
    )
from core_operations.views.common.main import (
    WMSListView,get_warehouse, get_user_time_zone, truncate_float,
    get_user_attributes, generate_log_message,
    get_local_date_known_timezone, upload_master_file,
    get_sku_ean_numbers, get_misc_value, get_sku_pack_repr,
    get_custom_sku_attributes_and_values, get_extra_attributes, get_decimal_value
    ) 
from inbound.views.common.fetch_query import get_data_from_custom_params
from inbound.views.common.common import (
    get_serial_numbers_with_reference_number, get_grn_data_with_grn_number,
    get_sku_pack_size_with_sku_codes, get_sku_pack_data_with_sku_ids,
    frame_filters_with_request_dict, get_sr_grn_data_with_grn_number
    )
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

from inbound.views.common.constants import SLASH_DATE_FORMAT, DEFAULT_DATETIME_FORMAT

# serializers
from inbound.serializers.grn import ASNSerializer

from outbound.models import SalesReturnBatchLevel

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/common_grn' + today + '.log')
log_err = init_logger('logs/common_grn.log')

@get_warehouse
def get_asn_numbers(request, warehouse:User):
    request_data = request.GET
    message, filter_dict, asn_numbers = get_asn_numbers_list(request_data, warehouse)
    if message:
        return JsonResponse({'error_message' : message}, status=400)
    return JsonResponse({'data' : asn_numbers}, status=200)

def get_asn_numbers_list(filters, warehouse, date_filters = {}, status_filters={}):
    filter_dict = {'asn_user' : warehouse.id}
    filter_keys = [
            'id', 'asn_number', 'po_number', 'po_reference', 'sku_code', 'status',
            'invoice_number', 'batch_no', 'batch_reference', 'inspection_lot_number',
            'supplier_name', 'supplier_id', 'po_type'
            ]
    message, framed_filters, [] = frame_filters_with_request_dict(filters, filter_keys)
    if message:
        return message, framed_filters, []
    if framed_filters:
        filter_dict.update(framed_filters)
    if date_filters:
        filter_dict.update(date_filters)
    if status_filters:
        filter_dict.update(status_filters)
    exclude_dict = {'status': 8}
    #Fetch Supplier Master Data
    extra_params = {
        'filters' : filter_dict, 'order_by' : ['asn_number'],
        'distincts' : ['asn_number'],
        'value_keys' : ['asn_number'], 'return_type' : 'values_list',
        'flat' : True, 'excludes' : exclude_dict
        }
    asn_numbers = \
        get_data_from_custom_params(ASNSummary, 'asn', extra_params)
    
    return '', filter_dict, asn_numbers


def get_grn_po_sku_wise_sps_data(users, grn_numbers):
    #invoice_date, 
    timezone = get_user_time_zone(users[0], users[0])
    #Fetching Username
    warehouse_user = User.objects.get(id=users[0])
    warehouse = warehouse_user.username

    #### GRN HEADER KEYS ####
    grn_value_list = [
        'grn_number', 'grn_date', 'grn_tax_value', 'grn_amount_without_tax', 
        'grn_amount_with_tax', 'grn_quantity',
        'invoice_number', 'invoice_value', 'invoice_date', \
        'supplier_id', 'supplier_name', 'supplier_phone_number',
        'supplier_email', 'supplier_gst', 'supplier_reference',
        ]
    sps_header_json_keys = [
        ('additional_cost', 'additional_cost', 0),
        ('discount', 'discount', 0), ('remarks', 'remarks', '')
        ]
    sps_header_import_json_keys = [
        ('boe_number', 'boe_number', '' ), ('boe_total', 'boe_total', 0), ('boe_date', 'boe_date', ''),\
        ('remittance_advice_no', 'remittance_adv_no', ''), ('remittance_advice_date', 'remittance_adv_date', ''),\
        ('exchange_rate', 'exchange_rate', ''), ('date_of_exchange_rate', 'exchange_rate_date', '')
        ]

    #### GRN LINE KEYS ####
    sku_value_list = [
        'sku_code', 'sku_description', 'sku_category', 'sku_brand', 'sku_weight', 'sku_reference',\
        'po_quantity' ,'grn_quantity', 'invoice_quantity', 'received_quantity', 'invoice_value',\
        'grn_amount_with_tax', 'grn_amount_without_tax', 'price', 'mrp', 'uom',
        'sgst_tax', 'cgst_tax', 'igst_tax', 'cess_tax', 'apmc_tax',
        'batch_no', 'manufactured_date', 'expiry_date', 'json_data', 'product_type']

    sps_sku_json_keys = [('shortage_quantity', 'shortage', 0)]

    sps_sku_import_json_keys = [('assessable_value', 'assessable_value', ''), ('customs_duty','custom_duty_charges', ''),
        ('additional_charges','additional_charges', ''), ('taxable_value', 'taxable_value', 0),
        ('cess_amount', 'cess_amount', 0), ('gst_amount', 'gst_amount', 0)]
    batch_import_json_keys = [('bond_no', 'bond_no', ''), ('bond_date', 'bond_date', '')]


    #### PO KEYS ####
    po_value_list = ['po_number', 'po_date', 'po_type', 'po_reference', 'po_line_reference', 'open_po_json_data']
    open_po_json_keys = [('line_reference', 'line_reference', '')]
   
    #ean_number, 'reasons', 'gst_amount'
    

    #Fetching data from SPS
    get_value_list = [*grn_value_list, *sku_value_list, *po_value_list]
    filters = {'grn_number__in' : grn_numbers, 'purchase_order__open_po__sku__user__in' : users}
    sps_extra_params = {'filters' : filters, 'value_keys' : get_value_list, 'return_type' : 'values_list'}
    sps_data = \
        get_data_from_custom_params(SellerPOSummary, 'grn',  sps_extra_params)

    #Fetching data from QualityCheck for each grn
    qc_filters = {'po_location__seller_po_summary__grn_number__in':grn_numbers, 'po_location__location__zone__user__in': users}
    qc_values = ['grn_number', 'sku_code', 'batch_no', 'accepted_qty', 'rejected_qty']
    qc_aggregates = {'accepted_qty': 'Sum', 'rejected_qty': 'Sum'}
    qc_distincts = ['grn_number', 'sku_code', 'batch_no']
    qc_extra_params = {
        'filters' : qc_filters, 'distincts' : qc_distincts, 'aggregateds' : qc_aggregates,
        'value_keys' : qc_values, 'return_type' : 'values_list', 'is_reports_db'  : True
        }

    qc_data = get_data_from_custom_params(QualityCheck, 'qc', qc_extra_params)
    
    
    #Fetching SerialNumbers on GRN Number, SKU Based 
    sn_filters = {
        'reference_type': 'pending_putaway',
        'reference_number__in': set(grn_numbers),
        }
    sn_return_type = 'value_dict'
    value_keys = ['reference_number', 'serial_number', 'sku_code', 'batch_no']
    serial_number_dict = get_serial_numbers_with_reference_number(sn_filters, users[0], sn_return_type, value_keys, batch_based=True)
    
    #Fetching Tax Details on Product Type
    tax_objs = dict(TaxMaster.objects.filter(user=users[0]).values_list('product_type', 'reference_id'))
    tax_map_dict = {product_type : reference_id for product_type, reference_id in tax_objs.items()}
    
    
    #GRN DETAILS 
    grn_header_data_dict = {} #Key - grn_number
    for each_row in sps_data:
        dict_key = (each_row['grn_number'])
        grn_header_details = {}
        #GRN Header Data from SPS
        for grn_key in grn_value_list:
            grn_header_details[grn_key] = each_row[grn_key]
        
        #GRN Header Data from SPS JSON 
        for json_key in sps_header_json_keys:
            grn_header_details[json_key[0]] = each_row.get('json_data', {}).get(json_key[1], json_key[2])
        
            #GRN Header Import Data from SPS JSON 
            if each_row.get('po_type').lower() == "import":
                for json_key in sps_header_import_json_keys:
                    grn_header_details[json_key[0]] = each_row.get('json_data', {}).get(json_key[1], json_key[2])
        
        for each_key, each_value in grn_header_details.items():
            if 'date' in each_key:
                if each_value:
                    if each_key == 'invoice_date':
                        grn_header_details[each_key] = grn_header_details[each_key].strftime('%Y-%m-%d')
                    else:
                        grn_header_details[each_key] = get_local_date_known_timezone(timezone, grn_header_details[each_key], send_date=True).strftime(DEFAULT_DATETIME_FORMAT)
                else:
                    grn_header_details[each_key] = ''

        grn_header_data_dict[each_row['grn_number']] = grn_header_details

    tot_grn_tax, total_amt_with_tax, total_grn_qty, tot_grn_without_tax = 0, 0, 0, 0
    for each_row in sps_data:
        tot_grn_tax += each_row['grn_tax_value']
        tot_grn_without_tax += each_row['grn_amount_without_tax']
        total_amt_with_tax += (each_row['grn_tax_value'] + each_row['grn_amount_without_tax'])
        total_grn_qty += each_row['grn_quantity']
    grn_header_data_dict[each_row['grn_number']].update({'grn_tax_value': tot_grn_tax, 'grn_amount_without_tax': tot_grn_without_tax, 'grn_amount_with_tax':total_amt_with_tax, 'grn_quantity': total_grn_qty, "warehouse": warehouse})
        
    #PO DETAILS
    po_header_data_dict = {}
    for each_row in sps_data:
        dict_key = (each_row['grn_number'])
        po_header_details = {}
        #PO Header Data from Purchase Order
        for po_key in po_value_list:
            if po_key in ["json_data", "open_po_json_data"]:
                continue                        
            else:
                po_header_details[po_key] = each_row[po_key]
        for each_key, each_value in po_header_details.items():
            if 'date' in each_key:
                if each_value:
                    po_header_details[each_key] = get_local_date_known_timezone(timezone, po_header_details[each_key], send_date=True).strftime(DEFAULT_DATETIME_FORMAT)
                else:
                    po_header_details[each_key] = ''
        po_header_data_dict[dict_key] = po_header_details
        
    #SKU DETAILS
    sku_data_dict = {}
    for each_row in sps_data:
        dict_key = (each_row['grn_number'],each_row['po_number'])
        sku_details = {}
        for sku_key in sku_value_list:
            qc_dict_get_key = (each_row['grn_number'], each_row['sku_code'], each_row['batch_no'])
            if sku_key in ["json_data", "open_po_json_data"]:
                continue                        
            else:
                sku_details[sku_key] = each_row[sku_key]
            
            #SKU Data from SPS JSON
            for json_key in sps_sku_json_keys:
                sku_details[json_key[0]] = each_row.get('json_data', {}).get(json_key[1], json_key[2])
            
            #SKU Level QC Data
            sku_details['accepted_qty'] = qc_data.get(qc_dict_get_key, {}).get("accepted_qty")
            sku_details['rejected_qty'] = qc_data.get(qc_dict_get_key, {}).get("rejected_qty")

            #Tax Reference
            sku_details['tax_reference'] = tax_map_dict.get(each_row.get('product_type'), '')

            #Fetching Serial Numbers at SKU Level
            serial_key = (each_row['grn_number'], each_row['sku_code'], each_row['batch_no'])
            sku_details['serial_numbers'] = serial_number_dict.get(serial_key, [])
            sku_details['grn_amount_with_tax'] = each_row['grn_tax_value'] + each_row['grn_amount_without_tax']
            
            #SKU Import Data from SPS JSON 
            if each_row.get('po_type').lower() == "import":
                for json_key in sps_sku_import_json_keys:
                    sku_details[json_key[0]] = each_row.get('json_data', {}).get(json_key[1], json_key[2])
            
            #OPEN PO JSON Data
            sku_details['aux_data'] = {}
            if each_row.get('open_po_json_data'):
                for json_key in open_po_json_keys:
                    sku_details['aux_data'][json_key[0]] = each_row.get('open_po_json_data', {}).get(json_key[1], json_key[2])
        
        for each_key, each_value in sku_details.items():
            if 'date' in each_key:
                if each_value:
                    sku_details[each_key] = get_local_date_known_timezone(timezone, sku_details[each_key], send_date=True).strftime('%Y-%m-%d')
                else:
                    sku_details[each_key] = ''
        if dict_key in sku_data_dict:
            sku_data_dict[dict_key].append(sku_details)
        else:
            sku_data_dict[dict_key] = [sku_details]
    
    results_data_list = []
    for grn_key, grn_dict in grn_header_data_dict.items():
        po_data_list = []
        for grn_po_key, po_dict in po_header_data_dict.items():
            sku_data_list = []
            for grn_po_key, sku_dict in sku_data_dict.items():
                sku_data_list.append(sku_dict)
            po_dict['sku_data'] = sku_data_list
            po_data_list.append(po_dict)
        grn_dict['po_data'] = po_data_list
        results_data_list.append(grn_dict)
    return results_data_list

def get_asn_sku_wise_data(warehouse, asn_numbers):
    queryset = ASNSummary.objects.filter(
                    asn_number__in = asn_numbers,
                    asn_user=warehouse,
                    status__in=[1,2]
                )
    asn_data =  ASNSerializer(queryset,many=True)
    results_data_list = asn_data.data

    return results_data_list

class SKUWiseGRN(WMSListView):

    def headerwise_calc(self, line_wise_grn_data, field, decimal_limit = 0):
        res_calc = 0
        for row in line_wise_grn_data:
            res_calc += row[field] if row[field] else 0
        if decimal_limit:
            res_calc = truncate_float(res_calc, decimal_limit)
        return res_calc
    
    def get_checklist_data(self):
        asn_form_dict = FormFields().get_checklist_data("ASN", [self.line_dict.get('asn_id')], extra_flag=self.line_dict.get('vendor_batch_number'))
        #ASN Form Attributes
        asn_checklist_data = {}
        asn_form_values = asn_form_dict.get(self.line_dict['asn_id']) or {}
        if asn_form_values:
            for asn_id, attr_values in asn_form_dict.items():
                attr_ids = attr_values.keys()
                asn_form_attrs = get_user_attributes(self.warehouse, attr_ids=attr_ids)
                for each_attr in asn_form_attrs:
                    att_key = each_attr.get('attribute_name')
                    attr_id = each_attr.get('id')
                    asn_form_value = asn_form_values.get(attr_id, '')
                    if ":" in asn_form_value:
                        asn_form_value = asn_form_value.split(':')[0]
                    asn_checklist_data[att_key] = asn_form_value
        return asn_checklist_data

    def update_short_qty_details(self, grn_json_data):
        if grn_json_data.get('remarks') == 'shortage':
            self.line_dict['rejected_quantity'] = self.line_dict.get('grn_quantity', 0)

    def frame_extra_data(self, extra_data, decimal_limit, open_po_data):
        #EAN Numbers
        self.line_dict['ean_number'] = self.ean_numbers_dict.get(self.line_dict.get('sku_code'),[])
        
        #SKU Pack Size
        if open_po_data.get('uom_qty',''):
            self.line_dict['sku_pack_size'] = int(open_po_data.get('uom_qty',1))
        else:
            self.line_dict['sku_pack_size'] =  self.sku_pack_sizes.get(self.line_dict.get('sku_code'),{}).get('pack_quantity', 1)
        
        if not isinstance(self.line_dict['sku_pack_size'], list):
            self.line_dict['sku_pack_size'] = [self.line_dict['sku_pack_size']]

        #Checklist Data
        if extra_data.get('send_check_list'):
            self.line_dict['asn_checklist_data'] = json.dumps(self.get_checklist_data())
        
        if extra_data.get('line_wise_uploaded_files'):
            self.line_dict['grn_rejected_files'] = extra_data.get('line_wise_uploaded_files', {}).get(str(self.line_dict.get('id')), [])

        #AS OF NOW TAking from ASN JSON Data, Since I'm not Getting This Data from GRN Payload
        grn_json_data = self.line_dict.pop('json_data') or {}
        asn_json_data = self.line_dict.pop('asn_json_data') or {}
        self.line_dict['no_of_containers'] = grn_json_data.get('no_of_containers', 0)
        if not self.line_dict['no_of_containers']:
            self.line_dict['no_of_containers'] = asn_json_data.get('no_of_containers') or None

        if grn_json_data.get('lpns', ''):
            self.line_dict['lpn_numbers'] = grn_json_data.pop('lpns', '')
        #Fetch Serial numbers data
        serial_data = extra_data.get('serial_dict', {}).get(self.line_dict.get('id'), {})
        self.line_dict['accepted_serial_numbers'] = serial_data.get('accepted', [])
        self.line_dict['rejected_serial_numbers'] = serial_data.get('rejected', []) or serial_data.get('short', [])

        if grn_json_data.get('zone_restriction', ''):
            self.line_dict['put_zone'] = grn_json_data.get('zone_restriction')

        #update short qty details
        self.update_short_qty_details(grn_json_data)
        self.truncate_grn_quantities(decimal_limit)

        self.line_dict['grn_display_key'] = self.line_dict.get('grn_reference') or self.line_dict['grn_reference']
        self.line_dict['po_display_key'] =  self.line_dict['po_reference'] or self.line_dict['po_number']
        self.line_dict['batch_display_key'] =  self.line_dict['batch_reference'] or self.line_dict['batch_number']

    def truncate_grn_quantities(self, decimal_limit):
        for key in ['grn_quantity', 'invoice_quantity', 'received_quantity', 'po_quantity', 'accepted_quantity', 'rejected_quantity']:
            if key in self.line_dict:
                self.line_dict[key] = truncate_float(self.line_dict[key], decimal_limit)

    def linewise_keys(self, line_wise_grn_data, decimal_limit, add_data, extra_data, price_decimal_limit, open_po_data):
        keys_list = [
            ('supplier_name', 'supplier_name'),
            ('grn_date' , 'creation_date'), ('grn_reference', 'grn_reference'), ('grn_quantity','quantity'),
            ('po_number', 'po_number'), ('po_reference', 'po_reference'), ('po_line_reference', 'po_line_reference'),
            ('batch_number', 'batch_no' ), ('batch_reference', 'batch_reference' ), 
            ('manufactured_date', 'manufactured_date'), ('expiry_date', 'expiry_date'),
            ('retest_date', 'retest_date'), ('mrp', 'mrp'), ('json_data', 'json_data'),
            ('vendor_batch_number', 'vendor_batch_number'), ('inspection_lot_number', 'inspection_lot_number'), 
            ('open_po_json', 'open_po_json_data'), ('po_uom_qty', 'po_uom_qty'),('po_uom', 'po_uom'),
            ('manufacturer_name', 'manufacturer_name'), ('manufacturer_address', 'manufacturer_address'),
            ('asn_id', 'asn_id'), ('asn_json_data', 'asn_json_data'), ('asn_qty', 'asn_quantity'),
            ('accepted_quantity', 'accepted_quantity'), ('rejected_quantity', 'rejected_quantity'),
            ('po_quantity','po_order_quantity'), ('price','price'),
            ('grn_amount_with_tax', 'grn_amount_with_tax'), ('grn_amount_without_tax', 'grn_amount_without_tax'),
            ('sgst', 'sgst_tax'), ('cgst','cgst_tax'), ('igst','igst_tax'), ('cess','po_cess_tax'), ('apmc','apmc_tax'),
            ('sku_code','sku_code'), ('sku_id', 'sku_id'), ('sku_description','sku_desc'),
            ('hsn_code', 'hsn_code'), ('sku_weight','sku_weight'), ('sku_uom','sku_uom'),
            ('sku_category','sku_category'),('sku_brand','sku_brand'),
            ('id', 'id'),
            ('rejected_reasons', 'remarks'), ('invoice_quantity', 'invoice_quantity'),
            ('created_by', 'created_by'), ('updated_by', 'updated_by'), ('cancelled_by', 'cancelled_by'),
            ('batch_detial_id', 'batch_detail_id'), ('status', 'status'),
            ]
        line_list = []
        for row in line_wise_grn_data:
            self.line_dict = {}
            for keys in keys_list:
                # if keys[0] in ['accepted_quantity', 'rejected_quantity']:
                #     self.line_dict[keys[0]] = self.qc_dict.get(row.get('id'),{}).get(keys[0], 0)
                if keys[0] in ['manufactured_date', 'expiry_date', 'retest_date', 'grn_date']:
                    self.line_dict[keys[0]] = ''
                    if row.get(keys[1]):
                        self.line_dict[keys[0]] = get_local_date_known_timezone(
                            self.timezone, row.get(keys[1]), send_date=True).strftime(SLASH_DATE_FORMAT)                    
                elif keys[0] in ['manufacturer_name', 'manufacturer_address']:
                    self.line_dict[keys[0]] = self.line_dict.get('open_po_json', {}).get(keys[0], '')
                elif keys[0] in ['grn_amount_without_tax', 'grn_amount_with_tax', 'mrp', 'asn_qty', 'price']:
                    self.line_dict[keys[0]] = truncate_float(row[keys[1]], price_decimal_limit)
                elif keys[0] == "cancelled_by":
                    self.line_dict[keys[0]] = self.line_dict.get('updated_by') if row.get('status') == 9 else ''
                else:
                    self.line_dict[keys[0]] = row[keys[1]]
                self.line_dict['container_no'] = ''
                self.line_dict['sku_attributes'] = self.sku_attributes_dict.get(self.line_dict.get('sku_code', ''), {})
                self.line_dict['invoice_quantity'] = row.get('invoice_quantity', 0) or row.get('line_invoice_quantity', 0)

            #SKU Pack Repr
            pack_repr = ''
            packing_rep_data = self.sku_pack_dict.get(self.line_dict.get('sku_id'), {})
            if packing_rep_data:
                pack_repr = get_sku_pack_repr([packing_rep_data], self.line_dict.get('grn_quantity'))
            self.line_dict['sku_pack_repr'] = pack_repr

            self.frame_extra_data(extra_data, decimal_limit, open_po_data)
            self.line_dict.update(add_data)
            if row.get('batch_detail_id'):
                extra_batch_dict = self.batch_extra_attributes.get(str(row['batch_detail_id']), {})
                self.line_dict.update({'extra_batch_attributes': extra_batch_dict, **extra_batch_dict})
            if extra_data.get('send_pack_wise') == True and self.line_dict['no_of_containers']:
                line_list.extend(self.frame_packwise_data(self.line_dict))
            else:
                line_list.append(self.line_dict)
        return line_list

    def get_pack_details(self, data):
        pack_size, packs, eachs = None, None, None
        if data.get('sku_pack_size'):
            pack_size = data['sku_pack_size'][0]
            packs = int(data['grn_quantity'] // pack_size)
            eachs = data['grn_quantity'] - packs * pack_size
        return pack_size, packs, eachs

    def frame_packwise_data(self, data):
        """Frame grn data into packs """
        pack_wise_list = []
        for i in range(data.get('no_of_containers') or 1):
            copy_data = data.copy()
            copy_data['container_number'] = i+1
            pack_wise_list.append(copy_data)

        #If No Pack Master Defined
        if not pack_wise_list:
            pack_wise_list = [data]
        return pack_wise_list

    def get_gate_pass_data_dict(self, asn_numbers):
        """Fetch Gate pass information for each ASN"""
        asn_gate_pass_data = {}
        gate_pass_data = GatePassItem.objects.filter(
            gate_pass__warehouse_id=self.warehouse.id,
            transaction_id__in=asn_numbers,
            transaction_type="material_receipt"
        ).exclude(status=2).values("transaction_id", "creation_date", gate_id = F("gate_pass__gate_pass_id"))
        for each_data in gate_pass_data:
            asn_gate_pass_data[each_data['transaction_id']] = each_data

        return asn_gate_pass_data

    def get_grn_sku_wise_sps_data(self, warehouse, grn_numbers, add_data={}, extra_data={}):
        self.warehouse = warehouse
        decimal_limit = int(extra_data.get('decimal_limit', 0) or 0)
        price_decimal_limit = get_decimal_value(self.warehouse.id, 'price')
        excludes = extra_data.get('excludes', {})
        values_list = [
            'id', 'user_id', 'grn_number', 'grn_reference', 'creation_date', 'quantity', 'putaway_quantity',
            'price', 'json_data', 'invoice_number', 'invoice_date', 'creation_date',
            'invoice_quantity', 'status', 'invoice_value','receivable_quantity', 'remarks',
            'accepted_quantity', 'rejected_quantity',

            'batch_no', 'batch_reference', 'manufactured_date', 'expiry_date', 'mrp',
            'vendor_batch_number', 'inspection_lot_number', 'retest_date',

            'asn_id', 'asn_quantity', 'asn_json_data', 'asn_number', 'asn_date', 'asn_reference',

            'po_number', 'po_received_quantity', 'po_date', 'po_line_reference',
            
            'po_reference', 'po_order_quantity', 'open_po_json_data', 'open_po_ship_to',
            'igst_tax', 'cgst_tax', 'sgst_tax', 'utgst_tax', 'po_cess_tax', 'apmc_tax',
            'order_type', 'po_type','uom_qty','po_uom',

            'sku_id', 'hsn_code', 'sku_code', 'sku_desc', 'sku_type', 'sku_category',
            'sku_brand', 'sku_weight', 'sku_uom', 

            'supplier_name', 'supplier_id',  'supplier_phone_number', 'supplier_email', 
            'supplier_cst_number', 'supplier_pan_number', 'supplier_address',
            'grn_amount_with_tax', 'grn_amount_without_tax', 'created_by', 'updated_by',
            'supplier_reference', 'tcs_value', 'batch_detail_id', 'line_invoice_quantity'
            
            ]
        
        grn_extra_data = {
            'return_type': 'values_list', 'value_key_list' : values_list,
            'excludes': excludes
            }

        results = get_grn_data_with_grn_number(grn_numbers, self.warehouse, grn_extra_data)

        if not extra_data.get('uploaded_files'):
            extra_data['uploaded_files'] = get_master_doc_details(type('', (), {'META': {}})(), [self.warehouse.id], grn_numbers, 'GRN_PO_NUMBER')
        
        line_wise_dict = {}
        sku_codes, sku_ids, sps_ids, asn_ids = [], [], [], []
        total_po_quantity_dict, sku_wise_po_list = {}, []
        asn_numbers = []
        batch_detail_ids = []
        for res_row in results:
            po_sku_code = res_row['sku_code']
            sps_ids.append(res_row['id'])
            asn_ids.append(res_row['asn_id'])
            sps_key = res_row["grn_number"]
            asn_numbers.append(res_row["asn_number"])
            if res_row['batch_detail_id']:
                batch_detail_ids.append(res_row['batch_detail_id'])
            if po_sku_code not in sku_codes:
                sku_ids.append(res_row['sku_id'])
                sku_codes.append(po_sku_code)
            res_row['po_uom_qty'] = res_row.get('grn_quantity', 1) * res_row.get('uom_qty', 1)
            if sps_key in line_wise_dict:
                line_wise_dict[sps_key].append(res_row)
            else:
                line_wise_dict[sps_key] = [res_row]
            
            po_number, po_quantity  = res_row.get('po_number'), res_row.get('po_order_quantity')
            po_line_reference = res_row.get('po_line_reference', '')
            unique_key = (po_number, po_sku_code, po_line_reference)
            #Total Po Quantity
            if unique_key not in sku_wise_po_list:
                sku_wise_po_list.append(unique_key)
                total_po_quantity_dict[po_number] = total_po_quantity_dict.get(po_number, 0) + po_quantity

        # self.qc_dict = qc_dict
        self.ean_numbers_dict = get_sku_ean_numbers(sku_codes, self.warehouse)
        self.sku_pack_sizes = get_sku_pack_size_with_sku_codes(sku_codes, self.warehouse.id)
        self.sku_pack_dict = get_sku_pack_data_with_sku_ids(sku_ids, warehouse_id=self.warehouse.id)
        self.sku_attributes_dict = get_custom_sku_attributes_and_values({'sku__user':self.warehouse.id, 'sku__in' : sku_ids})
        #Fetching Batch Extra Attributes
        self.batch_extra_attributes = get_extra_attributes(batch_detail_ids, 'extra_batch_attributes')
        
        #fetch gate pass information
        gate_pass_dict = self.get_gate_pass_data_dict(asn_numbers)

        #fetch uploaded files
        uploaded_files_data = extra_data.get('uploaded_files', {}) or {}

        data_list = []
        self.timezone = get_user_time_zone(self.warehouse.id, self.warehouse.id)
        extra_field_dict = list(
            Pofields.objects.filter(
                user=self.warehouse.id,
                receipt_no__in=grn_numbers,
                field_type='grn_field'
            ).values('name', 'value', 'field_type', 'receipt_no')
        )

        grn_fields_dict = defaultdict(dict)
        for field in extra_field_dict:
            grn_fields_dict[field['receipt_no']][field['name']] = field['value']

        serial_filter = {'filters': {'reference_type': 'grn', 'transact_id__in': sps_ids}}
        serial_mixin = SerialNumberTransactionMixin(self.warehouse, self.warehouse, serial_filter)
        existing_serials = serial_mixin.get_sntd_details()
        existing_serials_dict = {}
        for data in existing_serials.get('data', []):
            existing_serials_dict.setdefault(data['transact_id'], {'accepted': [], 'rejected': [], 'short': []})[data['transact_type']].extend(data['serial_numbers'])
        extra_data['serial_dict'] = existing_serials_dict

        for grn_number, data in line_wise_dict.items():
            uploaded_files = uploaded_files_data.get(grn_number, []) or []
            uploaded_file_urls = [file['file_url'] for file in uploaded_files_data.get(grn_number, []) or []]
            line_wise_grn_data = line_wise_dict[grn_number]
            asn_number = data[0]['asn_number'] if data[0]['asn_number'] else ''
            gate_id_data = gate_pass_dict.get(asn_number, {})
            gate_pass_id = gate_id_data.get('gate_id', '')
            gate_pass_date = get_local_date_known_timezone(self.timezone, gate_id_data['creation_date'], send_date=True).strftime(SLASH_DATE_FORMAT) if gate_id_data.get('gate_id', '') else ''
            add_data.update({"gate_pass_id" : gate_pass_id, "gate_pass_date": gate_pass_date, "asn_number": asn_number})
            items_dict = self.linewise_keys(line_wise_grn_data, decimal_limit, add_data, extra_data, price_decimal_limit, data[0])
            sps_json = data[0].get('json_data', {}) or {}
            status_list = [item.get('status', None) for item in data]
            first_status = status_list[0]
            if all(status == first_status for status in status_list) and first_status in [0, 9]:
                grn_status = first_status
            else:
                grn_status = first_status
            data_dict = {
                'warehouse' : self.warehouse.username,
                'grn_number' : grn_number,
                'po_number': data[0]['po_number'],
                'grn_status': grn_status,
                'grn_reference' : data[0]['grn_reference'],
                'grn_display_key' :  data[0]['grn_reference'] or grn_number,
                'grn_quantity' : self.headerwise_calc(line_wise_grn_data, 'quantity', decimal_limit=decimal_limit),
                'tax_amount' : self.headerwise_calc(line_wise_grn_data, 'grn_amount_with_tax', decimal_limit=price_decimal_limit) - self.headerwise_calc(line_wise_grn_data, 'grn_amount_without_tax', decimal_limit=price_decimal_limit),
                'grn_date' : get_local_date_known_timezone(self.timezone, data[0]['creation_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT),
                'grn_amount_without_tax' : self.headerwise_calc(line_wise_grn_data, 'grn_amount_without_tax', decimal_limit=price_decimal_limit),
                'grn_amount_with_tax' : self.headerwise_calc(line_wise_grn_data, 'grn_amount_with_tax', decimal_limit=price_decimal_limit),
                'asn_number' : asn_number,
                'asn_reference': data[0].get('asn_reference', ''),
                'asn_date' : get_local_date_known_timezone(self.timezone, data[0]['asn_date'], send_date=True).strftime(SLASH_DATE_FORMAT) if data[0]['asn_date'] else '',
                'po_date' : get_local_date_known_timezone(self.timezone, data[0]['po_date'], send_date=True).strftime(SLASH_DATE_FORMAT) if data[0]['po_date'] else '',
                'invoice_number' : data[0]['invoice_number'],
                'invoice_date' : data[0]['invoice_date'].strftime(SLASH_DATE_FORMAT) if data[0]['invoice_date'] else '',
                'invoice_quantity': self.headerwise_calc(line_wise_grn_data, 'quantity', decimal_limit=decimal_limit),
                'invoice_value' : data[0]['invoice_value'],
                'supplier_id': data[0]['supplier_id'],
                'supplier_name': data[0]['supplier_name'],
                'supplier_reference': data[0]['supplier_reference'],
                'supplier_address': data[0]['supplier_address'],
                'phone_number': data[0]['supplier_phone_number'],
                'email_id': data[0]['supplier_email'],
                'gst_number': data[0]['supplier_cst_number'],
                'pan_number': data[0]['supplier_pan_number'],
                'order_type': data[0]['order_type'],
                'po_type': data[0].get('po_type', ''),
                'po_quantity': round(float(total_po_quantity_dict.get(data[0]['po_number'])), decimal_limit),
                'uploaded_files': uploaded_files,
                'uploaded_file_urls': uploaded_file_urls,
                'items': items_dict,
                'ship_to': data[0].get('open_po_ship_to', ''),
                'lr_number': sps_json.get('lr_number', ''),
                'discount': sps_json.get('discount', 0),
                'net_amount': self.headerwise_calc(line_wise_grn_data, 'grn_amount_with_tax', decimal_limit=price_decimal_limit),
                'tcs_value': data[0].get('tcs_value', 0),
                'grn_extra_attributes': grn_fields_dict.get(grn_number, {}),
                "gate_pass_id" : gate_pass_id,
                "gate_pass_date" : gate_pass_date,
                **add_data
            }
            #caclulate net amount - taken reference from grn document
            if sps_json.get('discount', 0) or data_dict.get('tcs_value', 0):
                try:
                    discount = float(sps_json.get('discount', 0))
                    tcs_value = float(data_dict.get('tcs_value', 0))
                except Exception:
                    discount = 0
                    tcs_value = 0
                data_dict['net_amount'] = data_dict['net_amount'] - discount + tcs_value

            data_list.append(data_dict)
        return data_list
    
def get_grn_sku_wise_jo_data(warehouse, search_params, extra_params=dict()):
    data_list = get_jo_completion_get_details(warehouse, search_params, extra_params)
    return data_list

def get_jo_completion_get_details(warehouse, search_parameters, extra_params=dict()):
    stock_details_dict, job_order_data, final_data_list = {}, {}, []
    job_order_data = get_jo_grn_data(warehouse, search_parameters)
    if job_order_data:
        stock_details_dict = get_stock_details(warehouse, job_order_data)
        jo_fg_details, jo_rm_details = prepare_fg_and_rm_data(
            warehouse, job_order_data, stock_details_dict
        )
        final_data_list = prepare_final_data(warehouse, jo_fg_details, jo_rm_details, extra_params)
    return final_data_list

#Get FG Data
def get_jo_grn_data(warehouse, search_parameters):
    search_params = {'user_id__in': [warehouse.id]}
    if 'sku_code' in search_parameters:
        search_params['job_order__product_code__sku_code'] = search_parameters['sku_code']
    if 'from_date' in search_parameters:
        search_params['creation_date__gte'] = search_parameters['from_date']
    if 'to_date' in search_parameters:
        search_params['creation_date__lte'] = search_parameters['to_date']
    if 'grn_number' in search_parameters:
        search_params['grn_number'] = search_parameters['grn_number']
    if 'grn_reference' in search_parameters:
        search_params['grn_reference'] = search_parameters['grn_reference']
    if 'batch_no' in search_parameters:
        search_params['batch_detail__batch_no'] = search_parameters['batch_no']
    if 'batch_reference' in search_parameters:
        search_params['batch_detail__batch_reference'] = search_parameters['batch_reference']
    if 'jo_reference' in search_parameters:
        search_params['job_order__jo_reference'] = search_parameters['jo_reference']
    values_list = {
        'fg_sku_code': F('job_order__product_code__sku_code'),
        'fg_sku_desc': F('job_order__product_code__sku_desc'),
        'fg_job_code': F('job_order__jo_reference'),
        'ordered_quantity': F('job_order__product_quantity'),
        'jo_grn_number': F('grn_number'),
        'grn_json_data': F('json_data'),
        'grn_quantity': F('quantity'),
        'fg_price': F('batch_detail__buy_price'),
        'suppliers_id': F('job_order__supplier__supplier_id'),
        'supplier_name': F('job_order__supplier__name'),
        'fg_batch_no': F('batch_detail__batch_no'),
        'fg_batch_reference': F('batch_detail__batch_reference'),
        'fg_mfg_date': F('batch_detail__manufactured_date'),
        'fg_exp_date': F('batch_detail__expiry_date'),
        'vendor_batch_no': F('batch_detail__vendor_batch_no'),
        'order_type': F('job_order__order_type'),
        'rej_quantity': F('damaged_quantity'),
        'grn_status': F('status'),
        'jo_grn_reference': F('grn_reference')
    }
    job_order_data = SellerPOSummary.objects.filter(**search_params).values(**values_list).\
        annotate(total_grn_qty = Sum('grn_quantity'), total_jo_qty = Sum('job_order__product_quantity'))
    
    return job_order_data

#Get RM Stock Details
def get_stock_details(warehouse, job_order_data):
    stock_ids, stock_details_dict = [], {}
    for jo_data in job_order_data:
        picked_quantity_details = jo_data.get('grn_json_data',{}).get('rm_picking_details',{})
        if picked_quantity_details:
            for rm, details in picked_quantity_details.items():
                for detail in details:
                    stoc_id = detail.get('stock_id','')
                    stock_ids.append(stoc_id) if stoc_id else None

    stock_details = StockDetail.objects.filter(id__in = stock_ids, sku__user__in = [warehouse.id]).\
        values(
        'id', 'sku__sku_code', 'sku__sku_desc', 'sku__sku_brand', 'sku__sku_category',
        'batch_detail__batch_no', 'batch_detail__manufactured_date','batch_detail__expiry_date',
        'batch_detail__buy_price', 'sku__measurement_type', 'location__location','creation_date',
        'batch_detail__json_data__sku_landed_cost', 'json_data'
    )
    for stock in stock_details:
        stock_details_dict[stock.get('id')] = stock
    return stock_details_dict

def get_rm_details(warehouse, rm_quantity_details, stock_details_dict):
    timezone = get_user_time_zone(warehouse)
    rm_details_dict = {}
    for rm, rm_details in rm_quantity_details.items():
        for rm_sku in rm_details:
            stock_details = stock_details_dict.get(rm_sku.get('stock_id'))
            rm_wac, mfg_date, exp_date = 0, '', ''
            rm_wac = stock_details.get('batch_detail__json_data__sku_landed_cost')
            if not rm_wac:
                rm_wac = stock_details.get('batch_detail__buy_price')
            mfg_date, exp_date = (
                stock_details.get('batch_detail__manufactured_date',''),
                stock_details.get('batch_detail__expiry_date')
            )
            if mfg_date:
                mfg_date = format_date(timezone, mfg_date)
            if exp_date:
                exp_date = format_date(timezone, exp_date)

            batch_details = {"rm_batch_no": stock_details.get('batch_detail__batch_no'), 
                            "rm_mfg_date": mfg_date, "rm_exp_date": exp_date,
                            "rm_consumed_qty": rm_sku.get('quantity'),
                            "rm_wac": rm_wac}

            if rm in rm_details_dict:
                rm_details_dict[rm].get('batch_details').append(batch_details)
            else:
                rm_details_dict[rm] = {"rm_sku_code": stock_details.get('sku__sku_code'), 
                                        "rm_sku_desc": stock_details.get('sku__sku_desc'), 
                    'batch_details':[batch_details]}
    return_dict = []
    for rm, rm_dic in rm_details_dict.items():
        return_dict.append(rm_dic)
    return return_dict

def format_date(timezone, obj):
    return get_local_date_known_timezone(
        timezone, obj, send_date=True
    ).strftime('%Y-%m-%d')

def prepare_fg_and_rm_data(warehouse, job_order_data, stock_details_dict):
    timezone = get_user_time_zone(warehouse)
    jo_fg_details, jo_rm_details, mfg_date, exp_date = {}, {}, '',''
    for jo_data in job_order_data:
        fg_uniq_key = (jo_data.get('fg_job_code'), jo_data.get('jo_grn_number'), 
                       jo_data.get('fg_sku_code'), jo_data.get('order_type'))
        
        mfg_date, exp_date = jo_data.get('fg_mfg_date',''), jo_data.get('fg_exp_date','')

        if mfg_date:
            mfg_date = format_date(timezone, mfg_date)
        if exp_date:
            exp_date = format_date(timezone, exp_date)

        if fg_uniq_key not in jo_fg_details:
            batch_no, batch_reference = jo_data.get('fg_batch_no',''), jo_data.get('fg_batch_reference')
            jo_fg_details[fg_uniq_key] = {
                "sku_code": jo_data.get('fg_sku_code'),
                "sku_description": jo_data.get('fg_sku_desc'), 
                "total_qty": jo_data.get('ordered_quantity'),
                "grn_quantity": jo_data.get('grn_quantity',0),
                "fg_wac": jo_data.get('fg_price'),
                "batch_number": batch_no,
                "batch_reference": batch_reference,
                "batch_display_key": batch_reference or batch_no,
                "manufactured_date": mfg_date,
                "expiry_date": exp_date,
                "vendor_batch_number": jo_data.get('vendor_batch_no', ''),
                "rejected_quantity": jo_data.get('rej_quantity', 0),
                "total_grn_quantity": jo_data.get('total_grn_qty', 0),
                "total_jo_qty": jo_data.get('total_jo_qty', 0),
                "status": jo_data.get('grn_status', 0),
                "grn_reference": jo_data.get('jo_grn_reference', ''),
                "grn_number": jo_data.get('jo_grn_number')
            }
            rm_details = get_rm_details(
                warehouse, jo_data.get('grn_json_data', {}).get('rm_picking_details'), stock_details_dict
            )
        else:
            jo_fg_details[fg_uniq_key]['total_grn_quantity'] += jo_data.get('total_grn_qty',0)
            jo_fg_details[fg_uniq_key]['grn_quantity'] += jo_data.get('grn_quantity',0)

        if fg_uniq_key not in jo_rm_details:
            jo_rm_details[fg_uniq_key] = rm_details
    return jo_fg_details, jo_rm_details

def prepare_final_data(warehouse, jo_fg_details, jo_rm_details, extra_params=dict()):
    status_dict = {'SP': 'Standard JO', 'NSP': 'Non Standard JO', 'VP':'Returnable JO'}
    final_dict, data, final_data_list = {}, {}, []
    for uniq_key, fg_data in jo_fg_details.items():
        rm_details = jo_rm_details.get(uniq_key)
        fg_data_dict = deepcopy(fg_data)
        fg_data_dict['rm_details'] = rm_details
        final_dict[uniq_key] = fg_data_dict

    for uniq_key, fg_data in final_dict.items():
        grn_uniq_key = (uniq_key[0], uniq_key[1])
        if grn_uniq_key in data:
            data[grn_uniq_key].get("items").append(fg_data)
        else:
            grn_reference, grn_number = fg_data.get("grn_reference", ''), uniq_key[1]
            data[grn_uniq_key] = {
                "warehouse": warehouse.username,
                "jo_reference": uniq_key[0],
                "grn_number": grn_number,
                "grn_reference": grn_reference,
                "grn_display_key": grn_reference or grn_number,
                "order_type": status_dict.get(uniq_key[3], ''),
                "grn_quantity": fg_data.get("total_grn_quantity"),
                "jo_quantity": fg_data.get("total_jo_qty"),
                "label_id": extra_params.get("label_id",''),
                "grn_status": fg_data.get("status", ''),
                "items" : [fg_data]
            }
    for uniq_key, da in data.items():
        final_data_list.append(da)
    return final_data_list

def get_grn_date(in_month):
    now = datetime.datetime.now()
    months = [ "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" ]
    if in_month in months:
        temp_year = now.year
        if in_month == 'December':
            temp_year = temp_year -1
        c_date = calendar.monthrange(temp_year, months.index(in_month)+1)[1]
        c_month = months.index(in_month)+1
        c_year = temp_year
        final_date = "%s/%s/%s" % (c_month, c_date, c_year)
        return final_date

@get_warehouse
def get_grn_extra_fields(request , warehouse):
    extra_grn_fields = grn_extra_fields(warehouse)
    return HttpResponse(json.dumps({'data':extra_grn_fields }))

def grn_extra_fields(user):
    extra_grn_fields = []
    grn_field_obj = get_misc_value('grn_fields', user.id)
    if grn_field_obj != 'false':
        extra_grn_fields = grn_field_obj.split(',')
    return extra_grn_fields

@get_warehouse
def get_grn_rejection_reasons(request, warehouse):
    grn_rejection_reasons = []
    reasons_available = 0
    reasons = get_misc_value('grn_rejection_reasons', warehouse.id)
    if reasons != 'false':
        grn_rejection_reasons = reasons.split(',')
        if len(grn_rejection_reasons) > 0:
            if grn_rejection_reasons[0] != '':
                reasons_available = 1
    return HttpResponse(json.dumps({'grn_rejection_reasons': grn_rejection_reasons,'reasons_available': reasons_available,}))

def save_grn_extra_fields(warehouse, grn_number, extra_fields, transaction="grn"):
    '''Save grn extra fields in Pofields model '''
    transaction_types ={
        'grn' : 'grn_field',
        'asn' : 'asn_field',
        'rtv' : 'rtv_field'
    }
    attribute_type = "asn_attributes" if transaction == "asn" else "grn"
    if transaction == "rtv":
        attribute_type = "rtv"
    grn_extra_fields = {}
    grn_user_attributes = [grn_attr.get('attribute_name') for grn_attr in get_user_attributes(warehouse, attribute_type)]
    for field_key, value in extra_fields.items():
        if field_key.startswith('attr_'):
            field_key = field_key.split('attr_')[1]
        if field_key in grn_user_attributes:
            grn_extra_fields[field_key] = value
    #Save extra atributes
    save_or_update_po_extra_fields(warehouse.id, grn_extra_fields, receipt_no=grn_number, field_type=transaction_types[transaction])



def create_file_po_grn_mapping(request, warehouse, receipt_no, ref_number, grn_number, master_type="GRN_PO_NUMBER"):
    try:
        if ref_number and warehouse:
            file_obj = request.FILES.get('invoice_file', '')
            master_docs_obj = list(MasterDocs.objects.filter(
                master_id=ref_number, user=warehouse.id, master_type__in=['PO_TEMP', 'ASN_PO_NUMBER']))
            if master_docs_obj and not file_obj:
                for master_doc_obj in master_docs_obj:
                    master_doc_obj.master_id = grn_number
                    master_doc_obj.master_type = master_type
                MasterDocs.objects.bulk_update_with_rounding(master_docs_obj, ['master_id', 'master_type'])
            elif file_obj and not master_docs_obj:
                upload_master_file(request, warehouse, ref_number, master_type,
                        master_file=request.FILES['invoice_file'], extra_flag=receipt_no)
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info(generate_log_message("GRNFileUploadFailure",
                    warehouse_name = warehouse.username, grn_number =grn_number, error=str(e)))

def frame_sr_item_dict_data(sr_data, batch_no, batch_reference, mfg_date, exp_date, serial_dict):
    """Frame SR Item Dict Data"""
    sps_id = sr_data.get('sps_id', '')
    serial_data = serial_dict.get(sps_id, {}) or {}
    accepted_serial_numbers = serial_data.get('accepted', [])
    rejected_serial_numbers = serial_data.get('rejected', []) or serial_data.get('short', [])
    items_data = {
            "sku_code": sr_data.get('sku_code'),
            "sku_description": sr_data.get('sku_desc'), 
            "sales_return_quantity": sr_data.get('sales_return_quantity'),
            "grn_quantity": sr_data.get('grn_quantity',0),
            "buy_price": sr_data.get('buy_price'),
            "batch_number": batch_no,
            "batch_reference": batch_reference,
            "batch_display_key": batch_reference or batch_no,
            "manufactured_date": mfg_date,
            "expiry_date": exp_date,
            "vendor_batch_number": sr_data.get('vendor_batch_number', ''),
            "damaged_quantity": sr_data.get('sr_damaged_quantity', 0),
            "cancelled_quantity": sr_data.get('sr_cancelled_quantity', 0),
            "accepted_quantity": sr_data.get('accepted_quantity', 0),
            "rejected_quantity": sr_data.get('rejected_quantity', 0),
            "reason": sr_data.get('sr_reason', ''),
            "status": sr_data.get('grn_status', 0),
            "sr_line_aux_data" : sr_data.get('sr_line_aux_data', {}),
            "sr_batch_aux_data" : sr_data.get('sr_batch_aux_data', {}),
            "sales_uom_quantity": sr_data.get('sales_uom_quantity', 0),
            "put_zone": sr_data.get('grn_json_data',{}).get('zone_restriction',''),
            "uom": sr_data.get('uom', ''),
            "accepted_serial_numbers": accepted_serial_numbers,
            "rejected_serial_numbers": rejected_serial_numbers,
            "remarks": sr_data.get('remarks', ""),
            "pack_id": sr_data.get('pack_id', ''),
            "pack_uom_quantity": sr_data.get('pack_uom_quantity', 0),
        }
    if sr_data.get("remarks", '') == 'Shortage':
        items_data['rejected_quantity'] = sr_data.get('grn_quantity',0)
    return items_data

def frame_sr_data_dict(warehouse, data_dict, grn_uniq_key, grn_number, grn_reference, grn_date, items_data, sr_data, extra_params):

    return_status = 'return_received'
    if extra_params.get('sr_pending_grn_qty_dict', {}).get(sr_data.get('return_id'), {}):
        return_status = 'partially_received'

    data_dict[grn_uniq_key] = {
                "warehouse": warehouse.username,
                "sales_return_id": sr_data.get('return_id'),
                "sales_return_reference": sr_data.get("return_reference"),
                "reference_number": sr_data.get("sr_reference_number"),
                "return_status": return_status,
                "grn_number": grn_number,
                "grn_reference": grn_reference,
                "grn_display_key": grn_reference or grn_number,
                "grn_quantity": sr_data.get("grn_quantity",0),
                "label_id": extra_params.get("label_id",''),
                "grn_status": sr_data.get("grn_status", ''),
                "customer_id": sr_data.get('customer_id'),
                "customer_name": sr_data.get('customer_name'),
                "customer_reference": sr_data.get('customer_reference'),
                "customer_type": sr_data.get('customer_type'),
                "document_type": sr_data.get('sr_document_type'),
                "return_type": sr_data.get('sr_return_type'),
                "aux_data" : sr_data.get('sr_aux_data', {}),
                "grn_date": grn_date,
                "items" : [items_data],
                "invoice_reference": sr_data.get('invoice_number') or sr_data.get('challan_number'),
                "sales_return_quantity": sr_data.get('sales_return_quantity'),
                "credit_note_number": sr_data.get('credit_note_number'),
                }
    return data_dict

def frame_data_list(warehouse, sales_return_data, extra_params={}):
    """Frame Data List for Sales Return Data"""
    timezone = get_user_time_zone(warehouse)
    data_dict, final_data_list = {}, []
    sps_ids, return_ids = [], []
    for sr_data in sales_return_data:
        sps_ids.append(sr_data.get('sps_id'))
        return_ids.append(sr_data.get('return_id'))

    # Fetch Pending GRN Quantity for SR
    sr_pending_grn_qty_dict = dict(SalesReturnBatchLevel.objects.filter(sales_return_sku__sales_return__return_id__in=return_ids, sales_return_sku__sales_return__warehouse=warehouse).values_list('sales_return_sku__sales_return__return_id').annotate(Sum('quantity')))
    extra_params['sr_pending_grn_qty_dict'] = sr_pending_grn_qty_dict

    #Fetch SerialNumbers for each sps id
    serial_filter = {'filters': {'reference_type': 'grn', 'transact_id__in': sps_ids}}
    serial_mixin = SerialNumberTransactionMixin(warehouse, warehouse, serial_filter)
    existing_serials = serial_mixin.get_sntd_details()
    serial_dict = {}
    for data in existing_serials.get('data', []):
        serial_dict.setdefault(data['transact_id'], {'accepted': [], 'rejected': [], 'short': []})[data['transact_type']].extend(data['serial_numbers'])

    for sr_data in sales_return_data:
        grn_uniq_key = (sr_data.get('sr_grn_number'))

        mfg_date, exp_date = sr_data.get('manufactured_date',''), sr_data.get('expiry_date','')
        if mfg_date:
            mfg_date = format_date(timezone, mfg_date)
        if exp_date:
            exp_date = format_date(timezone, exp_date)
        batch_no, batch_reference = sr_data.get('batch_no',''), sr_data.get('batch_reference')
        #Frame Items dict
        items_data = frame_sr_item_dict_data(sr_data, batch_no, batch_reference, mfg_date, exp_date, serial_dict)

        if grn_uniq_key in data_dict.keys():
            data_dict[grn_uniq_key]['grn_quantity'] = data_dict[grn_uniq_key].get('grn_quantity', 0) + sr_data.get('grn_quantity', 0)
            data_dict[grn_uniq_key]['sales_return_quantity'] = data_dict[grn_uniq_key].get('sales_return_quantity', 0) + sr_data.get('sales_return_quantity', 0)
            data_dict[grn_uniq_key].get("items").append(items_data)
        else:
            grn_date = get_local_date_known_timezone(timezone, sr_data.get('grn_date'), send_date=True).strftime(DEFAULT_DATETIME_FORMAT)
            grn_reference, grn_number = sr_data.get("sr_grn_reference", ''), sr_data.get('sr_grn_number')

            #Frame Final Data Dict
            data_dict = frame_sr_data_dict(warehouse, data_dict, grn_uniq_key, grn_number, grn_reference, grn_date, items_data, sr_data, extra_params)
    for _, data in data_dict.items():
        final_data_list.append(data)  

    return final_data_list


def get_grn_sku_wise_sr_data(warehouse, grn_numbers, add_data={}, return_first_item=False):

    extra_data = {
        'return_type': 'values',
    }
    sales_return_data = get_sr_grn_data_with_grn_number(grn_numbers, warehouse, extra_data)
    data_list = frame_data_list(warehouse, sales_return_data, add_data)

    return data_list[0] if return_first_item else data_list


def get_grn_numbers_list(warehouse, request_data):

    filters = {}
    message = ''
    q_filters = []
    excludes = {}

    filters['user__id'] = warehouse.id
    request_data.pop('warehouse', None)

    grn_numbers = []
    GRN_FILTERS = {            
        'PO' : ['supplier_id', 'po_number', 'asn_number', 'po_reference'],
        'SR' : ['return_id', 'return_reference', 'customer_id', 'customer_reference', 'customer_type'],
    }   
    filter_keys = [
        'grn_number', 'grn_reference', 'grn_type',
        'batch_number','batch_reference', 'sku_code',
        'creation_date__gt', 'creation_date__lt', 'jo_reference'
    ]
    grn_type = request_data.get('grn_type', 'PO')
    for key, value in request_data.items():
        if key in ['limit', 'offset', 'falt']:
            continue
        elif grn_type and key in GRN_FILTERS.get(grn_type,[]):
            filters[key] = value
        elif key in filter_keys:
            filters[key] = value
        elif key == 'grn_display_key':
            q_filters = [{'grn_number': value, 'grn_reference': value}]
        elif key == 'label_print':
            excludes = {'remarks': 'Shortage'}
        else:
            message = f'Invalid Filter {key}'
    extra_params = {
        'filters' : filters, 'order_by' : ['grn_number'],
        'distincts' : ['grn_number'],
        'value_keys' : ['grn_number'], 'return_type' : 'values_dict',
        'q_filters': q_filters, 'excludes': excludes
    }

    if grn_type == 'SR':
        usage = 'sr_grn'
    elif grn_type == 'job_order':
        usage = 'jo_grn'
    else:
        usage = 'grn'
        extra_params['value_keys'].append('asn_number')

    if not message:
        grn_numbers = get_data_from_custom_params(SellerPOSummary, usage, extra_params)

    return message, grn_numbers

@get_warehouse
def get_tax_for_based_on_config(request, warehouse:User):
    log.info(generate_log_message("GetTaxBasedOnConfig",
                                          request_user= str(request.user.username),
                                          warehouse = str))
    data = request.GET
    sku_code = data.get('sku_code')
    supplier_id = data.get('supplier_id', '')
    po_number = data.get('po_id')
    misc_data = get_misc_value('grn_tax_source', warehouse.id)
    tax_data = calculate_tax_based_on_config(sku_code, supplier_id, po_number, misc_data, warehouse)
    return JsonResponse({'tax_data': tax_data})

def calculate_tax_based_on_config(sku_code, supplier_id, po_id, misc_data, warehouse, sku_tax_type=None, supplier_tax_type=None):
    supplier_tax_mapping_dict = {1: "inter_state", 0: "intra_state"}
    supplier_tax_reverse_mapping = {"inter_state": 1, "intra_state": 0}
    # Get or cache SKU tax type
    if not sku_tax_type:
        sku_tax_type = SKUMaster.objects.filter(sku_code=sku_code, user = warehouse.id).values_list('product_type', flat=True).first()
    if misc_data == 'sku_master':
        if not supplier_tax_type:
            supplier_tax_type = SupplierMaster.objects.filter(supplier_id=supplier_id, user = warehouse.id).values_list('tax_type', flat=True).first()
        
        if sku_tax_type and supplier_tax_type:
            tax_data = TaxMaster.objects.filter(
                product_type=sku_tax_type,
                inter_state=supplier_tax_reverse_mapping.get(supplier_tax_type, 0),
                user=warehouse.id
            ).annotate(
                total_tax=F("cgst_tax") + F("sgst_tax") + F("igst_tax") + F("utgst_tax") + F("apmc_tax"),
                total_cess_tax=F("cess_tax")
            ).values(
                'inter_state', 'total_tax', 'total_cess_tax', 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'apmc_tax'
            ).first()
            
            if tax_data and supplier_tax_mapping_dict.get(tax_data['inter_state']) == supplier_tax_type:
                return tax_data

        return {"total_tax": 0, "total_cess_tax": 0}
    
    else:
        tax_data = PurchaseOrder.objects.filter(id=po_id).annotate(
            total_tax=F('open_po__cgst_tax') + F('open_po__sgst_tax') + F('open_po__igst_tax') + F('open_po__utgst_tax') + F('open_po__apmc_tax'),
            total_cess_tax=F('open_po__cess_tax'),
            cgst_tax=F('open_po__cgst_tax'),
            sgst_tax=F('open_po__sgst_tax'),
            igst_tax=F('open_po__igst_tax'),
            utgst_tax=F('open_po__utgst_tax'),
            apmc_tax=F('open_po__apmc_tax')
        ).values(
            'open_po__supplier__tax_type', 'total_tax', 'total_cess_tax', 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax',
              'apmc_tax'
        ).first()
        
        return tax_data if tax_data else {}