# Generated by Django 4.2.20 on 2025-05-29 06:47

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0065_costdimensions_costdimensionmaster'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inbound', '0059_historicalpolocation_data_stage_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='putawaymapping',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='putawaymapping',
            name='sku_class',
            field=models.CharField(default='', max_length=64),
        ),
        migrations.AlterUniqueTogether(
            name='putawaymapping',
            unique_together={('transaction_type', 'quantity_type', 'process_type', 'reason', 'entity_name', 'warehouse', 'strategy', 'zone', 'location', 'status', 'sku_class')},
        ),
    ]
