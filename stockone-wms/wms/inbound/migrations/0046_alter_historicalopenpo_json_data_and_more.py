# Generated by Django 4.2.20 on 2025-04-28 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0045_poheader'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='historicalopenpo',
            name='json_data',
            field=models.JSO<PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalpendinglineitems',
            name='json_data',
            field=models.JSO<PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalpendingpo',
            name='json_data',
            field=models.JSO<PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalreturntovendor',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='openpo',
            name='json_data',
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='pendinglineitems',
            name='json_data',
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='pendingpo',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='returntovendor',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
    ]
