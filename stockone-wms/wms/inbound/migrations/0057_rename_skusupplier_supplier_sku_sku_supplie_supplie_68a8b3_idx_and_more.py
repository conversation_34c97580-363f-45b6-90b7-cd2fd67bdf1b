# Generated by Django 4.2.20 on 2025-05-27 00:41

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0056_rename_skusupplier_supplier_sku_sku_supplie_supplie_68a8b3_idx_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."SKU_SUPPLIER_MAPPING_user_9af197c8"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "SKU_SUPPLIER_MAPPING_user_9af197c8"
                ON public."SKU_SUPPLIER_MAPPING" USING btree
                ("user" ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
