# Generated by Django 4.2.20 on 2025-05-27 00:08

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0052_alter_supplierattributes_attribute_name'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."SUPPLIER_ATTRIBUTES_supplier_id_af75c5a1"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "SUPPLIER_ATTRIBUTES_supplier_id_af75c5a1"
                ON public."SUPPLIER_ATTRIBUTES" USING btree
                (supplier_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
