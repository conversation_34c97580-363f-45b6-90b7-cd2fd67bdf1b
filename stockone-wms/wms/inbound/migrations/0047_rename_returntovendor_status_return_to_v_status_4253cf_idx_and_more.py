# Generated by Django 4.2.20 on 2025-05-26 23:33

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0046_alter_historicalopenpo_json_data_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."RETURN_TO_VENDOR_seller_po_summary_id_83488656"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "RETURN_TO_VENDOR_seller_po_summary_id_83488656"
                ON public."RETURN_TO_VENDOR" USING btree
                (seller_po_summary_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
        
    ]
