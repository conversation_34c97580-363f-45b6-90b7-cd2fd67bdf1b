# Generated by Django 4.2.20 on 2025-05-27 00:39

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0055_supplierattributes_supplier_at_supplie_40c546_idx'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."SKU_SUPPLIER_MAPPING_sku_id_f5f7d82a"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "SKU_SUPPLIER_MAPPING_sku_id_f5f7d82a"
                ON public."SKU_SUPPLIER_MAPPING" USING btree
                (sku_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
