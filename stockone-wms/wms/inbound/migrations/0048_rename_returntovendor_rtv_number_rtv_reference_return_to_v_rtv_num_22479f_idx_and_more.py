# Generated by Django 4.2.20 on 2025-05-26 23:37

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0047_rename_returntovendor_status_return_to_v_status_4253cf_idx_and_more'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='returntovendor',
            new_name='RETURN_TO_V_rtv_num_22479f_idx',
            old_fields=('rtv_number', 'rtv_reference'),
        ),
        migrations.RenameIndex(
            model_name='returntovendor',
            new_name='RETURN_TO_V_status_4253cf_idx',
            old_fields=('status',),
        ),
        migrations.AlterIndexTogether(
            name='returntovendor',
            index_together=set(),
        ),
    ]
