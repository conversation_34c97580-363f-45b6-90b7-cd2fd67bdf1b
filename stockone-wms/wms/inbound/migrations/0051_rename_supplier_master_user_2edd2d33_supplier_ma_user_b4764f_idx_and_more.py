# Generated by Django 4.2.20 on 2025-05-26 23:58

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0050_rename_suppliermaster_name_supplier_ma_name_eb422a_idx_and_more'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='suppliermaster',
            new_name='SUPPLIER_MA_user_b4764f_idx',
            old_name='SUPPLIER_MASTER_user_2edd2d33',
        ),
        migrations.RenameIndex(
            model_name='suppliermaster',
            new_name='SUPPLIER_MA_name_eb422a_idx',
            old_name='SUPPLIER_MASTER_name_8be8423c',
        ),
        migrations.RenameIndex(
            model_name='suppliermaster',
            new_name='SUPPLIER_MA_supplie_6ffb13_idx',
            old_fields=('supplier_id', 'user'),
        ),
        migrations.RenameIndex(
            model_name='suppliermaster',
            new_name='SUPPLIER_MA_name_eb422a_idx',
            old_fields=('name',),
        ),
        migrations.RenameIndex(
            model_name='suppliermaster',
            new_name='SUPPLIER_MA_user_b4764f_idx',
            old_fields=('user',),
        ),
        migrations.RenameIndex(
            model_name='suppliermaster',
            new_name='SUPPLIER_MA_name_54ec2a_idx',
            old_fields=('name', 'user'),
        ),
    ]
