# Generated by Django 4.2.16 on 2025-04-02 06:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0122_masterheadersview_multi_warehouse_stock_detail_master_and_more'),
    ]

    operations = [
        migrations.AlterIndexTogether(
            name='gatepassitem',
            index_together=set(),
        ),
        migrations.AddField(
            model_name='gatepassitem',
            name='handling_unit',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AddField(
            model_name='gatepassitem',
            name='warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='booking_detail_master',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.Alter<PERSON>ield(
            model_name='gatepass',
            name='gate_pass_sub_type',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='gatepass',
            name='gate_pass_type',
            field=models.CharField(choices=[('outbound', 'Outbound'), ('inbound', 'Inbound')], max_length=100),
        ),
        migrations.AlterIndexTogether(
            name='gatepassitem',
            index_together={('warehouse', 'transaction_id', 'transaction_type', 'status'), ('warehouse', 'transaction_id', 'transaction_type'), ('warehouse', 'gate_pass')},
        ),
    ]
