# Generated by Django 4.2.20 on 2025-04-28 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0126_alter_userintegrationapis_access_token'),
    ]

    operations = [
        migrations.AlterField(
            model_name='crontabschedule',
            name='input_params',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='spoketriggersdetail',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='wavecriteria',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
    ]
