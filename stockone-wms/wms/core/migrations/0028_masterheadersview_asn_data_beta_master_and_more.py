# Generated by Django 4.2 on 2023-06-13 13:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0027_masterheadersview'),
    ]

    operations = [
        migrations.AddField(
            model_name='masterheadersview',
            name='asn_data_beta_master',
            field=models.JSO<PERSON>ield(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='asn_data_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='ba_to_sa_detail_master',
            field=models.J<PERSON><PERSON>ield(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='batch_level_stock_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='cancelled_invoices_master',
            field=models.JSONField(default={}),
        ),
        migrations.Add<PERSON>ield(
            model_name='masterheadersview',
            name='confirm_cycle_count_master',
            field=models.J<PERSON><PERSON>ield(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='created_rtv_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='customer_invoices_tab_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='customer_order_view_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='cycle_count_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='delivery_challans_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='fore_cast_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='inventory_adjustment_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='invoiced_shipment_info_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='invoiced_shipment_picked_invoice_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='jo_quality_control_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='job_order_open_orders_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='manifest_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='material_requirement_planning_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='min_max_material_planning_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='move_inventory_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='open_orders_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='outbound_back_orders_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='po_putaway_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='po_quality_control_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='processed_orders_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='pull_to_locate_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='putaway_confirmation_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='raise_job_order_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='raise_po_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='raw_material_picklist_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='receive_jo_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='receive_po_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='reecive_po_beta_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='return_to_vendor_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='rtv_shipment_info_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='rtv_shipment_picked_invoice_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='sales_return_grn_beta_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='sales_return_grn_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='sales_return_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='scheduled_cycle_count_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='sku_pack_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='sr_putaway_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='sr_quality_control_master',
            field=models.JSONField(default={}),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='stock_transfer_invoice_tab_master',
            field=models.JSONField(default={}),
        ),
    ]
