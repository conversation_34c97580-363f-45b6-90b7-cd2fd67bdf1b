# Generated by Django 4.2.16 on 2024-10-29 08:11

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0107_masterheadersview_staging_routing'),
    ]

    operations = [
        migrations.AddField(
            model_name='incrementaltable',
            name='delimiter',
            field=models.CharField(default='', max_length=64),
        ),
        migrations.AddField(
            model_name='incrementaltable',
            name='suffix',
            field=models.CharField(default='', max_length=64),
        ),
        migrations.AlterIndexTogether(
            name='incrementaltable',
            index_together={('user', 'type_name')},
        ),
    ]
