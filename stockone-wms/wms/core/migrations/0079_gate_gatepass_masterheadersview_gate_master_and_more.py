# Generated by Django 4.2.10 on 2024-06-05 04:36

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils

related_name_const = '%(class)ss_account'
wms_base_const = 'wms_base.userprofile'


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0027_companymaster_json_data_and_more'),
        ('core', '0078_alter_tempjson_unique_together'),
    ]

    operations = [
        migrations.CreateModel(
            name='Gate',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('gate_id', models.CharField(blank=True, default='', max_length=100)),
                ('gate_name', models.CharField(blank=True, default='', max_length=100)),
                ('gate_type', models.CharField(choices=[('ENTRY', 'Entry'), ('EXIT', 'Exit'), ('BOTH', 'Both')], default='BOTH', max_length=5)),
                ('status', models.IntegerField(default=0)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name=related_name_const, to=wms_base_const)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'GATE',
                'unique_together': {('warehouse', 'gate_name')},
                'index_together': {('warehouse', 'gate_name'), ('warehouse', 'gate_name', 'gate_type'), ('warehouse', 'gate_type')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='GatePass',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('gate_pass_id', models.CharField(blank=True, default='', max_length=100)),
                ('gate_pass_type', models.CharField(choices=[('outbound', 'Outbound'), ('inbound', 'Inbound')], max_length=10)),
                ('gate_pass_sub_type', models.CharField(blank=True, max_length=20, null=True)),
                ('lr_no', models.CharField(blank=True, max_length=100, null=True)),
                ('lr_date', models.DateTimeField(blank=True, null=True)),
                ('transporter', models.CharField(blank=True, max_length=100, null=True)),
                ('vehicle_no', models.CharField(blank=True, max_length=20, null=True)),
                ('driver_name', models.CharField(blank=True, max_length=100, null=True)),
                ('driver_mob_no', models.CharField(blank=True, help_text='Mobile number of the driver.', max_length=10, null=True, validators=[django.core.validators.RegexValidator(message='Driver mobile number must be 10 digits', regex='^\\d{10}$')])),
                ('status', models.IntegerField(default=0, help_text='Status of the gate pass (0: created, 1: gate in, 2: gate out).')),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name=related_name_const, to=wms_base_const)),
                ('gate', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.gate')),
                ('warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'GATE_PASS',
                'index_together': {('warehouse', 'gate', 'status'), ('warehouse', 'status'), ('warehouse', 'gate')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.AddField(
            model_name='masterheadersview',
            name='gate_master',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.CreateModel(
            name='GatePassItem',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('transaction_id', models.CharField(blank=True, default='', max_length=100)),
                ('transaction_type', models.CharField(blank=True, default='', max_length=100)),
                ('status', models.IntegerField(default=0)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name=related_name_const, to=wms_base_const)),
                ('gate_pass', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.gatepass')),
            ],
            options={
                'db_table': 'GATE_PASS_ITEM',
                'index_together': {('transaction_id', 'transaction_type'), ('transaction_id', 'transaction_type', 'status')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
