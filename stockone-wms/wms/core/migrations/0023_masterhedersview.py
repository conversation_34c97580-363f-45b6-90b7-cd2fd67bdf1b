# Generated by Django 4.2 on 2023-06-06 08:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0022_rename_name_formfieldvalue_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='MasterHedersView',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('sku_master', models.J<PERSON><PERSON><PERSON>(default={})),
                ('location_master', models.JSO<PERSON>ield(default={})),
                ('zone_master', models.JSONField(default={})),
                ('supplier_master', models.J<PERSON><PERSON>ield(default={})),
                ('lpn_master', models.<PERSON><PERSON><PERSON><PERSON>(default={})),
                ('supplier_sku_mapping_master', models.<PERSON><PERSON><PERSON><PERSON>(default={})),
                ('inventory_replenishment_master', models.<PERSON><PERSON><PERSON><PERSON>(default={})),
                ('min_max_replenishment_master', models.JSONField(default={})),
                ('customer_master', models.JSONField(default={})),
                ('customer_sku_mapping_master', models.JSONField(default={})),
                ('bom_master', models.JSONField(default={})),
                ('pricing_master', models.JSONField(default={})),
                ('tax_master', models.JSONField(default={})),
                ('user_access_control_master', models.JSONField(default={})),
                ('order_type_zone_mapping_master', models.JSONField(default={})),
                ('currency_exchange_master', models.JSONField(default={})),
                ('po_approval_config_master', models.JSONField(default={})),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'MASTER_HEADERS_VIEW',
            },
        ),
    ]
