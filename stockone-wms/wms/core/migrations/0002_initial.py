# Generated by Django 4.2 on 2023-05-06 07:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0001_initial'),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='skumaster',
            name='zone',
            field=wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.zonemaster'),
        ),
        migrations.AddField(
            model_name='skuimages',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='skuimages',
            name='sku',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.skumaster'),
        ),
        migrations.AddField(
            model_name='skugroups',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='skuattributes',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='skuattributes',
            name='sku',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.skumaster'),
        ),
        migrations.AddField(
            model_name='sizemaster',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='serialnumbertransactionmapping',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='serialnumbertransactionmapping',
            name='serial',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.serialnumbermapping'),
        ),
        migrations.AddField(
            model_name='serialnumbertransactionmapping',
            name='stock',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.stockdetail'),
        ),
        migrations.AddField(
            model_name='serialnumbertransactionmapping',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='serialnumbermapping',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='serialnumbermapping',
            name='batch_detail',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.batchdetail'),
        ),
        migrations.AddField(
            model_name='serialnumbermapping',
            name='carton',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.authorizedbins'),
        ),
        migrations.AddField(
            model_name='serialnumbermapping',
            name='sku',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.skumaster'),
        ),
        migrations.AddField(
            model_name='serialnumbermapping',
            name='stock',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.stockdetail'),
        ),
        migrations.AddField(
            model_name='serialnumbermapping',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='palletdetail',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='miscdetailoptions',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='miscdetailoptions',
            name='misc_detail',
            field=wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.miscdetail'),
        ),
        migrations.AddField(
            model_name='miscdetail',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='masterdocs',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='masterdocs',
            name='user',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='invoiceforms',
            name='warehouse_id',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='incrementaltable',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='incrementaltable',
            name='user',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='hsnmaster',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='hsnmaster',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='eannumbers',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='eannumbers',
            name='sku',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.skumaster'),
        ),
        migrations.AddField(
            model_name='currencyexchangemaster',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='currencyexchangemaster',
            name='created_by',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='currencyexchangemaster',
            name='updated_by',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='currencyexchangemaster',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='cartontypes',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='cartontypes',
            name='user',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='barcodetemplate',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='barcodesettings',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='barcodesettings',
            name='user',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='barcodeentities',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='barcodeentities',
            name='template',
            field=wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.barcodetemplate'),
        ),
        migrations.AddField(
            model_name='authorizedbins',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='authorizedbins',
            name='carton_types',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.cartontypes'),
        ),
        migrations.AddField(
            model_name='authorizedbins',
            name='user',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='addressmaster',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='addressmaster',
            name='user',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='actiontriggertostaff',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='actiontriggertostaff',
            name='actiontrigger',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.actiontrigger'),
        ),
        migrations.AddField(
            model_name='actiontriggertostaff',
            name='staffmaster',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wms_base.staffmaster'),
        ),
        migrations.AddField(
            model_name='actiontrigger',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
        ),
        migrations.AddField(
            model_name='actiontrigger',
            name='staff',
            field=models.ManyToManyField(blank=True, null=True, related_name='action_trigger_to_staff', through='core.ActionTriggerToStaff', to='wms_base.staffmaster'),
        ),
        migrations.AddField(
            model_name='actiontrigger',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='uommaster',
            unique_together={('company', 'sku_code', 'base_uom', 'uom_type', 'uom', 'conversion')},
        ),
        migrations.AlterUniqueTogether(
            name='uomdetail',
            unique_together={('company', 'uom_code', 'status')},
        ),
        migrations.AlterIndexTogether(
            name='taxmaster',
            index_together={('user', 'product_type'), ('user',), ('cgst_tax', 'sgst_tax', 'igst_tax', 'cess_tax', 'user'), ('user', 'product_type', 'inter_state')},
        ),
        migrations.AlterUniqueTogether(
            name='skurelation',
            unique_together={('parent_sku', 'member_sku', 'relation_type')},
        ),
        migrations.AlterIndexTogether(
            name='skurelation',
            index_together={('parent_sku', 'relation_type'), ('parent_sku', 'member_sku', 'relation_type'), ('parent_sku', 'member_sku')},
        ),
        migrations.AlterUniqueTogether(
            name='skumaster',
            unique_together={('user', 'sku_code', 'wms_code')},
        ),
        migrations.AlterIndexTogether(
            name='skumaster',
            index_together={('user', 'sku_code', 'wms_code'), ('user', 'sku_code'), ('user',), ('sku_code',)},
        ),
        migrations.AlterUniqueTogether(
            name='skuimages',
            unique_together={('sku', 'image_url')},
        ),
        migrations.AlterUniqueTogether(
            name='skugroups',
            unique_together={('user', 'group')},
        ),
        migrations.AlterIndexTogether(
            name='skuattributes',
            index_together={('sku', 'attribute_name'), ('sku',), ('attribute_name', 'attribute_value')},
        ),
        migrations.AlterUniqueTogether(
            name='sizemaster',
            unique_together={('user', 'size_name')},
        ),
        migrations.AlterIndexTogether(
            name='serialnumbertransactionmapping',
            index_together={('warehouse',), ('warehouse', 'serial'), ('warehouse', 'reference_number', 'reference_type'), ('warehouse', 'serial', 'reference_number')},
        ),
        migrations.AddConstraint(
            model_name='serialnumbermapping',
            constraint=models.UniqueConstraint(condition=models.Q(('status', 1)), fields=('warehouse', 'serial_number', 'sku', 'stock', 'status'), name='Unique Active Serial Number Mapping'),
        ),
        migrations.AlterIndexTogether(
            name='serialnumbermapping',
            index_together={('warehouse',), ('warehouse', 'status'), ('warehouse', 'status', 'sku'), ('warehouse', 'serial_number')},
        ),
        migrations.AlterIndexTogether(
            name='palletdetail',
            index_together={('user', 'pallet_code', 'quantity'), ('user', 'pallet_code')},
        ),
        migrations.AlterUniqueTogether(
            name='miscdetailoptions',
            unique_together={('misc_detail', 'misc_key', 'misc_value', 'status')},
        ),
        migrations.AlterUniqueTogether(
            name='miscdetail',
            unique_together={('user', 'misc_type')},
        ),
        migrations.AlterIndexTogether(
            name='miscdetail',
            index_together={('user', 'misc_type')},
        ),
        migrations.AlterIndexTogether(
            name='masterdocs',
            index_together={('master_id', 'master_type', 'uploaded_file'), ('user',), ('user', 'master_id', 'master_type', 'extra_flag'), ('master_type',), ('master_id',)},
        ),
        migrations.AlterUniqueTogether(
            name='invoiceforms',
            unique_together={('warehouse_id', 'invoice_format')},
        ),
        migrations.AlterIndexTogether(
            name='invoiceforms',
            index_together={('warehouse_id',), ('warehouse_id', 'invoice_format')},
        ),
        migrations.AlterUniqueTogether(
            name='hsnmaster',
            unique_together={('warehouse', 'hsn_code')},
        ),
        migrations.AlterIndexTogether(
            name='hsnmaster',
            index_together={('warehouse', 'hsn_code')},
        ),
        migrations.AlterUniqueTogether(
            name='eannumbers',
            unique_together={('ean_number', 'sku')},
        ),
        migrations.AlterIndexTogether(
            name='eannumbers',
            index_together={('sku', 'ean_number'), ('ean_number',)},
        ),
        migrations.AlterIndexTogether(
            name='currencyexchangemaster',
            index_together={('warehouse',), ('warehouse', 'from_currency', 'to_currency')},
        ),
        migrations.AlterUniqueTogether(
            name='cartontypes',
            unique_together={('area', 'carton_type', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='barcodesettings',
            unique_together={('user', 'format_type')},
        ),
        migrations.AlterIndexTogether(
            name='authorizedbins',
            index_together={('bin_number', 'status', 'user'), ('bin_number', 'user'), ('user',)},
        ),
        migrations.AlterUniqueTogether(
            name='addressmaster',
            unique_together={('id', 'account_id')},
        ),
        migrations.AlterUniqueTogether(
            name='actiontrigger',
            unique_together={('warehouse', 'action_type')},
        ),
    ]
