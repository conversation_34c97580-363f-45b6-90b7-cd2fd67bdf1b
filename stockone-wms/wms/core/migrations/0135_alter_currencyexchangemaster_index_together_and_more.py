# Generated by Django 4.2.20 on 2025-05-07 07:39

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0134_alter_authorizedbins_index_together_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."CURRENCY_EXCHANGE_MASTER_warehouse_id_b7e08056"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "CURRENCY_EXCHANGE_MASTER_warehouse_id_b7e08056"
                ON public."CURRENCY_EXCHANGE_MASTER" USING btree
                (warehouse_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
