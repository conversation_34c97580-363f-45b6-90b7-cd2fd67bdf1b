# Generated by Django 4.2 on 2023-05-06 09:21

from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0001_initial'),
        ('core', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SKUFields',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('field_id', models.PositiveIntegerField(default=0)),
                ('field_type', models.CharField(default='', max_length=128)),
                ('field_value', models.CharField(default='', max_length=128)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('sku', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.skumaster')),
            ],
            options={
                'db_table': 'SKU_FIELDS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
