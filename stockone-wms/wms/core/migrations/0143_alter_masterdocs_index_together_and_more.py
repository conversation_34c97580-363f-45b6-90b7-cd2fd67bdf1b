# Generated by Django 4.2.20 on 2025-05-07 09:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0142_alter_masterdocs_index_together_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."TAX_MASTER_user_id_23002c95"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "TAX_MASTER_user_id_23002c95"
                ON public."TAX_MASTER" USING btree
                (user_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
