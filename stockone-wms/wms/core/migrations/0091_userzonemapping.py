# Generated by Django 4.2.10 on 2024-07-30 02:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0027_companymaster_json_data_and_more'),
        ('inventory', '0047_zonemaster_get_sequence_zonemaster_put_sequence'),
        ('core', '0090_merge_20240724_0721'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserZoneMapping',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='zone_mapping_user', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('zone', models.ManyToManyField(blank=True, null=True, to='inventory.zonemaster')),
            ],
            options={
                'db_table': 'USER_ZONE_MAPPING',
                'unique_together': {('user', 'warehouse')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
