# Generated by Django 4.2 on 2023-05-10 07:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0004_remove_usergroups_account'),
        ('core', '0007_skumaster_zone'),
        ('inbound', '0005_discrepancy_pofields_delete_purchaseapprovalconfig')
    ]

    operations = [
        migrations.CreateModel(
            name='CrontabSchedule',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('type', models.CharField(max_length=50)),
                ('name', models.CharField(max_length=150)),
                ('description', models.TextField(blank=True, default='', null=True)),
                ('active', models.Bo<PERSON>an<PERSON>ield(default=True)),
                ('crontab', models.CharField(max_length=1000)),
                ('input_params', models.JSONField(blank=True, default={}, null=True)),
                ('function_name', models.Char<PERSON>ield(blank=True, max_length=126, null=True)),
                ('last_eval_dttm', models.DateTimeField(blank=True, null=True)),
                ('last_state', models.CharField(blank=True, max_length=32, null=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('timezone', models.CharField(default='UTC', max_length=100)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
            ],
            options={
                'db_table': 'CRONTAB_SCHEDULE',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='PurchaseApprovalConfig',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(default='', max_length=64)),
                ('display_name', models.CharField(default='', max_length=64)),
                ('min_Amt', models.FloatField(default=0)),
                ('max_Amt', models.FloatField(default=0)),
                ('level', models.CharField(default='', max_length=64)),
                ('approval_type', models.CharField(default='', max_length=32)),
                ('purchase_type', models.CharField(default='PO', max_length=32)),
                ('po_type', models.CharField(default='', max_length=64)),
                ('product_category', models.CharField(default='', max_length=64)),
                ('sku_category', models.CharField(default='', max_length=64)),
                ('department_type', models.CharField(default='', max_length=64)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('company', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='wms_base.companymaster')),
                ('user', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('user_role', models.ManyToManyField(default=None, to='wms_base.companyroles')),
            ],
            options={
                'db_table': 'PURCHASE_APPROVAL_CONFIG',
                'unique_together': {('user', 'name', 'level', 'min_Amt', 'max_Amt', 'approval_type')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
