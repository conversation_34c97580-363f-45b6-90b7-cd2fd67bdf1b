# Generated by Django 4.2.10 on 2024-04-04 18:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0025_warehouseconfig'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0073_merge_20240403_0409'),
    ]

    operations = [
        migrations.CreateModel(
            name='SKUWAC',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('average_price', models.FloatField(default=0)),
                ('average_price_rt', models.FloatField(default=0)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('sku', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.skumaster')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'SKU_WAC',
                'unique_together': {('warehouse', 'sku')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
