# Generated by Django 4.2.16 on 2025-02-26 11:01

from django.db import migrations, models
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('inbound', '0045_poheader'),
        ('core', '0116_alter_userintegrationcalls_retry_counter'),
    ]

    operations = [
        migrations.AddField(
            model_name='skumaster',
            name='pick_and_sort',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='skumaster',
            name='seller_master',
            field=wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='inbound.suppliermaster'),
        ),
    ]
