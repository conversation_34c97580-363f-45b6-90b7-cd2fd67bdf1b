# Generated by Django 4.2.20 on 2025-05-07 05:21

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0131_rename_booking_detail_master_masterheadersview_appointment_detail_master'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='authorizedbins',
            new_name='AUTHORIZED__bin_num_ae11fb_idx',
            old_fields=('bin_number', 'user'),
        ),
        migrations.RenameIndex(
            model_name='authorizedbins',
            new_name='AUTHORIZED__bin_num_68abb2_idx',
            old_fields=('bin_number', 'status', 'user'),
        ),
        migrations.RenameIndex(
            model_name='currencyexchangemaster',
            new_name='CURRENCY_EX_warehou_205fe0_idx',
            old_fields=('warehouse', 'from_currency', 'to_currency'),
        ),
        migrations.RenameIndex(
            model_name='eannumbers',
            new_name='EAN_NUMBERS_ean_num_f596b4_idx',
            old_fields=('ean_number',),
        ),
        migrations.RenameIndex(
            model_name='eannumbers',
            new_name='EAN_NUMBERS_sku_id_18f266_idx',
            old_fields=('sku', 'ean_number'),
        ),
        migrations.RenameIndex(
            model_name='gate',
            new_name='GATE_warehou_539f43_idx',
            old_fields=('warehouse', 'gate_name', 'gate_type'),
        ),
        migrations.RenameIndex(
            model_name='gate',
            new_name='GATE_warehou_fd4011_idx',
            old_fields=('warehouse', 'gate_name'),
        ),
        migrations.RenameIndex(
            model_name='gate',
            new_name='GATE_warehou_21fcb1_idx',
            old_fields=('warehouse', 'gate_type'),
        ),
        migrations.RenameIndex(
            model_name='gatepass',
            new_name='GATE_PASS_warehou_9e66e4_idx',
            old_fields=('warehouse', 'status'),
        ),
        migrations.RenameIndex(
            model_name='gatepass',
            new_name='GATE_PASS_warehou_35f498_idx',
            old_fields=('warehouse', 'gate'),
        ),
        migrations.RenameIndex(
            model_name='gatepass',
            new_name='GATE_PASS_warehou_c79b09_idx',
            old_fields=('warehouse', 'gate', 'status'),
        ),
        migrations.RenameIndex(
            model_name='gatepassitem',
            new_name='GATE_PASS_I_warehou_614748_idx',
            old_fields=('warehouse', 'transaction_id', 'transaction_type'),
        ),
        migrations.RenameIndex(
            model_name='gatepassitem',
            new_name='GATE_PASS_I_warehou_b5f959_idx',
            old_fields=('warehouse', 'transaction_id', 'transaction_type', 'status'),
        ),
        migrations.RenameIndex(
            model_name='gatepassitem',
            new_name='GATE_PASS_I_warehou_0f0e4a_idx',
            old_fields=('warehouse', 'gate_pass'),
        ),
        migrations.RenameIndex(
            model_name='hsnmaster',
            new_name='HSN_MASTER_warehou_ee6e2d_idx',
            old_fields=('warehouse', 'hsn_code'),
        ),
        migrations.RenameIndex(
            model_name='hubspokedetail',
            new_name='HUB_SPOKE_D_hub_id_c99c5c_idx',
            old_fields=('hub', 'status'),
        ),
        migrations.RenameIndex(
            model_name='hubspokedetail',
            new_name='HUB_SPOKE_D_hub_id_23ee4b_idx',
            old_fields=('hub', 'spoke', 'status'),
        ),
        migrations.RenameIndex(
            model_name='incrementalsegregationmaster',
            new_name='INCREMENTAL_warehou_a96b66_idx',
            old_fields=('warehouse', 'status'),
        ),
        migrations.RenameIndex(
            model_name='incrementalsegregationmaster',
            new_name='INCREMENTAL_warehou_610f9b_idx',
            old_fields=('warehouse', 'reference_type', 'reference_sub_type', 'reference_value'),
        ),
        migrations.RenameIndex(
            model_name='incrementaltable',
            new_name='INCREMENTAL_user_id_ce0088_idx',
            old_fields=('user', 'type_name'),
        ),
        migrations.RenameIndex(
            model_name='invoiceforms',
            new_name='INVOICE_FOR_warehou_e9a149_idx',
            old_fields=('warehouse_id', 'invoice_format'),
        ),
        migrations.RenameIndex(
            model_name='masterattributes',
            new_name='MASTER_ATTR_warehou_fc9c4b_idx',
            old_fields=('warehouse', 'attribute_reference', 'attribute_model'),
        ),
        migrations.RenameIndex(
            model_name='masterdocs',
            new_name='MASTER_DOCS_user_id_49188d_idx',
            old_fields=('user', 'master_id', 'master_type', 'extra_flag'),
        ),
        migrations.RenameIndex(
            model_name='masterdocs',
            new_name='MASTER_DOCS_master__daf2c9_idx',
            old_fields=('master_id', 'master_type', 'uploaded_file'),
        ),
        migrations.RenameIndex(
            model_name='miscdetail',
            new_name='MISC_DETAIL_user_fa70a9_idx',
            old_fields=('user', 'misc_type'),
        ),
        migrations.RenameIndex(
            model_name='palletdetail',
            new_name='PALLET_DETA_user_cd0445_idx',
            old_fields=('user', 'pallet_code'),
        ),
        migrations.RenameIndex(
            model_name='palletdetail',
            new_name='PALLET_DETA_user_7db5a3_idx',
            old_fields=('user', 'pallet_code', 'quantity'),
        ),
        migrations.RenameIndex(
            model_name='skuattributes',
            new_name='SKU_ATTRIBU_attribu_b7d7ed_idx',
            old_fields=('attribute_name', 'attribute_value'),
        ),
        migrations.RenameIndex(
            model_name='skuattributes',
            new_name='SKU_ATTRIBU_sku_id_effa3c_idx',
            old_fields=('sku', 'attribute_name'),
        ),
        migrations.RenameIndex(
            model_name='skumaster',
            new_name='SKU_MASTER_sku_cod_f5da2b_idx',
            old_fields=('sku_code',),
        ),
        migrations.RenameIndex(
            model_name='skumaster',
            new_name='SKU_MASTER_user_7a87ae_idx',
            old_fields=('user', 'sku_code', 'wms_code'),
        ),
        migrations.RenameIndex(
            model_name='skumaster',
            new_name='SKU_MASTER_user_59e87b_idx',
            old_fields=('user', 'sku_code'),
        ),
        migrations.RenameIndex(
            model_name='skurelation',
            new_name='SKU_RELATIO_parent__343eec_idx',
            old_fields=('parent_sku', 'member_sku', 'relation_type'),
        ),
        migrations.RenameIndex(
            model_name='skurelation',
            new_name='SKU_RELATIO_parent__42baac_idx',
            old_fields=('parent_sku', 'member_sku'),
        ),
        migrations.RenameIndex(
            model_name='skurelation',
            new_name='SKU_RELATIO_parent__607633_idx',
            old_fields=('parent_sku', 'relation_type'),
        ),
        migrations.RenameIndex(
            model_name='spoketriggersdetail',
            new_name='SPOKE_TRIGG_hub_spo_1a981f_idx',
            old_fields=('hub_spoke_detail', 'action_name', 'status'),
        ),
        migrations.RenameIndex(
            model_name='taxmaster',
            new_name='TAX_MASTER_cgst_ta_f6464c_idx',
            old_fields=('cgst_tax', 'sgst_tax', 'igst_tax', 'cess_tax', 'user'),
        ),
        migrations.RenameIndex(
            model_name='taxmaster',
            new_name='TAX_MASTER_user_id_3c4e26_idx',
            old_fields=('user', 'product_type', 'inter_state'),
        ),
        migrations.RenameIndex(
            model_name='taxmaster',
            new_name='TAX_MASTER_user_id_6b273d_idx',
            old_fields=('user', 'product_type'),
        ),
        migrations.RenameIndex(
            model_name='tempjson',
            new_name='TEMP_JSON_warehou_347611_idx',
            old_fields=('warehouse', 'model_name', 'model_reference'),
        ),
        migrations.RenameIndex(
            model_name='tempjson',
            new_name='TEMP_JSON_warehou_3022af_idx',
            old_fields=('warehouse', 'model_id', 'model_name'),
        ),
        migrations.RenameIndex(
            model_name='termsandconditions',
            new_name='TERMS_AND_C_warehou_dc7061_idx',
            old_fields=('warehouse', 'document_type'),
        ),
        migrations.RenameIndex(
            model_name='termsandconditions',
            new_name='TERMS_AND_C_warehou_7f3174_idx',
            old_fields=('warehouse', 'document_type', 'reference', 'status'),
        ),
        migrations.RenameIndex(
            model_name='userintegrationapis',
            new_name='USER_INTEGR_status_cb976d_idx',
            old_fields=('status', 'trigger', 'user_integration'),
        ),
        migrations.RenameIndex(
            model_name='userintegrationapis',
            new_name='USER_INTEGR_trigger_340cd8_idx',
            old_fields=('trigger', 'user_integration'),
        ),
        migrations.RenameIndex(
            model_name='userintegrationcalls',
            new_name='USER_INTEGR_status_f41138_idx',
            old_fields=('status',),
        ),
        migrations.RenameIndex(
            model_name='userintegrationcalls',
            new_name='USER_INTEGR_integra_dd18fe_idx',
            old_fields=('integration_reference', 'user_integrationapis'),
        ),
        migrations.RenameIndex(
            model_name='userintegrations',
            new_name='USER_INTEGR_status_df07bd_idx',
            old_fields=('status', 'user'),
        ),
        migrations.RenameIndex(
            model_name='userintegrations',
            new_name='USER_INTEGR_status_365715_idx',
            old_fields=('status',),
        ),
        migrations.RenameIndex(
            model_name='wavecriteria',
            new_name='WAVE_CRITER_name_06d276_idx',
            old_fields=('name', 'status', 'warehouse'),
        ),
    ]
