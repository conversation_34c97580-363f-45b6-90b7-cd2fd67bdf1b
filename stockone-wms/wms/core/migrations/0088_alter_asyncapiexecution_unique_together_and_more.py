# Generated by Django 4.2.10 on 2024-07-16 07:35

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0087_wmsuploads_json_data'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='asyncapiexecution',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='asyncapiexecution',
            name='status',
            field=models.IntegerField(choices=[(0, 'Pending'), (1, 'In Progress'), (2, 'Completed'), (3, 'Failed'), (4, 'Partially Completed')], default=0, max_length=2),
        ),
        migrations.AlterIndexTogether(
            name='asyncapiexecution',
            index_together={('id', 'status'), ('id', 'api_name', 'status', 'warehouse')},
        ),
        migrations.AlterModelTable(
            name='asyncapiexecution',
            table='ASYNC_API_EXECUTION',
        ),
    ]
