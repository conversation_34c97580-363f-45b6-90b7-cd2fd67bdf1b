# Generated by Django 4.2.20 on 2025-05-07 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0144_alter_masterdocs_index_together_and_more'),
    ]

    operations = [
       migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."TERMS_AND_CONDITIONS_warehouse_id_bbca1859"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "TERMS_AND_CONDITIONS_warehouse_id_bbca1859"
                ON public."TERMS_AND_CONDITIONS" USING btree
                (warehouse_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
