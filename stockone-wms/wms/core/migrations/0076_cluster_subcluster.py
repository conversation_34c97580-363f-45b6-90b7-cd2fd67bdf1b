# Generated by Django 4.2.10 on 2024-04-23 09:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0026_alter_apiratelimit_request_method'),
        ('core', '0075_merge_20240410_1037'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cluster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=64)),
                ('criteria', models.CharField(choices=[('order_skus_count', 'order_skus_count'), ('weight', 'weight'), ('volume', 'volume')], max_length=32)),
                ('range_from', models.FloatField()),
                ('range_to', models.Float<PERSON>ield()),
                ('orders_in_picklist', models.IntegerField()),
                ('lpn_type', models.CharField(blank=True, default='', max_length=64)),
                ('status', models.BooleanField(default=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'CLUSTER',
                'unique_together': {('warehouse', 'name')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SubCluster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=64)),
                ('status', models.BooleanField(default=True)),
                ('lpn_type', models.CharField(blank=True, default='', max_length=64)),
                ('orders_in_lpn', models.IntegerField()),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('cluster', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.cluster')),
            ],
            options={
                'db_table': 'SUB_CLUSTER',
                'unique_together': {('name', 'cluster')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
