# Generated by Django 4.2.10 on 2024-09-03 13:11

import core.models.common
from django.db import migrations, models
import wms.storage_backends


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0095_merge_20240819_0531'),
    ]

    operations = [
        migrations.AddField(
            model_name='masterheadersview',
            name='order_view_sku_level_master',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AlterField(
            model_name='masterdocs',
            name='uploaded_file',
            field=models.FileField(blank=True, null=True, storage=wms.storage_backends.PrivateMediaStorage(), upload_to=core.models.common.get_path),
        ),
        migrations.AlterField(
            model_name='userintegrationcalls',
            name='integration_reference',
            field=models.CharField(blank=True, default='', max_length=64, null=True),
        ),
    ]
