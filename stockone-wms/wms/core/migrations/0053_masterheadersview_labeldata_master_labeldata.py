# Generated by Django 4.2 on 2023-08-14 12:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0010_userpassword'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('core', '0052_merge_20230811_0529'),
    ]

    operations = [
        migrations.AddField(
            model_name='masterheadersview',
            name='labeldata_master',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.CreateModel(
            name='LabelData',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('label_data', models.J<PERSON><PERSON>ield()),
                ('label_data_hash', models.CharField(blank=True, max_length=64)),
                ('label_use_count', models.IntegerField(default=0)),
                ('object_id', models.PositiveIntegerField()),
                ('status', models.IntegerField(choices=[(0, 'UnLocked'), (1, 'Locked')], default=1)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('created_by', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_labeldata_set', to=settings.AUTH_USER_MODEL)),
                ('label_config', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.labelconfig')),
                ('updated_by', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updated_labeldata_set', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='labeldata_set', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'LABEL_DATA',
                'unique_together': {('warehouse', 'label_config', 'label_data_hash', 'content_type')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
