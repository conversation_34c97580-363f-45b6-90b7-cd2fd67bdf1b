# Generated by Django 4.2.16 on 2025-01-02 06:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0033_infraconfig'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0111_wavecriteria_full_open_order'),
    ]

    operations = [
        migrations.CreateModel(
            name='MasterAttributes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('attribute_reference', models.CharField(default='', max_length=32)),
                ('attribute_model', models.CharField(default='', max_length=32)),
                ('attribute_name', models.CharField(default='', max_length=64)),
                ('attribute_value', models.Char<PERSON>ield(default='', max_length=128)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'MASTER_ATTRIBUTES',
                'unique_together': {('warehouse', 'attribute_reference', 'attribute_model', 'attribute_name')},
                'index_together': {('warehouse', 'attribute_reference', 'attribute_model')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
