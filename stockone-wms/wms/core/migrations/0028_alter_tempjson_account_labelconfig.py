# Generated by Django 4.2 on 2023-06-21 04:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0008_delete_userattributes'),
        ('core', '0027_merge_20230616_1317'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tempjson',
            name='account',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile'),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='LabelConfig',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('used_at', models.CharField(blank=True, default='', max_length=64)),
                ('reference', models.CharField(blank=True, default='', max_length=64)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'LABEL_CONFIG',
                'unique_together': {('warehouse', 'reference')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
