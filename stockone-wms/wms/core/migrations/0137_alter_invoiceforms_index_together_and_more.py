# Generated by Django 4.2.20 on 2025-05-07 07:43

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0136_alter_currencyexchangemaster_index_together_and_more'),
    ]

    operations = [
       migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."INVOICE_FORMS_warehouse_id_id_8d468d7f"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "INVOICE_FORMS_warehouse_id_id_8d468d7f"
                ON public."INVOICE_FORMS" USING btree
                (warehouse_id_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
