# Generated by Django 4.2.16 on 2025-04-03 09:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0122_masterheadersview_multi_warehouse_stock_detail_master_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='wavecriteria',
            name='action_type',
            field=models.CharField(choices=[('picklist_generation', 'Picklist Generation'), ('fifo_allocation', 'FIFO Allocation'), ('fare_allocation', 'FAIR Allocation'), ('constrained_allocation', 'Constrained Allocation')], default='picklist_generation', max_length=64),
        ),
        migrations.AlterField(
            model_name='wavecriteria',
            name='order_type',
            field=models.Char<PERSON>ield(default='', max_length=256),
        ),
    ]
