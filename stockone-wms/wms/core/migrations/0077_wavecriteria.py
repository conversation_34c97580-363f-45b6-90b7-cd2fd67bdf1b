# Generated by Django 4.2.10 on 2024-05-01 04:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0026_alter_apiratelimit_request_method'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0076_cluster_subcluster'),
    ]

    operations = [
        migrations.CreateModel(
            name='WaveCriteria',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(default='', max_length=64)),
                ('start_time', models.TimeField(default=None, null=True)),
                ('end_time', models.TimeField(default=None, null=True)),
                ('frequency', models.Char<PERSON>ield(default='', max_length=64)),
                ('order_type', models.Char<PERSON>ield(default='', max_length=64)),
                ('no_of_orders', models.IntegerField(default=0)),
                ('picklist_strategy', models.CharField(default='', max_length=64)),
                ('status', models.IntegerField(default=1)),
                ('json_data', models.JSONField(blank=True, default={}, null=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'WAVE_CRITERIA',
                'index_together': {('name', 'status', 'warehouse')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
