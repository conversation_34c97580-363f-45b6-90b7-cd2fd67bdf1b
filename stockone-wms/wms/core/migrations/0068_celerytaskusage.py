# Generated by Django 4.2.7 on 2024-02-02 11:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0067_alter_qcconfiguration_reasons'),
    ]

    operations = [
        migrations.CreateModel(
            name='CeleryTaskUsage',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('task_id', models.CharField(max_length=255, unique=True)),
                ('queue', models.CharField(blank=True, max_length=255, null=True)),
                ('cpu_usage', models.FloatField()),
                ('cpu_cycles', models.IntegerField()),
                ('memory_usage', models.FloatField()),
                ('time', models.Integer<PERSON>ield()),
                ('extrajson', models.J<PERSON>NField(default=dict)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='taskusage_set', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'CELERY_TASK_USAGE',
            },
        ),
    ]
