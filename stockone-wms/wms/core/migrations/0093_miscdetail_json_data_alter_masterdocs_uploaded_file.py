# Generated by Django 4.2.10 on 2024-08-14 09:53

import core.models.common
import django.core.files.storage
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0092_actiontrigger_created_by_actiontrigger_updated_by_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='miscdetail',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='masterdocs',
            name='uploaded_file',
            field=models.FileField(blank=True, null=True, storage=django.core.files.storage.FileSystemStorage(), upload_to=core.models.common.get_path),
        ),
    ]
