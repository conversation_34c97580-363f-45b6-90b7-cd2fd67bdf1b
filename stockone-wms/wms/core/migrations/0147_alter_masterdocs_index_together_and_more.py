# Generated by Django 4.2.20 on 2025-05-07 09:45

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0146_alter_masterdocs_index_together_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."MASTER_DOCS_master_id_6b0c7ed2_idx"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "MASTER_DOCS_master_id_6b0c7ed2_idx"
                ON public."MASTER_DOCS" USING btree
                (master_id COLLATE pg_catalog."default" ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."MASTER_DOCS_master_id_6b0c7ed2_like"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "MASTER_DOCS_master_id_6b0c7ed2_like"
                ON public."MASTER_DOCS" USING btree
                (master_id COLLATE pg_catalog."default" varchar_pattern_ops ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."MASTER_DOCS_master_type_85cf4986_idx"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "MASTER_DOCS_master_type_85cf4986_idx"
                ON public."MASTER_DOCS" USING btree
                (master_type COLLATE pg_catalog."default" ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."MASTER_DOCS_master_type_85cf4986_like"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "MASTER_DOCS_master_type_85cf4986_like"
                ON public."MASTER_DOCS" USING btree
                (master_type COLLATE pg_catalog."default" varchar_pattern_ops ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."MASTER_DOCS_user_id_3feb6e1e"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "MASTER_DOCS_user_id_3feb6e1e"
                ON public."MASTER_DOCS" USING btree
                (user_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
