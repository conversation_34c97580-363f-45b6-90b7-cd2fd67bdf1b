# Generated by Django 4.2.10 on 2024-02-20 04:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0023_apiratelimit'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0068_celerytaskusage'),
    ]

    operations = [
        migrations.CreateModel(
            name='TermsAndConditions',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('document_type', models.CharField(default='', max_length=64)),
                ('reference', models.CharField(default='', max_length=64)),
                ('terms_and_conditions', models.TextField(blank=True, null=True)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'TERMS_AND_CONDITIONS',
                'unique_together': {('warehouse', 'document_type', 'reference')},
                'index_together': {('warehouse', 'document_type', 'reference', 'status'), ('warehouse',), ('warehouse', 'document_type')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
