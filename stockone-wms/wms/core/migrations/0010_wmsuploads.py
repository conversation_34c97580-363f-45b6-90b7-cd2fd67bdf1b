# Generated by Django 4.2 on 2023-05-18 02:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0004_remove_usergroups_account'),
        ('core', '0009_qcconfiguration'),
    ]

    operations = [
        migrations.CreateModel(
            name='WMSUploads',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('uploaded_file', models.FileField(blank=True, null=True, upload_to='static/wms_uploads/')),
                ('file_request_function', models.CharField(default='', max_length=64)),
                ('status', models.IntegerField(default=0)),
                ('response_file', models.TextField()),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'WMS_UPLOADS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
