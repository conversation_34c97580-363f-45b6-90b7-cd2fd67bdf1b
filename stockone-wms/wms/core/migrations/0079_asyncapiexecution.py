# Generated by Django 4.2.10 on 2024-05-29 06:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import uuid
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0027_companymaster_json_data_and_more'),
        ('core', '0078_alter_tempjson_unique_together'),
    ]

    operations = [
        migrations.CreateModel(
            name='AsyncAPIExecution',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('api_name', models.CharField(default='', max_length=64)),
                ('payload', models.JSONField(blank=True, null=True)),
                ('result', models.J<PERSON>NField(blank=True, null=True)),
                ('status', models.Integer<PERSON>ield(choices=[(0, 'Pending'), (1, 'In Progress'), (2, 'Completed'), (3, 'Failed')], default=0, max_length=2)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('requested_user', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='async_api_requested_user', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='async_api_warehouse', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
                'unique_together': {('id', 'account_id')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
