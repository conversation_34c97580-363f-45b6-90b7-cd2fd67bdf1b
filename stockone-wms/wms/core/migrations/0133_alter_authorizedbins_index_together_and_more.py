# Generated by Django 4.2.20 on 2025-05-07 07:13

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0132_rename_authorizedbins_bin_number_user_authorized__bin_num_ae11fb_idx_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."AUTHORIZED_BINS_user_id_195778d1"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "AUTHORIZED_BINS_user_id_195778d1"
                ON public."AUTHORIZED_BINS" USING btree
                (user_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
