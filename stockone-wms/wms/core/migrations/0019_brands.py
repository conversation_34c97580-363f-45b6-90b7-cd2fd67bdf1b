# Generated by Django 4.2 on 2023-05-29 06:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0006_remove_useraccesstokens_account_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0018_alter_wmsuploads_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Brands',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('brand_name', models.CharField(default='', max_length=64)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'BRANDS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
