# Generated by Django 4.2.20 on 2025-04-28 12:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0128_alter_actiontrigger_staff'),
    ]

    operations = [
        migrations.AlterField(
            model_name='asyncapiexecution',
            name='status',
            field=models.IntegerField(choices=[(0, 'Pending'), (1, 'In Progress'), (2, 'Completed'), (3, 'Failed'), (4, 'Partially Completed')], default=0),
        ),
        migrations.AlterField(
            model_name='incrementaltable',
            name='max_length',
            field=models.IntegerField(default=15, null=True),
        ),
        migrations.AlterField(
            model_name='wmsuploads',
            name='status',
            field=models.IntegerField(choices=[(0, 'Running'), (1, 'Completed'), (2, 'Failed'), (3, 'Invalid'), (4, 'Inprogress')], default=0),
        ),
    ]
