# Generated by Django 4.2.7 on 2023-12-15 14:23

from django.conf import settings
from django.db import migrations
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0063_masterheadersview_credit_note_master_and_more'),
    ]

    operations = [
        migrations.AlterIndexTogether(
            name='tempjson',
            index_together=set(),
        ),
        migrations.AddField(
            model_name='tempjson',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterIndexTogether(
            name='tempjson',
            index_together={('warehouse', 'model_id', 'model_name')},
        ),
    ]
