# Generated by Django 4.2 on 2023-06-13 08:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0023_skumaster_receipt_tolerance'),
    ]

    operations = [
        migrations.AddField(
            model_name='incrementaltable',
            name='date_type',
            field=models.IntegerField(choices=[(1, 'Include Finacial Year'), (2, 'Include Month and Year')], null=True),
        ),
        migrations.AddField(
            model_name='incrementaltable',
            name='interfix',
            field=models.CharField(default='', max_length=64),
        ),
        migrations.AddField(
            model_name='incrementaltable',
            name='prefix',
            field=models.CharField(default='', max_length=64),
        ),

    ]
