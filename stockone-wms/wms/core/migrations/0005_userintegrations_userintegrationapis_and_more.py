# Generated by Django 4.2 on 2023-05-07 12:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0004_remove_usergroups_account'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0004_alter_serialnumbertransactionmapping_index_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserIntegrations',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(default='', max_length=64)),
                ('auth_type', models.CharField(default='', max_length=64)),
                ('status', models.IntegerField(default=1)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'USER_INTEGRATIONS',
                'index_together': {('status', 'user'), ('status',)},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='UserIntegrationAPIS',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('api_url', models.TextField(default='')),
                ('token_data', models.TextField(blank=True, null=True)),
                ('send_token', models.IntegerField(default=0)),
                ('is_token_url', models.IntegerField(default=0)),
                ('access_token', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('trigger', models.CharField(blank=True, default='', max_length=128, null=True)),
                ('data_format', models.CharField(blank=True, default='', max_length=32, null=True)),
                ('api_method', models.CharField(blank=True, choices=[('POST', 'POST'), ('GET', 'GET'), ('PUT', 'PUT'), ('PATCH', 'PATCH')], default='', max_length=16, null=True)),
                ('data_params', models.CharField(blank=True, choices=[('form_params', 'Form Params'), ('request_body', 'Request Body')], default='', max_length=32, null=True)),
                ('filters', models.TextField(blank=True, default='', null=True)),
                ('retry_attempts', models.IntegerField(default=0)),
                ('is_async', models.BooleanField(default=True)),
                ('status', models.IntegerField(default=1)),
                ('batch_level_integration', models.IntegerField(default=0)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user_integration', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.userintegrations')),
            ],
            options={
                'db_table': 'USER_INTEGRATIONS_APIS',
                'index_together': {('trigger', 'user_integration'), ('status', 'trigger', 'user_integration')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='UserIntegrationCalls',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('integration_reference', models.CharField(blank=True, default='', max_length=32, null=True)),
                ('api_data', models.JSONField(blank=True, null=True)),
                ('api_response', models.TextField(blank=True, default='', null=True)),
                ('retry_counter', models.IntegerField(default=0)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user_integrationapis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.userintegrationapis')),
            ],
            options={
                'db_table': 'USER_INTEGRATIONS_CALLS',
                'index_together': {('integration_reference', 'user_integrationapis'), ('status',)},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
