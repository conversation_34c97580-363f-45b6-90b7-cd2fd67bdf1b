# Generated by Django 4.2.7 on 2024-01-09 04:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0065_userintegrationapis_attach_request_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='HubSpokeDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('hub', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hub_warehouse', to=settings.AUTH_USER_MODEL)),
                ('spoke', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='spoke_warehouse', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'HUB_SPOKE_DETAIL',
                'unique_together': {('hub', 'spoke')},
                'index_together': {('hub', 'spoke', 'status'), ('hub', 'status')},
            },
        ),
        migrations.CreateModel(
            name='SpokeTriggersDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('action_name', models.CharField(blank=True, default='', max_length=64)),
                ('status', models.IntegerField(default=1)),
                ('json_data', models.JSONField(blank=True, default={}, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('hub_spoke_detail', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hub_spoke_details', to='core.hubspokedetail')),
            ],
            options={
                'db_table': 'SPOKE_TRIGGERS_DETAIL',
                'unique_together': {('hub_spoke_detail', 'action_name')},
                'index_together': {('hub_spoke_detail', 'action_name', 'status')},
            },
        ),
    ]
