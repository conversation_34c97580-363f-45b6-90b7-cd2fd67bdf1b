# Generated by Django 4.2 on 2023-05-31 11:49

from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0008_delete_userattributes'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('core', '0020_userattributes'),
    ]

    operations = [
        migrations.CreateModel(
            name='FormFieldValue',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('value', models.CharField(default='', max_length=256)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('object_id', models.PositiveIntegerField()),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.userattributes')),
            ],
            options={
                'db_table': 'FORM_FIELD_VALUE',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
