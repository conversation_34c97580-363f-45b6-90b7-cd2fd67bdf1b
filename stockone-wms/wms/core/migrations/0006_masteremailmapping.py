# Generated by Django 4.2 on 2023-05-08 07:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('wms_base', '0004_remove_usergroups_account'),
        ('core', '0005_userintegrations_userintegrationapis_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MasterEmailMapping',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('master_id', models.CharField(default='', max_length=64)),
                ('master_type', models.CharField(default='', max_length=64)),
                ('email_id', models.Char<PERSON>ield(default='', max_length=64)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'MASTER_EMAIL_MAPPING',
                'unique_together': {('user', 'master_id', 'master_type', 'email_id')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
