# Generated by Django 4.2 on 2023-05-06 07:10

import core.models
import datetime
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils
from wms.storage_backends import PrivateMediaStorage


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('wms_base', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ActionTrigger',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.BooleanField(default=True)),
                ('to_customer', models.BooleanField(default=False)),
                ('to_supplier', models.BooleanField(default=False)),
                ('cron_tab', models.CharField(blank=True, default='', max_length=32)),
                ('updation_date', models.DateTime<PERSON>ield(auto_now=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('action_type', models.CharField(choices=[('DEFAULT', 'Default'), ('PO_GRN', 'PO GRN Generation'), ('PO_CANCEL', 'Purchase Order Cancellation'), ('PO_CREATION', 'Purchase Order Creation'), ('SHORT_PICK', 'Short Picked'), ('SO_CREATION', 'Sale Order Creation'), ('SO_CANCEL', 'Sale Order Cancellation'), ('PICKLIST_GEN', 'Picklist Generation'), ('PICKLIST_CANCEL', 'Picklist Cancellation'), ('RTV', 'RTV Generation'), ('MOVE_INV', 'Stock Moved'), ('JO_GRN', 'JO GRN Generation'), ('INV_ADJ', 'Inventory Adjusted'), ('JO_RAISED', 'Job Order Creation'), ('SALES_RETURN', 'Sales Return Creation'), ('INVOICE_GEN', 'Invoice Generation'), ('EINVOICE_GEN', 'EInvoice Generation'), ('EWAY_BILL_GEN', 'EWay Bill Generation'), ('INVOICE_CANCEL', 'Invoice Cancellation'), ('EINVOICE_CANCEL', 'EInvoice Cancellation'), ('EWAY_BILL_CANCEL', 'EWay Bill Cancellation'), ('SHIPMENT_GEN', 'Shipment Generation'), ('INV_REPORT', 'Inventory Report'), ('INV_APPROVAL', 'Inventory Approval E-mail'), ('ALL_INTEGRATION', 'All Integrations')], default='DEFAULT', max_length=20)),
            ],
            options={
                'db_table': 'ACTION_TRIGGER',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='ActionTriggerToStaff',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ACTION_TRIGGER_staff',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='AddressMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('address_line_one', models.TextField(default='', max_length=512, null=True)),
                ('address_line_two', models.TextField(default='', max_length=512, null=True)),
                ('city', models.CharField(default='', max_length=64, null=True)),
                ('state', models.CharField(default='', max_length=64, null=True)),
                ('pincode', models.IntegerField(blank=True, null=True)),
                ('country', models.CharField(max_length=64, null=True)),
                ('zip_code', models.CharField(max_length=10, null=True)),
                ('latitude', models.CharField(blank=True, max_length=64, null=True)),
                ('longitude', models.CharField(blank=True, max_length=64, null=True)),
                ('name', models.CharField(blank=True, max_length=64, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=32, null=True)),
                ('address_type', models.CharField(default='', max_length=16, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='AuthorizedBins',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('bin_number', models.CharField(max_length=64)),
                ('status', models.PositiveIntegerField(default=0)),
                ('prefix', models.CharField(blank=True, max_length=32, null=True)),
                ('bin_type', models.CharField(default='general', max_length=32)),
                ('create_user', models.CharField(blank=True, max_length=64, null=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'AUTHORIZED_BINS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='BarcodeEntities',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('entity_type', models.CharField(default='', max_length=64)),
                ('start', models.PositiveIntegerField(blank=True, default=None, null=True)),
                ('end', models.PositiveIntegerField(blank=True, default=None, null=True)),
                ('Format', models.CharField(blank=True, max_length=256, null=True)),
                ('regular_expression', models.CharField(blank=True, max_length=256, null=True)),
            ],
            options={
                'db_table': 'BARCODE_ENTITIES',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='BarcodeSettings',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('format_type', models.CharField(max_length=256)),
                ('size', models.CharField(blank=True, max_length=256, null=True)),
                ('show_fields', models.CharField(blank=True, max_length=256, null=True)),
                ('rows_columns', models.CharField(blank=True, max_length=64, null=True)),
                ('styles', models.TextField(blank=True, null=True)),
                ('mapping_fields', models.CharField(blank=True, max_length=256, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'BARCODE_SETTINGS_TABLE',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='BarcodeTemplate',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('user', models.PositiveIntegerField()),
                ('name', models.CharField(default='', max_length=64)),
                ('brand', models.CharField(default='', max_length=64)),
                ('length', models.PositiveIntegerField(default=None)),
            ],
            options={
                'db_table': 'BARCODE_TEMPLATE',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='CartonTypes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('area', models.CharField(blank=True, max_length=32, null=True)),
                ('carton_type', models.CharField(blank=True, max_length=32, null=True)),
                ('carton_prefix', models.CharField(blank=True, max_length=32, null=True)),
                ('length', models.FloatField(default=0)),
                ('width', models.FloatField(default=0)),
                ('height', models.FloatField(default=0)),
                ('weight', models.FloatField(default=0)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'CARTON_TYPES',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='CurrencyExchangeMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('from_currency', models.CharField(blank=True, max_length=5, null=True)),
                ('to_currency', models.CharField(blank=True, max_length=5, null=True)),
                ('exchange_rate', models.FloatField(default=1)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'CURRENCY_EXCHANGE_MASTER',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='EANNumbers',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('ean_number', models.CharField(default='', max_length=512)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'EAN_NUMBERS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='HSNMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('product_type', models.CharField(default='', max_length=128)),
                ('hsn_code', models.IntegerField(default=0)),
                ('hsn_description', models.CharField(default='', max_length=128)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'HSN_MASTER',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='IncrementalTable',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('type_name', models.CharField(max_length=64)),
                ('value', models.BigIntegerField()),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'INCREMENTAL_TABLE',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='InvoiceForms',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('document_type', models.CharField(max_length=32, null=True)),
                ('invoice_format', models.CharField(max_length=32, null=True)),
                ('output_data', models.TextField(blank=True, null=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'INVOICE_FORMS',
            },
        ),
        migrations.CreateModel(
            name='MasterDocs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('master_id', models.CharField(db_index=True, default='', max_length=64)),
                ('master_type', models.CharField(db_index=True, default='', max_length=64)),
                ('uploaded_file', models.FileField(blank=True, null=True, storage=PrivateMediaStorage(), upload_to=core.models.common.get_path)),
                ('extra_flag', models.CharField(default='', max_length=64)),
                ('document_url', models.CharField(blank=True, default='', max_length=256, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'MASTER_DOCS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='MiscDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('user', models.PositiveIntegerField(db_index=True)),
                ('misc_type', models.CharField(db_index=True, max_length=64)),
                ('misc_value', models.TextField()),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'MISC_DETAIL',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='MiscDetailOptions',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('misc_key', models.CharField(default='', max_length=64)),
                ('misc_value', models.CharField(max_length=255)),
                ('status', models.BooleanField(default=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'MISC_DETAIL_OPTIONS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='PalletDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('user', models.PositiveIntegerField()),
                ('pallet_code', models.CharField(max_length=64)),
                ('quantity', models.FloatField(default=0)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'PALLET_DETAIL',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SerialNumberMapping',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('serial_number', models.CharField(blank=True, default='', max_length=64, null=True)),
                ('status', models.IntegerField(default=0)),
                ('json_data', models.JSONField(blank=True, default={}, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'SERIAL_NUMBER_MAPPING',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SerialNumberTransactionMapping',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('transaction_id', models.PositiveIntegerField(blank=True, null=True)),
                ('reference_number', models.CharField(blank=True, default='', max_length=64, null=True)),
                ('reference_type', models.CharField(blank=True, default='', max_length=32, null=True)),
                ('status', models.IntegerField(default=0)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'SERIAL_TRANSACTION_MAPPING',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SizeMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('user', models.PositiveIntegerField()),
                ('size_name', models.CharField(default='', max_length=64)),
                ('size_value', models.TextField()),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'SIZE_MASTER',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SKUAttributes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('attribute_name', models.CharField(db_index=True, default='', max_length=64)),
                ('attribute_value', models.CharField(default='', max_length=128)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'SKU_ATTRIBUTES',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SKUGroups',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('user', models.PositiveIntegerField()),
                ('group', models.CharField(default='', max_length=60)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'SKU_GROUPS',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SKUImages',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('image_url', models.CharField(default='', max_length=256)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'SKU_IMAGES',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SKUMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('user', models.PositiveIntegerField(db_index=True)),
                ('sku_code', models.CharField(max_length=128)),
                ('wms_code', models.CharField(max_length=128)),
                ('sku_desc', models.CharField(default='', max_length=350)),
                ('sku_group', models.CharField(default='', max_length=64)),
                ('sku_type', models.CharField(default='', max_length=64)),
                ('sku_category', models.CharField(default='', max_length=128)),
                ('sku_class', models.CharField(default='', max_length=64)),
                ('sku_brand', models.CharField(default='', max_length=64)),
                ('style_name', models.CharField(default='', max_length=256)),
                ('sku_size', models.CharField(default='', max_length=64)),
                ('product_type', models.CharField(default='', max_length=64)),
                ('threshold_quantity', models.FloatField(default=0)),
                ('max_norm_quantity', models.FloatField(default=0)),
                ('online_percentage', models.PositiveIntegerField(default=0)),
                ('discount_percentage', models.PositiveIntegerField(default=0)),
                ('price', models.FloatField(default=0)),
                ('cost_price', models.FloatField(default=0)),
                ('mrp', models.FloatField(default=0)),
                ('image_url', models.URLField(default='')),
                ('qc_check', models.IntegerField(default=1)),
                ('scan_picking', models.IntegerField(default=0)),
                ('status', models.IntegerField(default=1)),
                ('relation_type', models.CharField(default='', max_length=32)),
                ('measurement_type', models.CharField(default='', max_length=32)),
                ('sale_through', models.CharField(default='', max_length=32)),
                ('mix_sku', models.CharField(db_index=True, default='', max_length=32)),
                ('color', models.CharField(default='', max_length=64)),
                ('ean_number', models.CharField(default='', max_length=512)),
                ('load_unit_handle', models.CharField(db_index=True, default='unit', max_length=32)),
                ('hsn_code', models.CharField(db_index=True, default='', max_length=20)),
                ('sequence', models.IntegerField(default=0)),
                ('sub_category', models.CharField(default='', max_length=64)),
                ('pick_group', models.CharField(default='', max_length=64)),
                ('shelf_life', models.FloatField(default=0)),
                ('customer_shelf_life', models.DurationField(default=datetime.timedelta(0))),
                ('minimum_shelf_life', models.DurationField(default=datetime.timedelta(0))),
                ('youtube_url', models.CharField(default='', max_length=64)),
                ('enable_serial_based', models.IntegerField(default=0)),
                ('block_options', models.CharField(default='', max_length=5)),
                ('batch_based', models.IntegerField(default=0)),
                ('gl_code', models.PositiveIntegerField(default=0)),
                ('average_price', models.FloatField(default=0)),
                ('average_price_rt', models.FloatField(default=0)),
                ('length', models.CharField(default=0, max_length=64)),
                ('breadth', models.CharField(default=0, max_length=64)),
                ('weight', models.CharField(default=0, max_length=64)),
                ('height', models.CharField(default=0, max_length=64)),
                ('volume', models.FloatField(default=0)),
                ('article_no', models.CharField(default='', max_length=128)),
                ('sku_reference', models.CharField(default='', max_length=128)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('qc_eligible', models.BooleanField(default=False)),
                ('qc_category', models.CharField(choices=[('full', 'FULL QC'), ('sample', 'SAMPLE QC')], default='', max_length=64)),
                ('sample_percentage', models.FloatField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('make_or_buy', models.IntegerField(choices=[(0, 'Default'), (1, 'Make'), (2, 'Buy')], default=0)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
            ],
            options={
                'db_table': 'SKU_MASTER',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='UserPrefixes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('product_category', models.CharField(blank=True, default='', max_length=128)),
                ('sku_category', models.CharField(blank=True, default='', max_length=128)),
                ('type_name', models.CharField(default='', max_length=64)),
                ('prefix', models.CharField(default='', max_length=64)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'USER_PREFIXES',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='UOMMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(default='', max_length=128)),
                ('sku_code', models.CharField(default='', max_length=128)),
                ('base_uom', models.CharField(default='', max_length=32)),
                ('uom_type', models.CharField(default='', max_length=64)),
                ('uom', models.CharField(default='', max_length=64)),
                ('conversion', models.FloatField(default=0)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('company', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wms_base.companymaster')),
            ],
            options={
                'db_table': 'UOM_MASTER',
            },
        ),
        migrations.CreateModel(
            name='UOMDetail',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('uom_code', models.CharField(default='', max_length=128)),
                ('uom_description', models.CharField(default='', max_length=128)),
                ('uom_class', models.CharField(default='', max_length=64)),
                ('conversion_factor', models.FloatField(default=0)),
                ('decimals', models.DecimalField(decimal_places=0, default=0, max_digits=50)),
                ('base_uom', models.BooleanField(default=False)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('company', wms_base.wms_utils.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wms_base.companymaster')),
                ('created_by', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='uom_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='uom_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'UOM_Detail',
            },
        ),
        migrations.CreateModel(
            name='TempJson',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('model_id', models.PositiveIntegerField()),
                ('model_name', models.CharField(default='', max_length=256)),
                ('model_json', models.TextField(default='')),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'TEMP_JSON',
                'index_together': {('model_id', 'model_name')},
            },
        ),
        migrations.CreateModel(
            name='TaxMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('product_type', models.CharField(db_index=True, default='', max_length=64)),
                ('inter_state', models.IntegerField(default=0)),
                ('cgst_tax', models.FloatField(default=0)),
                ('sgst_tax', models.FloatField(default=0)),
                ('igst_tax', models.FloatField(default=0)),
                ('cess_tax', models.FloatField(default=0)),
                ('utgst_tax', models.FloatField(default=0)),
                ('apmc_tax', models.FloatField(default=0)),
                ('min_amt', models.FloatField(default=0)),
                ('max_amt', models.FloatField(default=0)),
                ('reference_id', models.CharField(default='', max_length=64)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'TAX_MASTER',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
        migrations.CreateModel(
            name='SKURelation',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity', models.FloatField(default=1)),
                ('relation_type', models.CharField(default='', max_length=64)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('member_sku', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='member_sku', to='core.skumaster')),
                ('parent_sku', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='parent_sku', to='core.skumaster')),
            ],
            options={
                'db_table': 'SKU_RELATION',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
