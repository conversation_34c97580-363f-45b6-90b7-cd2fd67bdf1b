# Generated by Django 4.2 on 2023-07-24 06:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0045_remove_actiontriggertostaff_account'),
    ]

    operations = [
        migrations.AddField(
            model_name='userattributes',
            name='attribute_order',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='userattributes',
            name='show_name',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='qcconfiguration',
            name='transaction_type',
            field=models.CharField(choices=[('after_grn', 'AFTER_PO_GRN'), ('after_jo_grn', 'AFTER_JO_GRN'), ('after_sr_grn', 'AFTER_SR_GRN'), ('after_putaway', 'AFTER_PUTAWAY'), ('after_move_inventory', 'AFTER_MOVE_INVENTORY')], db_index=True, default='', max_length=64),
        ),
    ]
