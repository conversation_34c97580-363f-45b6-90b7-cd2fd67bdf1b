# Generated by Django 4.2.15 on 2024-10-16 13:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0031_companylevelconfigs'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0103_merge_20241015_1702'),
    ]

    operations = [
        migrations.CreateModel(
            name='IncrementalSegregationMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('reference_type', models.CharField(blank=True, default='', max_length=64)),
                ('reference_sub_type', models.CharField(blank=True, default='', max_length=64)),
                ('reference_value', models.CharField(blank=True, default='', max_length=64)),
                ('status', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('incremental_record', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.incrementaltable')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'INCREMENTAL_SEGREGATION_MASTER',
                'index_together': {('warehouse', 'status'), ('warehouse', 'reference_type', 'reference_sub_type', 'reference_value')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
