# Generated by Django 4.2.20 on 2025-05-07 08:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0140_alter_masterdocs_index_together_and_more'),
    ]

    operations = [
       migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."SKU_MASTER_user_8842122c"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "SKU_MASTER_user_8842122c"
                ON public."SKU_MASTER" USING btree
                ("user" ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
