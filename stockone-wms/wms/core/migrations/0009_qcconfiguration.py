# Generated by Django 4.2 on 2023-05-11 15:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0004_remove_usergroups_account'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0008_crontabschedule_purchaseapprovalconfig'),
    ]

    operations = [
        migrations.CreateModel(
            name='QCConfiguration',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('transaction_type', models.CharField(choices=[('after_grn', 'AFTER_PO_GRN'), ('after_jo_grn', 'AFTER_JO_GRN'), ('after_sr_grn', 'AFTER_SR_GRN')], db_index=True, default='', max_length=64)),
                ('all_qc', models.BooleanField(default=False)),
                ('status', models.BooleanField(default=False)),
                ('reasons', models.Char<PERSON>ield(blank=True, default='', max_length=64)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('warehouse', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'QC_CONFIGURATION',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
