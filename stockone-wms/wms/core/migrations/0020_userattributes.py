# Generated by Django 4.2 on 2023-05-29 13:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0008_delete_userattributes'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0019_brands'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserAttributes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('attribute_model', models.CharField(default='', max_length=32)),
                ('attribute_name', models.CharField(default='', max_length=64)),
                ('attribute_type', models.CharField(default='', max_length=64)),
                ('attribute_values', models.CharField(default='', max_length=64)),
                ('is_mandatory', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('status', models.IntegerField(default=1)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('json_data', models.JSONField(blank=True, null=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('user', wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'USER_ATTRIBUTES',
                'unique_together': {('user', 'attribute_model', 'attribute_name')},
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
