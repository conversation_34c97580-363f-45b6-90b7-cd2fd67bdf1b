# Generated by Django 4.2 on 2023-05-08 09:03

from django.db import migrations
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0004_cyclecount_replenishmentmaster_moveinventory_and_more'),
        ('core', '0006_masteremailmapping'),
    ]

    operations = [
        migrations.AddField(
            model_name='skumaster',
            name='zone',
            field=wms_base.wms_utils.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.zonemaster'),
        ),
    ]
