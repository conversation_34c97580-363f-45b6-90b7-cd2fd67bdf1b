# Generated by Django 4.2.20 on 2025-05-07 08:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0138_alter_invoiceforms_index_together_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='DROP INDEX IF EXISTS public."SKU_ATTRIBUTES_sku_id_7d0742e8"',
            reverse_sql="""
                CREATE INDEX IF NOT EXISTS "SKU_ATTRIBUTES_sku_id_7d0742e8"
                ON public."SKU_ATTRIBUTES" USING btree
                (sku_id ASC NULLS LAST)
                TABLESPACE pg_default;
            """,
        ),
    ]
