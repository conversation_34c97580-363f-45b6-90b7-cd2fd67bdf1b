# Generated by Django 4.2 on 2023-08-30 11:47

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0055_masterheadersview_checklist_master'),
    ]

    operations = [
        migrations.AlterField(
            model_name='qcconfiguration',
            name='transaction_type',
            field=models.CharField(choices=[('after_grn', 'AFTER_PO_GRN'), ('after_jo_grn', 'AFTER_JO_GRN'), ('after_sr_grn', 'AFTER_SR_GRN'), ('after_putaway', 'AFTER_PUTAWAY'), ('after_jo_putaway', 'AFTER_JO_PUTAWAY'), ('after_move_inventory', 'AFTER_MOVE_INVENTORY'), ('after_material_to_material', 'AFTER_MATERIAL_TO_MATERIAL')], db_index=True, default='', max_length=64),
        ),
        migrations.AlterUniqueTogether(
            name='purchaseapprovalconfig',
            unique_together={('user', 'name', 'po_type', 'level', 'min_Amt', 'max_Amt', 'approval_type')},
        ),
    ]
