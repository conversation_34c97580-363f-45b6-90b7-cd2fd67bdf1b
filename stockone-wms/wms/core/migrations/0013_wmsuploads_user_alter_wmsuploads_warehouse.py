# Generated by Django 4.2 on 2023-05-19 05:48

from django.conf import settings
from django.db import migrations
import django.db.models.deletion
import wms_base.wms_utils


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0012_wmsuploads_response_status_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='wmsuploads',
            name='user',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='requested_user', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='wmsuploads',
            name='warehouse',
            field=wms_base.wms_utils.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_name', to=settings.AUTH_USER_MODEL),
        ),
    ]
