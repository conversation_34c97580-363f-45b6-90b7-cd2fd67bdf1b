#package imports
import datetime
import time
from django.utils import timezone
from datetime import datetime, timedelta

from wms_base.models import UserProfile
#django imports
from django.core.management import BaseCommand
from django.db.models import Sum, Max
from core_operations.views.common.main import DATE_FORMAT, get_local_date_known_timezone

#wms base
from wms_base.wms_utils import init_logger

#inventory models
from inventory.models.locator import ClosingStock, CostDimensions, CostDimensionMaster
from outbound.models.invoice import SellerOrderSummary
from inbound.models.rtv import ReturnToVendor
from inbound.models.grn import SellerPOSummary
from core.models import MiscDetail

from wms_base.models import User
from django.db import transaction
import pytz

log = init_logger('logs/load_closing_stock.log')

class Command(BaseCommand):
    """
    Loading Supplier SKU Wise Cost Dimensions
    """
    help = "Save Supplier SKU Wise Cost Dimensions everyday"

    def add_arguments(self, parser):
        parser.add_argument("--date", nargs="?", type=str, help="Date in YYYY-MM-DD format (IST). Defaults to yesterday.")
        parser.add_argument("--timezone", type=str, required=True, help="Timezone of the date (mandatory).")


    def prepare_supplier_cost_values(self, warehouse_id):
        costdimension_master = CostDimensionMaster.objects.filter(warehouse_id=warehouse_id)

        cost_data = {}
        for entry in costdimension_master:
            supplier_id = entry.seller_id if entry.seller_id else "default"
            key = (supplier_id, entry.sku_cost_category)

            cost_data[key] = {
                "GRN": entry.grn_cost,
                "RTV": entry.rtv_cost,
                "SaleOrders": entry.sale_orders_cost,
                "Storage": entry.storage_cost,
                "storage_cost_unit": entry.storage_cost_unit,
            }

        self.supplier_cost_dict = cost_data

    def get_cost(self, seller_id, sku_cost_category, cost_type, quantity, volume):
        uom, unit_cost, total_cost = "eaches", 0, 0

        key = (seller_id, sku_cost_category)  # Primary key
        default_key = ("default", sku_cost_category)  # Fallback key

        cost_dict = self.supplier_cost_dict

        if key in cost_dict:
            cost_data = cost_dict[key]
        elif default_key in cost_dict:
            cost_data = cost_dict[default_key]
        else:
            return  uom, unit_cost, total_cost

        unit_cost = cost_data.get(cost_type, 0)
        uom = cost_data.get("storage_cost_unit", "eaches")

        total_cost = quantity * unit_cost * 1
        # Adjust multiplication logic based on uom
        if cost_type == "Storage" and uom != "eaches":
            uom = "Cubic Centimeter"
            total_cost = quantity * unit_cost * volume

        return uom, unit_cost, total_cost
    

    def get_utc_range(self, date_obj, user_timezone):
        # Define the user's timezone
        local_tz = pytz.timezone(user_timezone)

        # Get local start of the day (00:00 AM in user's timezone)
        local_start = local_tz.localize(datetime.combine(date_obj, datetime.min.time()))

        # Alternative approach
        local_end = local_tz.localize(datetime.combine(date_obj, datetime.max.time()))

        # Convert both to UTC
        utc_start = local_start.astimezone(pytz.utc)
        utc_end = local_end.astimezone(pytz.utc)

        return utc_start, utc_end



    def store_cost_dimensions(self, date, timezone, warehouse_ids):
        log.info("Cost Dimensions Start time %s" % (str(datetime.now())))

        #check for the warehouses config enbled to calculate cost dimensions
        list_of_warehouse_ids = MiscDetail.objects.filter(misc_type="seller_usage_calculation", misc_value="true", user__in=warehouse_ids).values_list("user", flat=True)

        list_of_warehouses_ids_userprofileids = User.objects.filter(id__in=warehouse_ids).values("username", "id", "userprofile__id")
        warehouse_userprofile_map = {}
        for item in list_of_warehouses_ids_userprofileids:
            warehouse_userprofile_map[item['id']] = {
                'usermame': item['username'],
                'user_id': item['id'],
                'userprofile_id': item['userprofile__id']
            }

        #load cost dimensions for each warehouse
        for warehouse in list_of_warehouse_ids:

            #date conversion
            if date:
                date_obj = datetime.strptime(date, DATE_FORMAT)
                closing_date = date_obj
            else:
                # If date not provided, calculate yesterday's date based on warehouse timezone
                date_obj = datetime.utcnow()
                date_obj = date_obj.date() - timedelta(days=1)
                closing_date = get_local_date_known_timezone(date_obj, timezone)
                closing_date = closing_date.date() - timedelta(days=1)

            utc_start, utc_end = self.get_utc_range(date_obj, timezone)

            self.prepare_supplier_cost_values(warehouse)
            warehouse_name = warehouse_userprofile_map[warehouse]['usermame']
            warehouse_id = warehouse_userprofile_map[warehouse]['user_id']
            userprofile_id = warehouse_userprofile_map[warehouse]['userprofile_id']
            log.info("Processing Warehouse %s" % warehouse)

            # ====== Inward ========
            # #GRN Cost Calculation
            grn_processed = list(SellerPOSummary.objects.filter(user_id=warehouse_id, status=0, updation_date__gte=utc_start, updation_date__lte=utc_end).values("sku_id", "sku__sku_code", "sku__cost_category", "sku__seller_id", "sku__seller__supplier_id", "grn_number", "grn_type").annotate(total_quantity=Sum('quantity'), max_updation_date=Max('updation_date')))
            self.load_grn_details(grn_processed, warehouse_id, warehouse_name)

            # ====== Outward ========
            #Order Processed Cost Calculation
            seller_orders = list(SellerOrderSummary.objects.filter(account_id=userprofile_id, order_status_flag__in=["customer_invoices", "delivery_challans"], updation_date__gte=utc_start, updation_date__lte=utc_end).values('picklist__sku_id', 'picklist__sku__sku_code', 'picklist__sku__cost_category', 'picklist__sku__seller_id', "picklist__sku__seller__supplier_id" ,'picklist__picklist_number').annotate(total_quantity=Sum('quantity'), max_updation_date=Max('updation_date')))
            self.load_order_details(seller_orders, warehouse_id, warehouse_name)

            # #Return To Vendor Cost Calculation
            return_to_vendors = list(ReturnToVendor.objects.filter(account_id=userprofile_id, updation_date__gte=utc_start, updation_date__lte=utc_end, status=0).values("seller_po_summary__sku_id", "seller_po_summary__sku__sku_code", "seller_po_summary__sku__cost_category", "seller_po_summary__sku__seller_id","seller_po_summary__sku__seller__supplier_id", "rtv_number").annotate(total_quantity=Sum('quantity'), max_updation_date=Max('updation_date')))
            self.load_rtv_details(return_to_vendors, warehouse_id, warehouse_name)

            # ===== Storage =====
            # #SKU Quantity Volume Calculation
            closing_stocks = list(ClosingStock.objects.filter(stock__sku__user=warehouse_id, closing_date=closing_date).values("stock__sku_id", "stock__sku__sku_code", "stock__sku__cost_category", "stock__sku__seller_id", "stock__sku__seller__supplier_id", "stock__location__location", "stock__sku__volume", "closing_date").annotate(total_quantity=Sum('quantity'), max_updation_date=Max('updation_date')))
            self.load_storage_details(closing_stocks, warehouse_id, warehouse_name)
            # Consider adding the Invoiced Quantity + RTV quantity to sku storage cost dimensions


        log.info("Cost Dimensions End time %s" % str(datetime.now()))


    def bulk_create_cost_dimensions(self, cost_dimensions):
        with transaction.atomic(using='datahub'):
            batch_size = 1000
            for i in range(0, len(cost_dimensions), batch_size):
                batch = cost_dimensions[i:i + batch_size]
                CostDimensions.objects.using('datahub').bulk_create(batch)


    def load_grn_details(self, grn_processed, warehouse_id, warehouse_name):
        # now process this data and save in COST_DIMENSIONS table
        cost_dimensions = []
        for grn in grn_processed:

            uom, unit_cost, total_cost = self.get_cost(grn['sku__seller_id'], grn['sku__cost_category'], 'GRN', grn['total_quantity'], 1)
            obj = CostDimensions(
                    sku_id=grn['sku_id'],
                    sku_code=grn['sku__sku_code'],
                    seller_id=grn['sku__seller_id'],
                    seller_name=grn['sku__seller__supplier_id'],
                    cost_type='GRN',
                    cost_reference=grn['grn_number'],
                    cost_quantity=grn['total_quantity'],
                    cost_category=grn['sku__cost_category'],
                    uom=uom,
                    unit_cost=unit_cost,
                    conversion_factor=1,
                    cost_amount=total_cost,
                    cost_date=grn['max_updation_date'],
                    warehouse_id=warehouse_id,
                    warehouse_name=warehouse_name
                )
            cost_dimensions.append(obj)

        self.bulk_create_cost_dimensions(cost_dimensions)
        return cost_dimensions

    def load_order_details(self, seller_orders, warehouse_id, warehouse_name):
        # now process this data and save in COST_DIMENSIONS table
        cost_dimensions = []
        for order in seller_orders:
            uom, unit_cost, total_cost = self.get_cost(order['picklist__sku__seller_id'], order['picklist__sku__cost_category'], 'SaleOrders', order['total_quantity'], 1)
            obj = CostDimensions(
                    sku_id=order['picklist__sku_id'],
                    sku_code=order['picklist__sku__sku_code'],
                    seller_id=order['picklist__sku__seller_id'],
                    seller_name=order['picklist__sku__seller__supplier_id'],
                    cost_type='SaleOrders',
                    cost_reference=order['picklist__picklist_number'],
                    cost_quantity=order['total_quantity'],
                    cost_category=order['picklist__sku__cost_category'],
                    uom=uom,
                    unit_cost=unit_cost,
                    conversion_factor=1,
                    cost_amount=total_cost,
                    cost_date=order['max_updation_date'],
                    warehouse_id=warehouse_id,
                    warehouse_name=warehouse_name
                )
            cost_dimensions.append(obj)

        self.bulk_create_cost_dimensions(cost_dimensions)
        return cost_dimensions


    def load_rtv_details(self, return_to_vendors, warehouse_id, warehouse_name):
        # now process this data and save in COST_DIMENSIONS table
        cost_dimensions = []
        for rtv in return_to_vendors:
            uom, unit_cost, total_cost = self.get_cost(rtv['seller_po_summary__sku__seller_id'], rtv['seller_po_summary__sku__cost_category'], 'RTV', rtv['total_quantity'], 1)
            obj = CostDimensions(
                    sku_id=rtv['seller_po_summary__sku_id'],
                    sku_code=rtv['seller_po_summary__sku__sku_code'],
                    seller_id=rtv['seller_po_summary__sku__seller_id'],
                    seller_name=rtv['seller_po_summary__sku__seller__supplier_id'],
                    cost_type='RTV',
                    cost_reference=rtv['rtv_number'],
                    cost_quantity=rtv['total_quantity'],
                    cost_category=rtv['seller_po_summary__sku__cost_category'],
                    uom=uom,
                    unit_cost=unit_cost,
                    conversion_factor=1,
                    cost_amount=total_cost,
                    cost_date=rtv['max_updation_date'],
                    warehouse_id=warehouse_id,
                    warehouse_name=warehouse_name
                )
            cost_dimensions.append(obj)

        self.bulk_create_cost_dimensions(cost_dimensions)
        return cost_dimensions
    
    def load_storage_details(self, closing_stocks, warehouse_id, warehouse_name):
        # now process this data and save in COST_DIMENSIONS table
        cost_dimensions = []
        for stock in closing_stocks:
            uom, unit_cost, total_cost = self.get_cost(stock['stock__sku__seller_id'], stock['stock__sku__cost_category'], 'Storage', stock['total_quantity'], stock['stock__sku__volume'])
            conversion_factor = 1 if uom == "eaches" else stock['stock__sku__volume']
            obj = CostDimensions(
                    sku_id=stock['stock__sku_id'],
                    sku_code=stock['stock__sku__sku_code'],
                    seller_id=stock['stock__sku__seller_id'],
                    seller_name=stock['stock__sku__seller__supplier_id'],
                    cost_type='Storage',
                    cost_reference=stock['stock__location__location'],
                    cost_quantity=stock['total_quantity'],
                    cost_category=stock['stock__sku__cost_category'],
                    uom=uom,
                    unit_cost=unit_cost,
                    conversion_factor=conversion_factor,
                    cost_amount=total_cost,
                    cost_date=stock['max_updation_date'],
                    warehouse_id=warehouse_id,
                    warehouse_name=warehouse_name
                )
            cost_dimensions.append(obj)

        self.bulk_create_cost_dimensions(cost_dimensions)
        return cost_dimensions


    def handle(self, *args, **options):

        # get list of warehouse ids for matching timezone 
        warehouse_ids = UserProfile.objects.filter(timezone=options["timezone"], warehouse_level=3).values_list("user_id", flat=True)
        self.store_cost_dimensions(options["date"], options['timezone'], warehouse_ids)

