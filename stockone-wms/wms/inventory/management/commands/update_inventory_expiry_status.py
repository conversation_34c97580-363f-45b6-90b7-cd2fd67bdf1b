from django.core.management.base import BaseCommand
from inventory.models import StockDetail
from wms_base.models import User
import logging
import ast
import datetime
from datetime import timedelta

log = logging.getLogger(__name__)

def update_inventory_status(warehouse_ids, status_type):
    count = 0

    inventory_qs = StockDetail.objects.exclude(
        location__zone__segregation__in=['inbound_staging', 'outbound_staging', 'replenishment_staging_area', 'nte_staging_area']
    ).exclude(
        location__zone__storage_type='wip_area'
    ).exclude(
        batch_detail__expiry_date__isnull=True
    ).filter(
        sku__user__in=warehouse_ids, status = 1
    ).select_related('sku', 'batch_detail')

    bulk_update_stocks = []

    for inv in inventory_qs:
        expiry_date = inv.batch_detail.expiry_date
        if not expiry_date:
            continue

        try:
            if status_type == 'EXPIRED':
                calc1_date = expiry_date
            else:
                customer_shelf_life = inv.sku.customer_shelf_life.total_seconds() / timedelta(days=1).total_seconds()
                calc1_date = expiry_date - datetime.timedelta(days=customer_shelf_life)

            date_diff = calc1_date.replace(tzinfo=None) - datetime.datetime.utcnow()
        except Exception as e:
            try:
                date_diff = datetime.datetime.combine(calc1_date, datetime.time(0, 0)) - datetime.datetime.utcnow()
            except Exception:
                log.error("Critical date parsing failure for inventory ID %s: %s", inv.id, str(e))
                continue
            log.info("Error in calculating date difference for inventory ID %s: %s", inv.id, str(e))

        shelf_life = round(((date_diff.total_seconds() / 60) / 60) / 24, 2)

        if shelf_life <= 0:
            remarks = 'Automatic - Batch Expired' if status_type == 'EXPIRED' else 'Automatic - Below Shelf Life Threshold'
            new_status = 7 if status_type == 'EXPIRED' else 8

            inv.status = new_status
            inv.remarks = remarks
            bulk_update_stocks.append(inv)
            count += 1

    if bulk_update_stocks:
        StockDetail.objects.bulk_update(
            bulk_update_stocks, ['status', 'remarks']
        )

    return count

class Command(BaseCommand):

    help = 'Updates inventory status to EXPIRED or NEAR_EXPIRED based on batch expiration and shelf life'

    def add_arguments(self, parser):
        parser.add_argument('--extra_params', type=str, dest="extra_params")

    def handle(self, *args, **options):
        self.stdout.write('Starting inventory status update...')
        extra_data = ast.literal_eval(options['extra_params'])
        warehouses = extra_data.get('warehouse', [])
        warehouse_ids = list(User.objects.filter(username__in=warehouses).values_list('id', flat=True))

        try:
            expired_count = update_inventory_status(warehouse_ids, 'EXPIRED')
            near_expired_count = update_inventory_status(warehouse_ids, 'NEAR_EXPIRED')

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated inventory status:\n'
                    f'- {expired_count} items marked as EXPIRED\n'
                    f'- {near_expired_count} items marked as NEAR_EXPIRED'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error updating inventory status: {str(e)}')
            )