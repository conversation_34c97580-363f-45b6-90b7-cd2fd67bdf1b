#package imports
from datetime import date, timedelta
from json import dumps
from collections import OrderedDict

#django imports
from django.db.models import Sum, Q
from django.core.management import BaseCommand
from django.db.models.functions import Coalesce
from django.core.cache import cache
from django.db.models import <PERSON>loatField
from django.test.client import RequestFactory

#core imports
from core_operations.views.common.main import (
    get_misc_value, get_multiple_misc_values
)

#inventory models
from inventory.models import StockDetail, MinMaxPlanning
from inventory.views.locator.stock_detail import get_putaway_and_non_sellable_quantity

#inbound imports
from inbound.models import (
    PurchaseOrder, SKUSupplier, PendingLineItems
)
from inbound.views.purchase_order.purchase_order import PurchaseOrderSet

#outbound imports
from outbound.models import (
    OrderDetail, Picklist
)
from inbound.views.purchase_order.save_po import add_po
#wms base
from wms_base.models import User, UserAddresses

from django.http import QueryDict

def get_replenishment_sku_details(warehouse):
    '''
    Calculates the replenishment quantities based on minimum, maximum, and other quantities.

    Args:
        warehouse: The warehouse object for which to calculate the replenishment quantities.

    Returns:
        A tuple containing two dictionaries:
        - po_creation_data: A dictionary containing the purchase order creation data, where the keys are supplier IDs and the values are dictionaries with the following keys:
            - exp_delivery_date: The expected delivery date of the purchase order.
            - items: A list of dictionaries representing the items to be ordered, where each dictionary has the following keys:
                - sku: The SKU code of the item.
                - order_quantity: The quantity to be ordered for the item.
            - supplier_type: The type of the supplier.
        - min_max_creation_list: A list of dictionaries representing the suggested replenishment quantities, where each dictionary has the following keys:
            - sku_id: The ID of the SKU.
            - supplier_id: The ID of the supplier.
            - max_norm_qty: The maximum normalized quantity for the SKU.
            - min_norm_qty: The minimum normalized quantity for the SKU.
            - sellable_qty: The sellable quantity of the SKU.
            - bulk_qty: The bulk quantity of the SKU.
            - wip_qty: The work-in-progress quantity of the SKU.
            - putaway_qty: The pending putaway quantity of the SKU.
            - open_order_qty: The open order quantity of the SKU.
            - suggested_qty: The suggested replenishment quantity for the SKU.
            - moq: The minimum order quantity for the SKU.
            - warehouse_id: The ID of the warehouse.
            - open_po_qty: The open purchase order quantity for the SKU.
    '''
    po_creation_data, min_max_creation_list = {}, []
    auto_po_switch = get_misc_value('min_max_po_type', warehouse.id)
    if auto_po_switch != "false":

        #Bulk Quantity, Sellable Quantity, WIP Quantity
        sellable_quantities = list(StockDetail.objects.filter(sku__user = warehouse.id, quantity__gt = 0).\
            values('sku__sku_code').annotate(
            bulk_quantity = Coalesce(
            Sum('quantity', filter = Q(location__zone__segregation='non_sellable', \
                                        location__zone__storage_type='bulk_area'
                                    )), 0, output_field=FloatField()
                                ),
            sellable_quantity = Coalesce(
            Sum('quantity', filter = Q(location__zone__segregation='sellable')), 0, \
                output_field=FloatField()
                        ),
            wip_quantity = Coalesce(
            Sum('quantity', filter = Q(location__zone__segregation='non_sellable', \
                            location__zone__storage_type='wip_area')), 0, \
                            output_field=FloatField()
                        )
            )
        )

        #Preparing Quantity dict with key as sku_code
        sellable_quantity_dict = {}
        for stock in sellable_quantities:
            sku_code = stock.get('sku__sku_code')
            sellable_quantity_dict[sku_code] = {
                'bulk_quantity': stock.get('bulk_quantity',0),
                'sellable_quantity': stock.get('sellable_quantity',0),
                'wip_quantity': stock.get('wip_quantity')
            }
        
        #Get Open Order Quantity
        open_order_quantity = dict(OrderDetail.objects.exclude(
            order_type__in=['Standard JO','Non Standard JO','Returnable JO']).\
            filter(user = warehouse.id, quantity__gt=0, status=1).\
            values_list('sku__sku_code').annotate(Sum('quantity')
            )
        )

        #Reserved Quantity
        reserved_quantity = dict(Picklist.objects.exclude(
            order__order_type__in=['Standard JO','Non Standard JO','Returnable JO']
            ).filter(status ='open', user = warehouse.id).\
            values_list('stock__sku__sku_code').annotate(Sum('reserved_quantity')
            )
        )
        
        #Open Purchase Order Quantity
        open_po_quantity = dict(PurchaseOrder.objects.exclude(status = 'location-assigned').\
                                filter(open_po__sku__user = warehouse.id).\
                                values_list('open_po__sku__sku_code').\
                                annotate(open_po_qty = Sum('open_po__order_quantity') - Sum('received_quantity')))

        #Open Pending PO Approval Quantity
        open_pending_approval_quantity = dict(PendingLineItems.objects.filter(
            pending_po__final_status__in = ['pending','reverted', 'approved'], \
            pending_po__open_po_id__isnull = True, sku__user = warehouse.id
        ).values_list('sku__sku_code').annotate(Sum('quantity')))

        #Pending Putaway Quantities
        (putaway_quantity, return_putaway,
        pull_to_locate_quantity,
        non_sellable_quantity,jo_pending_putaway) = get_putaway_and_non_sellable_quantity([warehouse.id])
        
        #If one sku mapped to multiple suppliers
        supplier_sku_mapped_data = {}
        supplier_data = list(SKUSupplier.objects.filter(
            sku__user = warehouse.id, sku__status = 1, sku__max_norm_quantity__gt = 0
        ).values('sku','supplier','sku__sku_code','sku__max_norm_quantity',\
                'sku__threshold_quantity','supplier__supplier_id','moq',
                'lead_time','preference','supplier__supplier_type'))
        
        #Getting Supplier Based on Preference
        for supplier in supplier_data:
            sku_code = supplier.get('sku__sku_code')
            if sku_code in supplier_sku_mapped_data:
                if supplier_sku_mapped_data.get(sku_code).get('preference') >= supplier.get('preference',0):
                    del supplier_sku_mapped_data[sku_code]
                    supplier_sku_mapped_data[sku_code] = supplier
            else:
                supplier_sku_mapped_data[sku_code] = supplier
        
        #Main Loop
        for supplier_sku_data in supplier_sku_mapped_data.values():
            sku_code = supplier_sku_data.get('sku__sku_code')
            sellable_quantity = sellable_quantity_dict.get(sku_code, {}).get('sellable_quantity',0)
            bulk_quantity = sellable_quantity_dict.get(sku_code, {}).get('bulk_quantity', 0)
            wip_quantity = sellable_quantity_dict.get(sku_code,{}).get('wip_quantity', 0)

            open_order_qty = open_order_quantity.get(sku_code,0)
            reserved_qty = reserved_quantity.get(sku_code,0)

            putaway_qty = putaway_quantity.get(sku_code,0)
            return_putaway_qty = return_putaway.get(sku_code,0)
            pull_to_locate_qty = pull_to_locate_quantity.get(sku_code,0)
            open_po_qty = open_po_quantity.get(sku_code,0)

            pen_approval_po_qty = open_pending_approval_quantity.get(sku_code, 0)
            max_norm_qty = supplier_sku_data.get('sku__max_norm_quantity',0)
            min_norm = supplier_sku_data.get('sku__threshold_quantity',0)
            moq = supplier_sku_data.get('moq',0)

            lead_time = supplier_sku_data.get('lead_time',0)
            supplier_type = supplier_sku_data.get('supplier__supplier_type', '')
            #Net Qty
            net_qty = (sellable_quantity + bulk_quantity + wip_quantity + putaway_qty + return_putaway_qty + pull_to_locate_qty) - (open_order_qty + reserved_qty)

            #Replenishment Qty
            replenishment_qty = max_norm_qty - net_qty - open_po_qty - pen_approval_po_qty
            exp_delivery_date = date.today()
            if lead_time:
                exp_delivery_date = exp_delivery_date + timedelta(days=lead_time)
            exp_delivery_date = exp_delivery_date.strftime("%m/%d/%Y")

            all_pending_putaway = return_putaway_qty + pull_to_locate_qty + putaway_qty
            open_ordered_qty = open_order_qty + reserved_qty
            open_pen_qty = open_po_qty + pen_approval_po_qty

            if net_qty < min_norm and replenishment_qty > 0:
                replenishment_qty = max(replenishment_qty,moq)
                supplier_id = (supplier_sku_data.get('supplier__supplier_id'))
                suggested_dict = {
                    'sku_id': supplier_sku_data.get('sku'), 
                    'supplier_id':supplier_sku_data.get('supplier'), 
                    'max_norm_qty': max_norm_qty, 
                    'min_norm_qty': min_norm,
                    'sellable_qty': sellable_quantity, 
                    'bulk_qty': bulk_quantity, 
                    'wip_qty': wip_quantity,
                    'putaway_qty': all_pending_putaway, 
                    'open_order_qty': open_ordered_qty, 
                    'suggested_qty': replenishment_qty, 
                    'moq': moq,
                    'warehouse_id': warehouse.id, 
                    'open_po_qty':open_pen_qty
                }
                min_max_creation_list.append(suggested_dict)
                if supplier_id in po_creation_data:
                    po_creation_data[supplier_id].get('items').append({
                        'sku' : sku_code, 'order_quantity': replenishment_qty
                    })
                else:
                    po_creation_data[supplier_id] = {
                        'exp_delivery_date': exp_delivery_date, 
                        'items': [{'sku' : sku_code, 'order_quantity': replenishment_qty}],
                        'supplier_type': supplier_type
                        }

    return po_creation_data, min_max_creation_list

def additional_data_for_po_creation(sku_list, supplier_id_list, warehouse):
    """
    Get additional data for purchase order creation.

    Args:
        sku_list: A list of SKU codes.
        supplier_id: The ID of the supplier.
        warehouse: The warehouse object.

    Returns:
        A dictionary containing the additional data for purchase order creation.
    """

    # get configured batch attributes
    configured_batch_attributes = get_multiple_misc_values(['additional_batch_attributes'], warehouse.id).get('additional_batch_attributes', '').split(',')
    additional_info_dict = {}
    if sku_list and supplier_id_list:
        # fetch price from supplier sku mapping and mrp from sku master
        supplier_sku_data = list(SKUSupplier.objects.filter(
            sku__user=warehouse.id, sku__sku_code__in=sku_list, supplier__supplier_id__in = supplier_id_list, preference=1).
            values('sku__sku_code', 'sku__batch_based', 'sku__mrp', 'price', 'supplier__supplier_id'))

        for data in supplier_sku_data:
            key = ( data.get('sku__sku_code'),  data.get('supplier__supplier_id'))
            additional_info_dict.setdefault(key, {})  # Set default value if key doesn't exist
            additional_info_dict[key]['price'] = data.get('price')
            additional_info_dict[key]['mrp'] = data.get('sku__mrp') if data.get('sku__batch_based') and 'mrp' in configured_batch_attributes else ''

    return additional_info_dict


def create_or_update_min_max_planning(warehouse, manual_run, min_max_creation_list):
    min_max_creation_objs, update_min_max_list = [],[]
    for creation_dict in min_max_creation_list:
        run_type = 1 if manual_run else 0 #run_type 1 for manual run else 0
        status = 1 if manual_run else 0 #open status for manual run else closed 

        creation_dict.update({'run_type': run_type, 'status': status, 'account_id': warehouse.userprofile.id})
        filter_dict = {'supplier_id': creation_dict.get('supplier_id'), \
                        'sku_id': creation_dict.get('sku_id'), 'status': status}

        #Checking obj based on status
        min_max_obj = MinMaxPlanning.objects.filter(**filter_dict)
        if min_max_obj.exists():
            for obj in min_max_obj:
                obj.status = 0
                update_min_max_list.append(obj)
        else:
            min_max_creation_objs.append(MinMaxPlanning(**creation_dict))

        #Bulk Creation and Updation of MinMaxPlanning
    if min_max_creation_objs:
        MinMaxPlanning.objects.bulk_create_with_rounding(min_max_creation_objs)
    if update_min_max_list:
        MinMaxPlanning.objects.bulk_update_with_rounding(
            update_min_max_list, ['status'], batch_size = 100
        )

def create_po_for_replenishment_qty(warehouse, po_creation_data, ship_to, response=None):
    """
    Create purchase orders for replenishment quantity.

    Args:
        warehouse (object): The warehouse object.
        po_creation_data (dict): A dictionary containing the purchase order creation data.
        ship_to (str): The shipping address.

    Returns:
        None
    """
    misc_types = ['external_po_type', 'external_sto_warehouses']
    external_misc_data = get_multiple_misc_values(misc_types, warehouse.id)

    for supplier, data_dict in po_creation_data.items():
        supplier_dict =  OrderedDict()
        supplier_dict['supplier_id'] = supplier
        supplier_dict['warehouse'] = warehouse.username
        supplier_dict['po_delivery_date'] = data_dict.get('exp_delivery_date')
        supplier_dict['items'] = data_dict.get('items')
        supplier_dict['ship_to'] = ship_to

        # To Skip PO Extra Fields Validations
        supplier_dict['skip_extra_fields_validation'] = True

        # To Create Sale Order for Replenishment Qty
        supplier_dict['create_sale_order'] = True

        # Setting default PO type based on supplier type
        default_po_type = 'stocktransfer'
        external_sto_warehouses = external_misc_data.get('external_sto_warehouses') or ''
        if supplier in external_sto_warehouses.split(','):
            default_po_type = external_misc_data.get('external_po_type') or 'stocktransfer'

        if data_dict['supplier_type'] == 'internal':
            supplier_dict['po_type'] = default_po_type

        request = RequestFactory
        request.user = warehouse
        request.warehouse = warehouse
        request.method= 'POST'
        request.GET = {}
        request.META = {'csrf_token': 'NOTPROVIDED'}
        request.body = dumps(supplier_dict)
        po_set = PurchaseOrderSet()
        po_set.request = request
        resp = po_set.post()
        if response:
            return resp

def save_po_for_replenishment_qty(warehouse, po_creation_data, ship_to, sku_list, supplier_id_list):
    """
    Save purchase orders for replenishment quantity.

    Args:
        warehouse: The warehouse object representing the user.
        po_creation_data: A dictionary containing the purchase order creation data.
        ship_to: The shipping address for the purchase order.

    Returns:
        None
    """
    # fetch price and mrp for saving PO
    additional_data = additional_data_for_po_creation(sku_list, supplier_id_list, warehouse)
    query_dict = QueryDict('', mutable=True)
    for supplier_id, values in po_creation_data.items():
        for key, value in values.items():
            if key == 'items':
                for item in value:
                    addtional_info_key = (item['sku'], supplier_id)
                    query_dict.appendlist('sku', item['sku'])
                    query_dict.appendlist('wms_code', item['sku'])
                    query_dict.appendlist('min_max_id', item['min_max_id'])
                    query_dict.appendlist('order_quantity', item['order_quantity'])
                    query_dict.appendlist('price', additional_data.get(addtional_info_key,{}).get('price',''))
                    query_dict.appendlist('mrp', additional_data.get(addtional_info_key,{}).get('mrp',''))
            else:
                query_dict.appendlist(key, value)
        query_dict.appendlist('supplier_id', supplier_id)
        query_dict.appendlist('ship_to', ship_to)
        po_request = RequestFactory
        po_request.user = warehouse
        po_request.warehouse = warehouse
        po_request.method= 'POST'
        po_request.META = {'csrf_token': 'NOTPROVIDED'}
        po_request.POST = query_dict
        add_po(po_request)

class Command(BaseCommand):
    """
        Min Max Based Auto PO
    """
    help = 'Min Max Based Auto PO'

    def add_arguments(self, parser):
        parser.add_argument('--user', type=str, dest="username")
        parser.add_argument('--manual_run', type=str, dest="manual_run")

    def handle(self, *args, **options):
        manual_run = options.get('manual_run', False)
        username = options.get('username')
        users = User.objects.filter(username__in=[username])
        cache_status = False
        for warehouse in users:
            if not manual_run:
                cache_status = cache.add(warehouse.username, "True", timeout=20)
            if manual_run or cache_status:
                supplier_level_po_dict, ship_to, po_response, min_max_creation_objs, update_min_max_list = [], '', {}, [], []
                #Calculates Replenshment Qty for PO Creation
                #po_creation_data = {'exp_delivery_date': exp_delivery_date, 'items': [{'sku' : sku_code, 'order_quantity': replenishment_qty}]}

                po_creation_data, min_max_creation_list = get_replenishment_sku_details(warehouse)
                user_address = UserAddresses.objects.filter(user=warehouse.id)[0]
                if user_address:
                    ship_to = ('%s %s, %s' % (user_address.address_name, user_address.address, user_address.pincode))
                if ship_to and po_creation_data and not manual_run:
                    #If Not Manual Run then creating PO
                    create_po_for_replenishment_qty(warehouse, po_creation_data, ship_to)

                #Creates or Updates Min Max Planning
                create_or_update_min_max_planning(warehouse, manual_run, min_max_creation_list)

            #Cache Deletion
            if not manual_run:
                cache.delete(warehouse.username)

            


