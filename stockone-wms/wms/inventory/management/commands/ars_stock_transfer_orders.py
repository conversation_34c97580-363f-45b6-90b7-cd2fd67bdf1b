#wms base
from wms_base.wms_utils import init_logger

#inventory imports
from .base_stock_transfer_orders import BaseStockTransferCommand
from inventory.views.replenishment.ars_replenishment import ARSReplenishment


class Command(BaseStockTransferCommand):
    """
    ARS Stock Transfer Orders

    This command automates the process of creating ARS (Automated Replenishment System)
    stock transfer orders between warehouses using ABC classification for inventory management.
    """

    help = "ARS Stock Transfer Orders"

    def initialize_logger(self):
        """
        Initialize the logger for ARS stock transfer orders
        """
        return init_logger('logs/ars_stock_transfer_orders.log')

    def get_command_name(self):
        """
        Get the name of the command for logging purposes
        """
        return "ARS Stock Transfer Orders"

    def abc_calculation(self):
        self.process_abc_calculation(ARSReplenishment)