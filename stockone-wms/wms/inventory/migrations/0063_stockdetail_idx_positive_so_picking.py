# Generated by Django 4.2.20 on 2025-05-21 05:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0062_zonemaster_is_check_digit'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='stockdetail',
            index=models.Index(condition=models.Q(('quantity__gt', 0)), fields=['sku', 'receipt_type', 'transact_number'], name='idx_positive_so_picking'),
        ),
    ]
