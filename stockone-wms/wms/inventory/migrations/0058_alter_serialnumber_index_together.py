# Generated by Django 4.2.20 on 2025-05-02 09:17

from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0130_alter_userzonemapping_zone'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0057_alter_historicalserialnumbermapping_json_data_and_more'),
    ]

    operations = [
        migrations.AlterIndexTogether(
            name='serialnumber',
            index_together={('warehouse', 'status', 'stock'), ('sku', 'location'), ('warehouse', 'status'), ('warehouse', 'serial_number', 'status'), ('status', 'warehouse', 'location'), ('warehouse', 'sku', 'status'), ('warehouse', 'serial_number', 'status', 'stock')},
        ),
    ]
