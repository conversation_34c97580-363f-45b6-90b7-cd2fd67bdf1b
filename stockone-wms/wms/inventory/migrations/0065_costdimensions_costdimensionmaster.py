# Generated by Django 4.2.20 on 2025-05-26 14:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inbound', '0046_alter_historicalopenpo_json_data_and_more'),
        ('inventory', '0064_alter_historicalstockdetail_status_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CostDimensions',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('sku_id', models.IntegerField()),
                ('seller_id', models.IntegerField()),
                ('warehouse_id', models.IntegerField()),
                ('sku_code', models.CharField(default='', max_length=64)),
                ('seller_name', models.CharField(default='', max_length=255)),
                ('warehouse_name', models.CharField(default='', max_length=255)),
                ('cost_type', models.CharField(choices=[('SaleOrders', 'SaleOrders'), ('GRN', 'GRN'), ('RTV', 'RTV'), ('Storage', 'Storage')], default='SaleOrders', max_length=32)),
                ('cost_reference', models.CharField(default='', max_length=256)),
                ('cost_quantity', models.FloatField(default=0)),
                ('cost_category', models.CharField(default='default', max_length=64)),
                ('uom', models.CharField(choices=[('Eaches', 'Eaches'), ('Cubic Centimeter', 'Cubic Centimeter')], default='Eaches', max_length=64)),
                ('unit_cost', models.FloatField(default=0)),
                ('conversion_factor', models.FloatField(default=0)),
                ('cost_amount', models.FloatField(default=0)),
                ('cost_date', models.DateTimeField()),
            ],
            options={
                'db_table': 'COST_DIMENSIONS',
                'unique_together': {('sku_id', 'seller_id', 'warehouse_id', 'cost_type', 'cost_reference', 'cost_date')},
                'index_together': {('seller_id', 'cost_type', 'cost_reference'), ('cost_type', 'cost_reference', 'cost_date'), ('sku_id', 'seller_id', 'cost_type')},
            },
        ),
        migrations.CreateModel(
            name='CostDimensionMaster',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('sku_cost_category', models.CharField(default='default', max_length=64)),
                ('sale_orders_cost', models.FloatField(default=0)),
                ('grn_cost', models.FloatField(default=0)),
                ('rtv_cost', models.FloatField(default=0)),
                ('storage_cost', models.FloatField(default=0)),
                ('storage_cost_unit', models.CharField(choices=[('Eaches', 'Eaches'), ('Cubic Centimeter', 'Cubic Centimeter')], default='Eaches', max_length=64)),
                ('status', models.CharField(choices=[('Inactive', 'Inactive'), ('Active', 'Active')], default='Active')),
                ('seller', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inbound.suppliermaster')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'COST_DIMENSION_MASTER',
                'unique_together': {('seller_id', 'warehouse_id', 'sku_cost_category')},
            },
        ),
    ]
