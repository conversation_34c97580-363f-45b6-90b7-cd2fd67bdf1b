# Generated by Django 4.2.20 on 2025-04-28 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0056_historicalskupackmaster'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='historicalserialnumbermapping',
            name='json_data',
            field=models.J<PERSON><PERSON><PERSON>(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalskupackmaster',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='locationtype',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='serialnumber',
            name='json_data',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='serialnumbermapping',
            name='json_data',
            field=models.J<PERSON><PERSON><PERSON>(blank=True, default=dict, null=True),
        ),
        migrations.Alt<PERSON><PERSON><PERSON>(
            model_name='serialnumbertransaction',
            name='json_data',
            field=models.J<PERSON><PERSON><PERSON>(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='skupackmaster',
            name='json_data',
            field=models.J<PERSON>NField(blank=True, default=dict, null=True),
        ),
    ]
