# Generated by Django 4.2.20 on 2025-05-22 10:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0063_stockdetail_idx_positive_so_picking'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalstockdetail',
            name='status',
            field=models.IntegerField(choices=[(0, 'OnHold'), (1, 'Available'), (2, 'Consumed'), (3, 'Reserved'), (4, 'Rejected'), (5, 'Obsolete'), (6, 'Blocked'), (7, 'Expired'), (8, 'Near_Expired')], default=1),
        ),
        migrations.AlterField(
            model_name='stockdetail',
            name='status',
            field=models.IntegerField(choices=[(0, 'OnHold'), (1, 'Available'), (2, 'Consumed'), (3, 'Reserved'), (4, 'Rejected'), (5, 'Obsolete'), (6, 'Blocked'), (7, 'Expired'), (8, 'Near_Expired')], default=1),
        ),
    ]
