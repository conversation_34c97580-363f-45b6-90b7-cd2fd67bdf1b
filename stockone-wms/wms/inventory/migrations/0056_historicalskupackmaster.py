# Generated by Django 4.2.20 on 2025-04-23 11:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0035_companymaster_default_roles_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0126_alter_userintegrationapis_access_token'),
        ('inventory', '0055_cyclecount_lpn_number_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalSKUPackMaster',
            fields=[
                ('id', models.IntegerField(blank=True, db_index=True)),
                ('pack_id', models.CharField(default='', max_length=32)),
                ('pack_quantity', models.FloatField(default=0)),
                ('purchase_uom', models.IntegerField(default=0)),
                ('sales_uom', models.IntegerField(default=0)),
                ('status', models.IntegerField(default=1)),
                ('json_data', models.J<PERSON>NField(blank=True, default={}, null=True)),
                ('creation_date', models.DateTimeField(blank=True, editable=False)),
                ('updation_date', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='wms_base.userprofile')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='core.skumaster')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical sku pack master',
                'verbose_name_plural': 'historical sku pack masters',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
