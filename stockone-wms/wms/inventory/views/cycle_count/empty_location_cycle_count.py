# Package imports
import datetime
from json import loads

# Rest framework imports
from rest_framework import status

# Django imports
from django.http import JsonResponse
from django.db.models import OuterRef, Exists, Q

# WMS base imports
from wms_base.wms_utils import init_logger

# Core common functions
from core_operations.views.common.main import WMSListView, get_user_ip, get_incremental, get_misc_value

# Inventory imports
from inventory.models import LocationMaster, StockDetail, CycleCount

log = init_logger('logs/empty_location_cycle_count.log')

class EmptyLocationCycleCountCreateView(WMSListView):
    """
        API view to create cycle counts for empty locations in specified zones.
        This view allows users to generate cycle counts for locations that do not have any stock.
        It cancels any existing empty location cycle counts for the user before creating new ones.
    """
    def fetch_existing_empty_location_cycles(self):
        """
        fetch existing empty location cycle counts for this warehouse/user.
        """
        self.skip_locations = list(CycleCount.objects.filter(
            run_type='empty_location',
            status=1,
            location__zone__zone__in=self.zones,
            location__zone__user=self.warehouse.id
        ).values_list('location__location', flat=True))
        
    def get_empty_locations(self):
        """
        Get all empty locations in the given zones for this warehouse/user.
        """
        locations = LocationMaster.objects.filter(
            zone__zone__in=self.zones, status=1, zone__user=self.warehouse.id
        ).exclude(
            location__in = self.skip_locations
        )
        if not locations.exists():
            self.empty_locations = []
            return
        stock_subquery = StockDetail.objects.filter(
            location=OuterRef('pk'),
            quantity__gt=0,
            sku__user=self.warehouse.id
        ).filter(
            ~Q(status=2),
            ~Q(location__zone__segregation__in=["inbound_staging", "outbound_staging"]),
            ~Q(location__zone__storage_type__in =  ['wip_area', 'replenishment_staging_area', 'nte_staging_area'])
        )
        
        self.empty_locations = list(
            locations.annotate(has_stock=Exists(stock_subquery))
            .filter(has_stock=False)
            .values_list('id', flat=True)
        )

    def bulk_create_cycle_counts(self):
        """
        Bulk create CycleCount records for the given empty locations.
        """
        cycle_id = get_incremental(self.warehouse, 'cycle_id', '', False, len(self.empty_locations))
        cycle_counts = []
        for location in self.empty_locations:
            cycle_counts.append(
                CycleCount(
                    account_id=self.warehouse.userprofile.id,
                    location_id=location,
                    status=1,
                    run_type='empty_location',
                    quantity=0,
                    cycle=cycle_id,
                    seen_quantity=0,
                    json_data={
                        'requested_user': self.request.user.username,
                        'done_by': 'User Generated'                    
                    }
                )
            )
            cycle_id += 1
        CycleCount.objects.bulk_create(cycle_counts)

    def post(self, *args, **kwargs):
        """
            Create empty location cycle counts for the specified zones.
            Cancels any existing empty location cycle counts for the user before creating new ones.
            Expects a JSON body with 'zones' (list of zone IDs).
        """
        try:
            self.set_user_credientials()
            params = loads(self.request.body)
            log_message = (
                "Request Create Empty Location Cycle Count Username %s, IPAddress %s and params are %s"
                % (str(self.warehouse.username), str(get_user_ip(self.request)), str(params))
            )
            log.info(log_message)

            self.zones = params.get('zones', [])
            if self.zones and isinstance(self.zones, list) and len(self.zones) > 0:
                # fetch existing empty location cycle counts
                self.fetch_existing_empty_location_cycles()

                # Get empty locations in the specified zones
                self.get_empty_locations()
                if not self.empty_locations:
                    return JsonResponse({'message': 'No empty locations found in the specified zones', 'errors': None}, status=status.HTTP_404_NOT_FOUND)
                
                # Bulk create cycle counts for empty locations
                self.bulk_create_cycle_counts()
                return JsonResponse({'message': 'Empty location cycle counts created successfully', 'errors': None}, status=status.HTTP_201_CREATED)
            
            else:
                return JsonResponse({'message': 'Zones are required in list and must be a non-empty list', 'errors': None}, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            log.error(f"Error creating empty location cycle counts: {str(e)}")
            return JsonResponse({'message': 'An error occurred while creating empty location cycle counts', 'errors': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, *args, **kwargs):
        """
        Update status and remarks for CycleCount records with given cycle_id,
        filtered by run_type='empty_location' and status=1.
        """
        try:
            self.set_user_credientials()
            params = loads(self.request.body)
            log_message = (
                "Request Update Empty Location Cycle Count Username %s, IPAddress %s and params are %s"
                % (str(self.warehouse.username), str(get_user_ip(self.request)), str(params))
            )
            log.info(log_message)
            cycle_id = params.get('id', '')
            new_remarks = params.get('remarks', '')

            if not cycle_id or new_remarks is None:
                return JsonResponse({'message': 'cycle_id and remarks are required', 'errors': None}, status=status.HTTP_400_BAD_REQUEST)
            if new_remarks and len(new_remarks) > 255:
                return JsonResponse({'message': 'Remarks cannot exceed 255 characters', 'errors': None}, status=status.HTTP_400_BAD_REQUEST)
        
            # Filter and update CycleCount records
            CycleCount.objects.filter(
                id=cycle_id,
                run_type='empty_location',
                status=1,
                location__zone__user=self.warehouse.id
            ).update(
                status=3,
                remarks=new_remarks,
                updation_date=datetime.datetime.now()
            )

            return JsonResponse({
                'message': 'CycleCount records updated successfully',
                'errors': None
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            log.error(f"Error updating empty location cycle counts: {str(e)}")
            return JsonResponse({'message': 'An error occurred while updating empty location cycle counts', 'errors': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, *args, **kwargs):
        """
        Optimized: List all empty_location cycle counts with status=1.
        Returns one record per (cycle, zone, location) combination.
        """
        try:
            self.set_user_credientials()
            request_data = self.request.GET
            user_sub_zone_mapping = get_misc_value('user_sub_zone_mapping', self.warehouse.id)
            filters = {
                'run_type': 'empty_location',
                'status': 1,
                'location__zone__user': self.warehouse.id
            }
            zone = request_data.get('zone', None)
            location = request_data.get('location', None)
            if zone:
                if user_sub_zone_mapping == 'true':
                    filters['location__sub_zone__zone__icontains'] = zone
                else:
                    filters['location__zone__zone__icontains'] = zone
            
            if location:
                filters['location__location__icontains'] = location

            if user_sub_zone_mapping == 'true':
                values_list = [
                    'cycle', 'id', 'location__location', 'location__sub_zone__zone'
                ]
                order_by_fields = ['cycle', 'location__sub_zone__zone', 'location__location']
            else:
                values_list = [
                    'cycle', 'id', 'location__location', 'location__zone__zone'
                ]
                order_by_fields = ['cycle', 'location__zone__zone', 'location__location']
            
            cycle_objs = (
                CycleCount.objects
                .filter(**filters)
                .select_related('location__zone')
                .values(*values_list)
                .order_by(*order_by_fields)
            )
            seen = set()
            results = []

            for cc in cycle_objs:
                cycle = cc['cycle']
                location = cc['location__location']
                if user_sub_zone_mapping == 'true':
                    zone = cc['location__sub_zone__zone']
                else:
                    zone = cc['location__zone__zone']
                key = (cycle, zone, location)

                if key not in seen:
                    seen.add(key)
                    results.append({
                        'id': cc['id'],
                        'cycle_id': cycle,
                        'zone': zone,
                        'location': location
                    })

            response_data = {
                'page_info': {},
                'status': 200,
                'messages': 'Success',
                'data': results
            }
            return JsonResponse(response_data)
        except Exception as e:
            log.error(f"Error fetching empty location cycle counts: {str(e)}")
            return JsonResponse({
                'error': 'An error occurred while fetching empty location cycle counts'
            }, status=500)