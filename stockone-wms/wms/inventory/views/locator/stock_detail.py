#package imports
from copy import deepcopy
from collections import OrderedDict, defaultdict
from json import dumps, loads
from itertools import chain

import datetime
import pytz
import json
import math
import os
import pandas as pd
from typing import Union
from django.utils import timezone
from decimal import Decimal
import contextlib
from django.test import RequestFactory

#django imports
from django.http import HttpResponse, JsonResponse
from django.db.models import Q, Sum, Max, F
from rest_framework import serializers, viewsets
from django.db import transaction
from functools import reduce
import operator
from django.views.generic import ListView
from dateutil import parser
from datetime import timedelta, time
#wms imports
from wms_base.models import User, UserGroups, UserProfile

#inventory models
from inventory.models import (
    StockDetail,SerialNumberMapping, LocationMaster,
    CycleCount, SKUPackMaster, BatchDetail, ZoneMaster,
    INVENTORY_CHOICES, BatchKey, ClosingStock
)
from inventory.serializers.stock import StockReversalSerializer, StockSerializer, StockStatusUpdateDataSerializer
from inventory.views.locator.stock_summary import get_sku_stock_summary

#inbound imports
from inbound.models import (
    PurchaseOrder, PendingLineItems, POLocation, ASNSummary
)

#production imports
from production.models import BOMMaster

#outbound imports
from outbound.models import (
    OrderDetail, Picklist, OrderTypeZoneMapping, StockAllocation
)

#core operations
from core.models import (
    SKUMaster, QCConfiguration, PalletDetail,
    UserPrefixes
)

import ast

from reports.models import (Report, GeneratedReport)

from core_operations.views.common.main import (
    get_user_time_zone, get_misc_value,
    get_local_date_known_timezone, get_uom_with_multi_skus,
    get_exclude_zones, get_exclude_locations,
    scroll_data, get_sku_ean_numbers,
    get_multiple_misc_values, get_misc_object,
    frame_datatable_column_filter,
    get_user_prefix_incremental, get_warehouse, WMSListView,
    get_decimal_value, truncate_float, get_company_id,
    get_uom_decimals, get_incremental, get_custom_sku_attributes_and_values,
    create_or_update_master_attributes, get_extra_attributes
)
from core_operations.views.common.utils import parse_date_format
from wms_base.wms_utils import init_logger, reverse_stock_choice_mapping, folder_check
from wms_base.models import User

from inbound.views.common.constants import INBOUND_STAGING_LANES_MAPPING, SERIAL_STAGING_LANES_MAPPING, RECEIPT_TYPE_MAPPING
from inventory.management.commands.stock_ledger_async import (
    prepare_custom_stock_ledger_data
)

from django.core.files import File

from inventory.views.locator.validations import (
    get_sku_codes, get_sku_master_dict, validate_dates
)
from inventory.views.serial_numbers.serial_number import SerialNumberMixin
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
from django.contrib.postgres.aggregates import StringAgg, ArrayAgg

date_format = '%Y-%m-%d'
date_time_format = '%Y-%m-%d %H:%M:%S%z'

log = init_logger('logs/stock_locator.log')

StockDetailHeaders = {
    'SKU Code' :'SKU Code', 'Product Description' :'SKU Description',
    'SKU Category':'SKU Category', 'Batch Number':'Batch Number', 'Manufactured Date':'Manufactured Date',
    'Expiry Date':'Expiry Date', 'MRP':'MRP', 'Weight':'Weight',
    'Zone':'Zone', 'Location':'Location', 'Reserved Quantity':'Reserved Quantity',
    'Base Uom Quantity': 'Quantity', 'Procurement Price':'Procurement Price',
    'Procurement Tax Percent':'Procurement Tax Percent', 'Carton ID': 'Carton ID',
    'Stock Value':'Stock Value', 'Receipt Number':'Receipt Number',
    'Receipt Date':'Receipt Date','Receipt Type':'Transaction Type',
    'Creation Date':'Transaction Date', 'Serial Numbers':'Serial Numbers',
    'Status': 'Status'
}
choice_mapping = dict(INVENTORY_CHOICES)

def format_date(timezone, obj):
    if obj:
        return get_local_date_known_timezone(
            timezone, obj, send_date=True
        ).strftime(date_format)
    return ''

def get_data_for_batch_level_stock():
    """
    Retrieves data for batch level stock.

    Returns:
        dict: A dictionary containing the data for batch level stock. The keys represent the field names and the values represent the corresponding field values.
    """
    return {"sku_code":"sku__sku_code", "sku_desc":"sku__sku_desc", "sku_category":"sku__sku_category",
            "batch_no":"batch_detail__batch_no", "batch_reference" : "batch_detail__batch_reference", "zone":"location__zone__zone", "location":"location__location",
            "vendor_batch_no":"batch_detail__vendor_batch_no", "inspection_lot_number":"batch_detail__inspection_lot_number",
            "bin_number":"carton__bin_number", "supplier_name":"supplier__name", "supplier_id":"supplier__supplier_id",
            "mrp": "batch_detail__mrp", "weight": "batch_detail__weight",
            "best_before_date":"batch_detail__best_before_date", "reevaluation_date":"batch_detail__reevaluation_date",
            "retest_date":"batch_detail__retest_date", 'expiry_date':"batch_detail__expiry_date",
            "manufactured_date":"batch_detail__manufactured_date"}

def get_order_data_for_batch_level_stock(order_data_dict, sort_by_column, sort_type):
    """
    Get the order data for batch level stock.

    Args:
        order_data_dict (dict): A dictionary containing order data.
        sort_by_column (str): The column to sort the order data by.
        sort_type (str): The type of sorting to apply.

    Returns:
        str: The sorted order data.

    """
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column

    if sort_type == '1':
        order_data = '-%s' % order_data

    return order_data

def get_custom_filter_dict_for_batch_level_stock():
    """
    Returns a dictionary containing custom filter criteria for batch level stock.

    Returns:
        dict: A dictionary with filter criteria for batch level stock. The keys represent the field names to filter on,
              and the values represent the corresponding lookup expressions.
    """
    return {
        "sku_code__icontains": "sku__sku_code",
        "sku_desc__icontains": "sku__sku_desc",
        "sku_category__icontains": "sku__sku_category__icontains",
        "batch_display_key__icontains": ["batch_detail__batch_no__icontains", "batch_detail__batch_reference__icontains"],
        "manufactured_date__icontains": "batch_detail__manufactured_date__icontains",
        "expiry_date__icontains": "batch_detail__expiry_date__icontains",
        "zone__icontains": "location__zone__zone__icontains",
        "location__icontains": "location__location__icontains",
        "best_before_date__icontains": "batch_detail__best_before_date__icontains",
        "inspection_lot_number__icontains": "batch_detail__inspection_lot_number__icontains",
        "reevaluation_date_icontains": "batch_detail__reevaluation_date__icontains",
        "vendor_batch_no__icontains": "batch_detail__vendor_batch_no__icontains",
        "retest_date__icontains": "batch_detail__retest_date__icontains",
        "bin_number__icontains": "carton__bin_number__icontains",
        "supplier_name__icontains": "supplier__name__icontains",
        "supplier_id__icontains": "supplier__supplier_id__icontains",
        "weight__icontains": "batch_detail__weight__icontains"
    }

def get_batch_level_stock_data_list():
    return ['sku__sku_code', 'sku__sku_desc', 'sku__sku_size', 'sku__sku_category',
            'batch_detail__batch_no', 'batch_detail__manufactured_date', 'batch_detail__expiry_date',
            'batch_detail__mrp', 'batch_detail__weight', 'location__zone__zone', 'location__location',
            'quantity', 'batch_detail__buy_price', 'batch_detail__tax_percent',
            'avg_price_rt', 'quantity', 'receipt_number', 'receipt_date',
            'receipt_type', 'creation_date', 'supplier__supplier_id', 'supplier__name', 'carton__bin_number',
            'batch_detail_id','sku__enable_serial_based', 'id', 'batch_detail__batch_reference',
            'batch_detail__retest_date', 'batch_detail__reevaluation_date', 'batch_detail__best_before_date',
            'batch_detail__inspection_lot_number', 'batch_detail__vendor_batch_no', 'status', 'unit_price', 'sku_id']

def get_batch_level_stock_search_params(request_data):
    search_term = request_data.get('global_search', '')
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)
    return search_term, sort_by_column, sort_type

def get_column_filter_dict_for_batch_level_stock(request_data):
    column_filters = {}
    column_filters_dict = {}
    or_filters = []
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = get_custom_filter_dict_for_batch_level_stock()
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                if isinstance(custom_filter_dict[key], list):
                    or_filters += [Q(**{custom_filter_dict[key][i]: value}) for i in range(len(custom_filter_dict[key]))]
                else:
                    column_filters_dict[custom_filter_dict[key]] = value
            elif value in reverse_stock_choice_mapping:
                column_filters_dict[key] = reverse_stock_choice_mapping.get(value)
            else:
                column_filters_dict[key] = value
    return column_filters_dict, or_filters

def get_batch_level_stock_master_data(user_ids, column_filters_dict, filter_query):

    stock_detail_objs = StockDetail.objects.select_related('sku', 'location', 'location__zone', 'pallet_detail', 'batch_detail').\
                                            exclude(receipt_number=0)\
                                            .filter(sku__user__in=user_ids, quantity__gt=0,**column_filters_dict)\
                                            .filter(filter_query)
    return stock_detail_objs

def prepare_batch_level_master_data(stock_detail_objs, search_term, order_data):
    if search_term:
        master_data = stock_detail_objs.filter(Q(receipt_number__icontains=search_term) |
                                               Q(quantity__icontains=search_term) |
                                               Q(location__zone__zone__icontains=search_term) |
                                               Q(sku__sku_code__icontains=search_term) |
                                               Q(sku__sku_desc__icontains=search_term) |
                                               Q(location__location__icontains=search_term) |
                                               Q(sku__sku_category__icontains=search_term)).order_by(order_data)

    else:
        master_data = stock_detail_objs.order_by(order_data)
    return master_data

def get_serial_number_dict_for_batch_level_stock(stock_list):
    serial_number_dict = {}
    serial_number_list = list(SerialNumberMapping.objects.filter(stock__in = stock_list,status=1).values_list('stock','serial_number'))
    for stock_id, serial_numbers in serial_number_list:
        if stock_id in serial_number_dict:
            serial_number_dict[stock_id].append(serial_numbers)
        else:
            serial_number_dict[stock_id] = [serial_numbers]
    return serial_number_dict

def get_picklist_reserved_dict_for_batch_level_stock(warehouse, stock_list):
    picklist_reserved_dict = {}
    picklist_reserved_dict = dict(Picklist.objects.filter(
                                status__in = ['open', 'Allocated'], user = warehouse.id,stock_id__in = stock_list
                            ).values_list('stock_id').annotate(sum= Sum('reserved_quantity')))
    return picklist_reserved_dict

def get_transactional_dates_for_batch_level_stock(data, timezone):
    mfg_date = exp_date = retest_date = re_evaluation_date = best_before_date = ''
    receipt_date = get_local_date_known_timezone(
        timezone, data.get('receipt_date'),send_date=True
    ).strftime("%d %b, %Y")
    creation_date = get_local_date_known_timezone(timezone, data.get('creation_date'))
    if data.get('batch_detail__manufactured_date'):
        mfg_date = get_local_date_known_timezone(
            timezone,data.get('batch_detail__manufactured_date'),send_date=True
            ).strftime(date_format)

    if data.get('batch_detail__expiry_date'):
        exp_date = get_local_date_known_timezone(
            timezone,data.get('batch_detail__expiry_date'),send_date=True
        ).strftime(date_format)

    if data.get('batch_detail__retest_date'):
        retest_date = get_local_date_known_timezone(
            timezone,data.get('batch_detail__retest_date'),send_date=True
        ).strftime(date_format)

    if data.get('batch_detail__reevaluation_date'):
        re_evaluation_date = get_local_date_known_timezone(
            timezone,data.get('batch_detail__reevaluation_date'),send_date=True
        ).strftime(date_format)

    if data.get('batch_detail__best_before_date'):
        best_before_date = get_local_date_known_timezone(
            timezone,data.get('batch_detail__best_before_date'),send_date=True
        ).strftime(date_format)

    return receipt_date, creation_date, mfg_date, exp_date, retest_date, re_evaluation_date, best_before_date

def get_common_params(request_data):
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    extra_params = json.loads(request_data.get('extraParams')) if request_data.get('extraParams',"") else {}
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "sku__sku_code"
    sort_type = request_data.get('sort_type', 0)
    return column_headers, extra_params, sort_by_column, sort_type


def get_batch_level_stock(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse,
                          filters):

    '''
        Return Stock Detail Records
    '''
    user_ids = [warehouse.id]
    decimal_limit = int(get_decimal_value(warehouse.id))
    request_data = request.GET

    #search terms
    search_term, sort_by_column, sort_type = get_batch_level_stock_search_params(request_data)

    #Order Data
    order_data_dict = get_data_for_batch_level_stock()
    order_data = get_order_data_for_batch_level_stock(order_data_dict, sort_by_column, sort_type)

    #Header Search
    column_filters_dict, or_filters = get_column_filter_dict_for_batch_level_stock(request_data)
    filter_query = reduce(operator.or_, or_filters) if or_filters else Q()
    staging_names = [each_stage['name'] for each_stage in list(INBOUND_STAGING_LANES_MAPPING.values())]
    serial_number_dict,stock_list = {},[]

    #timezone
    timezone = get_user_time_zone(warehouse)

    # list of data to be fetched from stock detail
    lis = get_batch_level_stock_data_list()

    # main query to fetch data
    stock_detail_objs = get_batch_level_stock_master_data(user_ids, column_filters_dict, filter_query)

    # prepare data based on search term and order term
    master_data = prepare_batch_level_master_data(stock_detail_objs, search_term, order_data)

    #Total Count
    temp_data['recordsTotal'] = master_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    temp_data['headers'] = StockDetailHeaders

    #Fetch all required data from stock detail
    master_data = master_data.values(*lis)

    #SKU Details
    sku_codes, sku_ids, stock_ids = [], [], []
    for data in master_data:
        sku_codes.append(data.get('sku__sku_code'))
        sku_ids.append(data.get('sku_id'))
        stock_ids.append(data.get('id'))

    #GET SKU Pack Details
    sku_pack_dict = get_sku_pack_data_with_sku_ids(sku_ids)

    #SKU UOM Dict
    skus_uom_dict = get_uom_with_multi_skus(warehouse, sku_codes, uom_type='purchase')

    #Serial Number Dict
    serial_number_dict = get_serial_number_dict_for_batch_level_stock(stock_ids)

    #Reserved Dict
    picklist_reserved_dict = get_picklist_reserved_dict_for_batch_level_stock(warehouse, stock_ids)

    #status choice mappping
    for data in list(master_data[start_index:stop_index]):
        receipt_date, creation_date, mfg_date, exp_date, retest_date, re_evaluation_date, best_before_date = get_transactional_dates_for_batch_level_stock(data, timezone)
        reserved_quantity = 0

        sku_code, stock_id, uom_dict  = (
            data.get('sku__sku_code',''),
            data.get('id',''),
            skus_uom_dict.get('sku__sku_code', {})
        )

        pcf = uom_dict.get('sku_conversion', 1)
        price = data.get('unit_price') if data.get('unit_price') else 0
        tax = data.get('batch_detail__tax_percent') if data.get('batch_detail__tax_percent') else 0
        if stock_id in picklist_reserved_dict.keys():
            reserved_quantity = picklist_reserved_dict.get(stock_id)

        receipt_type = data.get('receipt_type','')
        if receipt_type in staging_names:
            receipt_type = "po_receipt"

        quantity = data.get('quantity',0)
        quantity_for_val = quantity/pcf
        stock_value = quantity_for_val * price
        serial_numbers = serial_number_dict.get(stock_id, ['Non Serial Stock'])
        for serial_number in serial_numbers:
            if serial_number == 'Non Serial Stock':
                serial_number = ''
        batch_no, batch_reference = data.get('batch_detail__batch_no',''), data.get('batch_detail__batch_reference','')

        #Fetching Packing Data
        pack_repr = ''
        packing_rep_data = sku_pack_dict.get(data.get('sku_id'), {})
        if packing_rep_data:
            pack_repr = get_sku_pack_repr(packing_rep_data, quantity)

        row_data = OrderedDict((
                                ('sku_code', sku_code),
                                ('sku_desc', data.get('sku__sku_desc','')),
                                ('sku_size', data.get('sku__sku_size','')),
                                ('sku_category', data.get('sku__sku_category','')),
                                ('batch_no', batch_no),
                                ('batch_reference', batch_reference),
                                ('batch_display_key', batch_reference or batch_no),
                                ('manufactured_date', mfg_date),
                                ('expiry_date', exp_date),
                                ('mrp', data.get('batch_detail__mrp','')),
                                ('weight', data.get('batch_detail__weight','')),
                                ('zone', data.get('location__zone__zone','')),
                                ('location', data.get('location__location','') ),
                                ('reserved_quantity',round(reserved_quantity, decimal_limit)),
                                ('quantity', round(quantity, decimal_limit)),
                                ('price', round(price, decimal_limit)),
                                ('tax', round(tax, decimal_limit)),
                                ('stock_value', round(stock_value, decimal_limit)),
                                ('receipt_number', data.get('receipt_number','')),
                                ('receipt_date', receipt_date),
                                ('receipt_type', receipt_type),
                                ('creation_date', creation_date),
                                ('supplier_id', data.get('supplier__supplier_id','')),
                                ('supplier_name', data.get('supplier__name','')),
                                ('bin_number', data.get('carton__bin_number','')),
                                ("batch_detail_id", data.get("batch_detail_id")),
                                ("stock_id",stock_id),
                                ("serial_number",serial_number),
                                ("vendor_batch_no", data.get('batch_detail__vendor_batch_no')),
                                ("inspection_lot_number", data.get('batch_detail__inspection_lot_number')),
                                ("best_before_date", best_before_date),
                                ("retest_date", retest_date),
                                ("reevaluation_date", re_evaluation_date),
                                ("pack_repr", pack_repr),
                                ("status", choice_mapping[data.get('status')])
                                ))
        temp_data['aaData'].append(row_data)


def batch_level_stock(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse,
                          request_filters):
    '''
        Return Stock Detail Records
    '''
    log.info(("Request Filters  %s , Search Param %s, order term %s, col num %s") % (str(
        request_filters), str(global_search_term), str(order_term), str(col_num)
    ))
    #search params
    request_data = request.GET
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "sku__sku_code"
    order_data_dict = {"sku_code":"sku__sku_code", "sku_desc":"sku__sku_desc",
                       "batch_no":"batch_detail__batch_no"}
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column
    sort_type = request_data.get('sort_type', 0)
    if sort_type == '1':
        order_data = '-%s' % order_data
    column_filters_dict = {}
    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {"sku_code__icontains":"sku__sku_code__icontains",
                            "sku_desc__icontains":"sku__sku_desc__icontains",
                            "batch_no__icontains":"batch_detail__batch_no__icontains"}
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict.get(key)] = value
            elif value in reverse_stock_choice_mapping:
                column_filters_dict[key] = reverse_stock_choice_mapping.get(value)
            else:
                column_filters_dict[key] = value
    values_list = ['sku__sku_code', 'sku__sku_desc', 'batch_detail__batch_no', 'status', 'receipt_type', 'receipt_number']
    stock_detail_objs = StockDetail.objects.filter(
        sku__user__in = [warehouse.id], quantity__gt = 0, **column_filters_dict
    ).exclude(status = 2).order_by(order_data)

    if search_term:
        stock_detail_objs = stock_detail_objs.filter(
            Q(sku__sku_code__icontains=search_term) |
            Q(sku__sku_desc__icontains=search_term) |
            Q(batch_detail__batch_no=search_term)
        ).order_by(order_data)

    master_data = stock_detail_objs.values(*values_list).annotate(total_qty = Sum('quantity'))

    #Total Count
    temp_data['recordsTotal'] = master_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']

    #stock ids
    sku_codes = [details.get('sku__sku_code') for details in master_data[start_index:stop_index]]

    #Reserved Dict
    picklist_reserved = {}
    filters = {'status': 'open', 'user': warehouse.id,
               'stock__sku__sku_code__in': sku_codes, 'reserved_quantity__gt': 0
            }
    values_list = ['stock__sku__sku_code', 'stock__batch_detail__batch_no', 'stock__status',
                    'stock__receipt_number', 'stock__receipt_type'
                ]
    picklist_reserved =  get_reserved_quantity(filters, values_list)

    #main loop
    for data in list(master_data[start_index:stop_index]):
        sku_code, batch_no, status, receipt_number, receipt_type = (
            data.get('sku__sku_code'),
            data.get('batch_detail__batch_no'),
            data.get('status'),
            data.get('receipt_number'),
            data.get('receipt_type')
        )
        reserved_key = (sku_code, batch_no, status, receipt_number, receipt_type)
        reserved_qty = picklist_reserved.get(reserved_key, 0)

        row_data = OrderedDict((
            ('sku_code', sku_code),
            ('sku_desc', data.get('sku__sku_desc','')),
            ('batch_no', batch_no),
            ('status', choice_mapping[status]),
            ('total_qty', data.get('total_qty')),
            ('reserved_qty', reserved_qty),
            ('receipt_type', receipt_type),
            ('receipt_number', receipt_number)
        ))

        temp_data['aaData'].append(row_data)
    return temp_data

def get_reserved_quantity(filters, values_list):
    reserved_dict = Picklist.objects.filter(**filters).values(*values_list).\
                    annotate(reserved_qty = Sum('reserved_quantity'))
    picklist_reserved = {}
    for stock in reserved_dict:
        uniq_key = ()
        for value in values_list:
            uniq_key += (stock.get(value),)
        picklist_reserved[uniq_key] = stock.get('reserved_qty')
    return picklist_reserved

def get_allocated_quantity(filters, values_list):
    allocation_qty = StockAllocation.objects.filter(**filters).values(*values_list).\
        annotate(allocated_quantity = Sum('quantity'))
    allocated_reserved = {}
    for stock in allocation_qty:
        uniq_key = ()
        for value in values_list:
            uniq_key += (stock.get(value),)
        allocated_reserved[uniq_key] = stock.get('allocated_quantity')
    return allocated_reserved

def inventory_summary(
        start_index, stop_index, temp_data, global_search_term,
        order_term, col_num, request, warehouse, request_filters
    ):
    '''
        Return Inventory Summary
    '''
    #search params
    request_warehouses = []
    warehouse_username_details = {warehouse.id: warehouse.username}
    request_data = request.GET
    decimal_limit = get_decimal_value(warehouse.id)
    company_id = get_company_id(warehouse)
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "sku__sku_code"
    order_data_dict = {
        "sku_code":"sku__sku_code",
        "sku_desc":"sku__sku_desc",
    }
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column
    sort_type = request_data.get('sort_type', 0)
    if sort_type == '1':
        order_data = '-%s' % order_data
    column_filters_dict = {}
    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {
            "sku_code__icontains":"sku__sku_code__icontains",
            "sku_desc__icontains":"sku__sku_desc__icontains",
        }
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict.get(key)] = value
            elif value in reverse_stock_choice_mapping:
                column_filters_dict[key] = reverse_stock_choice_mapping.get(value)
            elif key == 'warehouse__icontains' and value:
                request_warehouses.extend(value.split(','))
            else:
                column_filters_dict[key] = value

    if request_warehouses:
        warehouse_username_details = (
            dict(User.objects.filter(username__in = request_warehouses).values_list('id', 'username'))
        )

    values_list = ['sku__sku_code', 'sku__sku_desc', 'status', 'sku__measurement_type', 'sku__user']

    master_data = StockDetail.objects.exclude(status = 2).filter(
        sku__user__in = warehouse_username_details.keys(), quantity__gt = 0, **column_filters_dict
    ).order_by(order_data)

    if search_term:
        master_data = master_data.filter(
            Q(sku__sku_code__icontains=search_term) |
            Q(sku__sku_desc__icontains=search_term)
        ).order_by(order_data)

    #Total Count
    temp_data['recordsTotal'] = master_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']

    master_data = master_data.values(*values_list).annotate(total_qty = Sum('quantity'))

    master_data = master_data[start_index:stop_index]
    #stock ids
    sku_codes, sku_uoms  = [], []

    for details in master_data:
        sku_codes.append(details.get('sku__sku_code'))
        sku_uoms.append(details.get('sku__measurement_type'))

    uom_decimals = get_uom_decimals(company_id, uom_codes = sku_uoms)

    #main loop
    for data in list(master_data):

        #Round off Based on UOM / Deciaml Limit Config
        uom_decimal_limit = uom_decimals.get(data.get('sku__measurement_type'))
        round_off = uom_decimal_limit or decimal_limit


        sku_code, status = (
            data.get('sku__sku_code'),
            data.get('status'),
        )

        total_qty = truncate_float(float(data.get('total_qty')), round_off)

        row_data = OrderedDict((
            ('sku_code', sku_code),
            ('sku_desc', data.get('sku__sku_desc','')),
            ('status', choice_mapping[status]),
            ('total_qty', total_qty),
            ('warehouse', warehouse_username_details.get(data.get('sku__user'), ''))
        ))

        temp_data['aaData'].append(row_data)

    return temp_data

def get_column_filter_dict_for_sku_stock_summary(column_headers, warehouse):
    or_filters = []
    column_filters_dict = {}
    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_batch_filter = {
            "batch_display_key__icontains":["batch_detail__batch_no__icontains", "batch_detail__batch_reference__icontains"],
            "sku_code__icontains": "sku__sku_code__icontains",
            "sku_desc__icontains": "sku__sku_desc__icontains"
        }
        custom_location_dict = {
            "location__icontains":"location__location__icontains",
            "zone__icontains":"location__zone__zone__icontains",
            "seller_id__icontains": "sku__seller__supplier_id",
            "seller_name__icontains": "sku__seller__name",
        }

        for key, value in column_filters.items():
            if key in custom_batch_filter:
                if isinstance(custom_batch_filter[key], list):
                    or_filters += [Q(**{custom_batch_filter[key][i]: value}) for i in range(len(custom_batch_filter[key]))]
                else:
                    column_filters_dict[custom_batch_filter[key]] = value
            elif key in custom_location_dict:
                column_filters_dict[custom_location_dict.get(key)] = value
            elif value in reverse_stock_choice_mapping:
                column_filters_dict[key] = reverse_stock_choice_mapping.get(value)
            elif key == 'warehouse__icontains':
                warehouse = User.objects.filter(username = value).first()
            else:
                column_filters_dict[key] = value
    return  or_filters, column_filters_dict, warehouse

def get_sku_details(stock_objs):
    sku_codes, sku_ids = [], []
    for stock in stock_objs:
        sku_codes.append(stock.get('sku__sku_code'))
        sku_ids.append(stock.get('sku_id'))
    return sku_codes, sku_ids

def get_sku_pack_quantity(sku_pack_dict, stock, total_qty, temp_stock_data):
    pack_repr = ''
    packing_rep_data = sku_pack_dict.get(stock.get('sku_id'), {})
    if packing_rep_data:
        pack_repr = get_sku_pack_repr(packing_rep_data, total_qty)
        temp_stock_data.update({'pack_repr' : pack_repr})

    return temp_stock_data

def get_short_pick_cycle_status(warehouse, sku_codes=None, detail_view='sku'):
    cycle_type_mapping = {
        'short_cycle_count': ['short pick'],
        'scheduled_cycle_count': ['scheduled'],
        'unscheduled_cycle_count': ['unscheduled'],
        'audit_cycle_count': ['Audit'],
        'adhoc_cycle_count': ['adhoc'],
    }

    misc_types = [
        'restrict_picklist_on_cycle_count_creation_options',
        'restrict_picklist_on_cycle_count_pending_approval_options'
    ]
    misc_dict = get_multiple_misc_values(misc_types, warehouse)

    restrict_creation_options = set(misc_dict.get('restrict_picklist_on_cycle_count_creation_options', '').split(','))
    restrict_approval_options = set(misc_dict.get('restrict_picklist_on_cycle_count_pending_approval_options', '').split(','))

    cycle_type_creation = {val for key, vals in cycle_type_mapping.items() if key in restrict_creation_options for val in vals}
    cycle_type_approval = {val for key, vals in cycle_type_mapping.items() if key in restrict_approval_options for val in vals}

    valid_run_types = cycle_type_creation.union(cycle_type_approval)
    if not valid_run_types:
        return {}, {}, {}

    filter_conditions = {
        'sku__user': warehouse,
        'status__in': [1, 2],
        'run_type__in': valid_run_types
    }

    if sku_codes:
        filter_conditions['sku__sku_code__in'] = sku_codes

    cycle_data = list(CycleCount.objects.filter(**filter_conditions).values(
        'sku__sku_code', 'batch_detail__batch_no', 'stock_status', 'status', 'location__location', 'run_type',
        'location_id', 'batch_detail_id', 'sku_id'
    ))
    sku_codes_from_cycle = []
    location_from_cycle = []

    for data in cycle_data:
        sku_codes_from_cycle.append(data['sku__sku_code'])
        location_from_cycle.append(data['location__location'])

    if sku_codes_from_cycle:
        stock_df = get_cycle_stock_df(warehouse, sku_codes_from_cycle, location_from_cycle)

    short_pick_cycle_data_dict = {}
    cycle_stock_dict, total_cycle_stock_dict = {}, {}
    for data in cycle_data:
        sku_code, sku_id = data.get('sku__sku_code'), data.get('sku_id', '')
        batch_no, batch_detail_id = data.get('batch_detail__batch_no', ''), data.get('batch_detail_id', None)
        stock_status = data.get('stock_status', 1)
        location, location_id = data.get('location__location', ''), data.get('location_id')
        short_pick_key = (sku_code, batch_no, stock_status, location)
        key = (sku_id, batch_detail_id, location_id)

        can_exclude_stock_ids = False
        if data['status'] == '1' and data['run_type'] in cycle_type_creation:
            short_pick_cycle_data_dict[short_pick_key] = 'Yes'
            can_exclude_stock_ids = True

        if data['status'] == '2' and data['run_type'] in cycle_type_approval:
            short_pick_cycle_data_dict[short_pick_key] = 'Yes'
            can_exclude_stock_ids = True

        if can_exclude_stock_ids:
            matching_stocks = stock_df[
                (stock_df['sku_id'] == key[0]) &
                ((stock_df['batch_detail_id'].isna() & (key[1] is None)) |
                (stock_df['batch_detail_id'] == key[1])) &
                (stock_df['location_id'] == key[2])
            ]

            if not matching_stocks.empty:
                # Get the quantity, also taking the first value
                stock_quantity = matching_stocks['quantity'].sum()

                sku_id = int(matching_stocks['sku_id'].iloc[0])
                batch_detail_id = int(matching_stocks['batch_detail_id'].iloc[0]) if matching_stocks['batch_detail_id'].notna().any() else None
                location_id = int(matching_stocks['location_id'].iloc[0]) if matching_stocks['location_id'].notna().any() else None

                total_cycle_stock_dict.setdefault(sku_id, 0)
                total_cycle_stock_dict[sku_id] += stock_quantity

                if detail_view != 'sku':
                    key = (sku_id,)
                    if detail_view in ['location_batch', 'batch']:
                        key += (batch_detail_id,)
                    if detail_view == 'location_batch':
                        key += (location_id,)

                    # Add to cycle_stock_dict
                    cycle_stock_dict.setdefault(key, 0)
                    cycle_stock_dict[key] += stock_quantity

    return short_pick_cycle_data_dict, cycle_stock_dict, total_cycle_stock_dict


def get_cycle_stock_df(warehouse, sku_codes_from_cycle, location):
    values_list = ['sku_id', 'location_id', 'batch_detail_id', 'quantity']
    stock_data = list(
        StockDetail.objects.filter(
            sku__sku_code__in=sku_codes_from_cycle,
            location__location__in=location,
            quantity__gt=0,
            sku__user=warehouse
        ).values_list(*values_list)
    )
    stock_df = pd.DataFrame(stock_data, columns=values_list)
    return stock_df if not stock_df.empty else pd.DataFrame(columns=values_list)

def get_staging_stock_sum_dict(warehouse, detail_view, sku_ids=None, staging_filter=False):
    if staging_filter:
        return {}, {}

    staging_dict = {'location__zone__segregation__in': ['inbound_staging', 'outbound_staging']}
    if sku_ids:
        staging_dict['sku_id__in'] = sku_ids

    values_list = ['sku_id', 'quantity']
    if detail_view in ['batch', 'location_batch']:
        values_list.append('batch_detail_id')
    if detail_view == 'location_batch':
        values_list.append('location_id')

    # Get all staging quantities in a single query
    staging_data = StockDetail.objects.filter(quantity__gt=0, sku__user=warehouse, **staging_dict).values(*values_list)

    # Process the data to create both dictionaries
    sku_dict, batch_dict = {}, {}
    for item in staging_data:
        sku_id, batch_id, qty = item['sku_id'], item.get('batch_detail_id'), item['quantity']
        sku_dict[sku_id] = sku_dict.get(sku_id, 0) + qty
        
        key =(sku_id,)
        if detail_view in ['location_batch', 'batch']:
            key += (batch_id,)
        if detail_view == 'location_batch':
            location_id = item.get('location_id')
            key += (location_id,)

        if len(key) > 1:
            batch_dict[key] = batch_dict.get(key, 0) + qty

    return sku_dict, batch_dict

def get_datatable_filters(view):
    get_batch, get_location, lpn_level = False, False, False
    if view == 'batch_wise':
        get_batch, get_location, lpn_level = True, False, False

    elif view == 'location_wise':
        get_batch, get_location, lpn_level = False, True, False

    elif view == 'batch_location_wise':
        get_batch, get_location, lpn_level = True, True, True

    return get_batch, get_location, lpn_level

def get_datetime_filters(search_params):
    """
    Filters the search parameters based on date range.

    Args:
        search_params (dict): Dictionary containing search parameters.

    Returns:
        dict: Dictionary containing date filters.
    """

    date_filters = {}
    if search_params.get('from_date__icontains', ''):
        date_filters['closing_date__gte'] = search_params.get('from_date__icontains', '')
        del search_params['from_date__icontains']
    else:
        date_filters['closing_date__gte'] = datetime.datetime.now() - timedelta(days = 15)
    if search_params.get('to_date__icontains', ''):
        to_date = datetime.datetime.combine(datetime.datetime.strptime(search_params.get('to_date__icontains'), '%Y-%m-%d') + timedelta(1),
                                                            time())
        date_filters['closing_date__lt'] = to_date
        del search_params['to_date__icontains']
    return date_filters

def extract_column_filters(request_data):
    """
    Extracts the column filters from the request data.

    Args:
        request_data (dict): Dictionary containing request data.

    Returns:
        dict: Dictionary containing column filters.
    """
    column_headers = json.loads(request_data.get("columnFilter", "{}"))

    column_filters_dict = {}
    search_params = {}

    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {
            "sku_code__icontains": "stock__sku__sku_code__icontains",
            "batch_no__icontains": "stock__batch_detail__batch_no__icontains",
            "batch_reference__icontains": "stock__batch_detail__batch_reference__icontains",
            "vendor_batch_number__icontains": "stock__batch_detail__vendor_batch_no__icontains",
            "inspection_lot_number__icontains": "stock__batch_detail__inspection_lot_number__icontains",
            "zone__icontains": "stock__location__zone__zone__icontains",
            "location__icontains": "stock__location__location__icontains"
        }

        for key, value in column_filters.items():
            if key in ['from_date__icontains', 'to_date__icontains']:
                continue
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict[key]] = value
            elif value in reverse_stock_choice_mapping:
                column_filters_dict[key] = reverse_stock_choice_mapping[value]
            else:
                column_filters_dict[key] = value

        search_params = get_datetime_filters(column_filters)

    return column_filters_dict, search_params

def get_sort_order(request_data):
    """
    Retrieves the sort order based on the request data.

    Args:
        request_data (dict): Dictionary containing request data.

    Returns:
        str: Sort order.
    """
    sort_by_column = request_data.get('sort_by_column', 'stock__sku__sku_code')
    sort_type = request_data.get('sort_type', '0')

    order_data_dict = {
        "sku_code": "stock__sku__sku_code",
        "batch_no": "stock__batch_detail__batch_no",
        "vendor_batch_number": "stock__batch_detail__vendor_batch_no",
        "inspection_lot_number": "stock__batch_detail__inspection_lot_number",
        "zone": "stock__location__zone__zone",
        "location": "stock__location__location",
        "batch_reference": "stock__batch_detail__batch_reference",
        "mfg_date": "stock__batch_detail__manufactured_date",
        "exp_date": "stock__batch_detail__expiry_date",
        "retest_date": "stock__batch_detail__retest_date",
        "reevaluation_date": "stock__batch_detail__reevaluation_date",
        "best_before_date": "stock__batch_detail__best_before_date",
        "zone_type": "stock__location__zone__segregation",
        "storage_type": "stock__location__zone__storage_type"
    }

    order_data = order_data_dict.get(sort_by_column, sort_by_column) or "stock__sku__sku_code"
    if sort_type == '1':
        return f"-{order_data}"
    return order_data

def get_filtered_closing_stock_queryset(filters, search_params, order_data, warehouse):
    """
    Retrieves the filtered queryset for closing stock based on the provided filters, search parameters, and order data.

    Args:
        filters (dict): Dictionary containing filter criteria.
        search_params (dict): Dictionary containing search parameters.
        order_data (str): Field name to order the queryset.
        warehouse (Warehouse): Warehouse instance.

    Returns:
        QuerySet: Filtered queryset for closing stock.
    """
    return ClosingStock.objects.filter(stock__sku__user=warehouse.id, **filters, **search_params).order_by(order_data)

def get_values_list():
    """
    Returns the list of values to be fetched from the ClosingStock queryset.

    Returns:
        list: List of field names to be fetched.
    """
    return [
        'closing_date', 'stock__sku__user', 'stock__sku__sku_code', 'stock__sku__sku_desc',
        'stock__batch_detail__batch_no', 'stock__sku__measurement_type', 'stock__sku__sku_size',
        'stock__sku__sku_category', 'stock__sku__sub_category', 'stock__sku__sku_brand',
        'stock__sku__sku_reference', 'stock__batch_detail__manufactured_date',
        'stock__batch_detail__expiry_date', 'stock__batch_detail__mrp',
        'stock__batch_detail__weight', 'stock__location__location',
        'stock__location__zone__zone', 'stock__batch_detail__vendor_batch_no',
        'stock__batch_detail__inspection_lot_number', 'stock__batch_detail__retest_date',
        'stock__batch_detail__reevaluation_date', 'stock__batch_detail__best_before_date',
        'stock__location__zone__segregation', 'stock__location__zone__storage_type',
        'stock__batch_detail__batch_reference'
    ]

def get_uom_mappings(master_data, company_id):
    uoms = [data.get('stock__sku__measurement_type') for data in master_data]
    return get_uom_decimals(company_id, uom_codes=uoms)

def format_closing_stock_rows(master_data, uom_decimals, fallback_decimal):
    """
    Formats the closing stock data rows based on the provided master data and uom mappings.

    Args:
        master_data (list): List of dictionaries containing master data.
        uom_decimals (dict): Dictionary mapping uom codes to their decimal places.
        fallback_decimal (int): Fallback decimal places to use if uom code is not found.

    Returns:
        list: List of formatted data rows.
    """
    formatted_data = []
    date_field_mapping = {
        'closing_date': 'closing_date',
        'stock__batch_detail__manufactured_date': 'mfg_date',
        'stock__batch_detail__expiry_date': 'exp_date',
        'stock__batch_detail__retest_date': 'retest_date',
        'stock__batch_detail__reevaluation_date': 'reevaluation_date',
        'stock__batch_detail__best_before_date': 'best_before_date',
    }

    for data in list(master_data):
        round_off = uom_decimals.get(data.get('stock__sku__measurement_type'), fallback_decimal)
        total_qty = truncate_float(float(data.get('total_qty', 0)), round_off)

        row = OrderedDict((
            ('sku_code', data.get('stock__sku__sku_code', '')),
            ('sku_desc', data.get('stock__sku__sku_desc', '')),
            ('sub_category', data.get('stock__sku__sub_category', '')),
            ('sku_brand', data.get('stock__sku__sku_brand', '')),
            ('batch_no', data.get('stock__batch_detail__batch_no', '')),
            ('batch_reference', data.get('stock__batch_detail__batch_reference', '')),
            ('vendor_batch_number', data.get('stock__batch_detail__vendor_batch_no', '')),
            ('inspection_lot_number', data.get('stock__batch_detail__inspection_lot_number', '')),
            ('mrp', data.get('stock__batch_detail__mrp', '')),
            ('weight', data.get('stock__batch_detail__weight', '')),
            ('zone', data.get('stock__location__zone__zone', '')),
            ('location', data.get('stock__location__location', '')),
            ('uom_type', data.get('stock__sku__measurement_type', '')),
            ('zone_type', data.get('stock__location__zone__segregation', '')),
            ('storage_type', data.get('stock__location__zone__storage_type', '')),
            ('stock_qty', total_qty),
        ))

        for source, target in date_field_mapping.items():
            row[target] = data.get(source).strftime("%Y-%m-%d") if data.get(source) else ''

        formatted_data.append(row)

    return formatted_data

def get_closing_stock_report_data(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse, filters):
    """
    Returns closing stock data for the Inventory tab datatable.

    Applies filters, sorting, and pagination based on request parameters.
    Computes stock quantities with proper rounding and formats results
    with batch, zone, and location details.

    Args:
        start_index (int): Pagination start index.
        stop_index (int): Pagination stop index.
        temp_data (dict): Dictionary to hold total count and result rows.
        request (HttpRequest): Incoming request with filters and sort info.
        warehouse (Warehouse): Target warehouse instance.
        filters (dict): Extra filter options (not used directly).

    Returns:
        dict: Updated temp_data with total count and formatted result rows.
    """
    request_data = request.GET
    decimal_limit = get_decimal_value(warehouse.id)
    company_id = get_company_id(warehouse)

    column_filters_dict, search_params = extract_column_filters(request_data)
    order_data = get_sort_order(request_data)

    queryset = get_filtered_closing_stock_queryset(column_filters_dict, search_params, order_data, warehouse)

    temp_data['recordsTotal'] = queryset.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']

    values_list = get_values_list()
    paginated_data = (
        queryset.values(*values_list)
        .annotate(total_qty=Sum('quantity'))[start_index:stop_index]
    )

    uom_decimals = get_uom_mappings(paginated_data, company_id)
    temp_data['aaData'] = format_closing_stock_rows(paginated_data, uom_decimals, decimal_limit)

    return temp_data

def sku_stock_summary(
        start_index, stop_index, temp_data, global_search_term, order_term,
        col_num, request, warehouse, filters
    ):
    #Stock Summary
    request_data = request.GET

    column_headers, extra_params, sort_by_column, sort_type = get_common_params(request_data)

    view = extra_params.get('view','batch_wise')
    order_data_dict = {
        "sku_code":"sku__sku_code",
        "sku_desc":"sku__sku_desc",
        "batch_no": "batch_detail__batch_no",
        "zone": "location__zone__zone",
        "location":"location__location",
        "seller_id": "sku__seller__seller_id",
        "seller_name": "sku__seller__name"
    }

    order_data = order_data_dict.get(sort_by_column, sort_by_column)
    if sort_type == '1':
        order_data = '-%s' % order_data

    get_batch, get_location, lpn_level = get_datatable_filters(view)

    or_filters = []
    column_filters_dict = {}
    or_filters, column_filters_dict, warehouse = get_column_filter_dict_for_sku_stock_summary(column_headers, warehouse)
    filter_query = reduce(operator.or_, or_filters) if or_filters else Q()
    status, sku_code = (
        extra_params.get('status'),
        extra_params.get('sku_code'),
    )

    final_data = get_stock_details_data(
        warehouse, order_data, sku_code, status, column_filters_dict, filter_query,
        get_location, get_batch, temp_data, start_index, stop_index, lpn_level
    )

    #Total Count
    temp_data['aaData'] = final_data

    return temp_data

def get_stock_details_data(
        warehouse, order_data, sku_code, status, column_filters_dict, filter_query, get_location, get_batch,
        temp_data, start_index, stop_index, lpn_level=False
    ):
    warehouse_id = warehouse.id
    decimal_limit = get_decimal_value(warehouse_id)
    company_id = get_company_id(warehouse)
    timezone = get_user_time_zone(warehouse)

    misc_types = [
        "sku_serialisation"
    ]
    misc_dict = get_multiple_misc_values(misc_types, warehouse_id)

    filter_dict = {'quantity__gt': 0,'sku__sku_code': sku_code, 'sku__user': warehouse_id}

    stock_status = ''
    if str(status):
        stock_status = reverse_stock_choice_mapping[status]
        filter_dict.update({'status': stock_status})

    if column_filters_dict:
        filter_dict.update(column_filters_dict)

    values_list = [
        'sku__sku_code',
        'sku__sku_desc',
        'status',
        'sku__measurement_type',
        'sku_id',
        'sku__seller__supplier_id',
        'sku__seller__name',
    ]

    reserved_values_list = [
        'stock__sku__sku_code',
    ]

    if get_location:
        values_list.extend([
            'location__zone__zone',
            'location__location',
            'location_id',
            'location__zone__segregation',

        ])

        reserved_values_list.extend([
            'stock__location__zone__zone',
            'stock__location__location',
        ])

    if get_batch:
        values_list.extend([
            'batch_detail__batch_no', 'batch_detail__mrp', 'batch_detail__batch_reference',
            'batch_detail__weight', 'batch_detail__manufactured_date', 'batch_detail__expiry_date',
            'batch_detail__vendor_batch_no', 'batch_detail__retest_date', 'batch_detail__reevaluation_date',
            'batch_detail__best_before_date', 'batch_detail__inspection_lot_number', 'unit_price', 'batch_detail_id',
            'batch_detail__json_data__batch_wac', 'batch_detail__json_data__batch_wac_updated_by', 'batch_detail__json_data__batch_wac_updated_date'
        ])
        reserved_values_list.extend([
            'stock__batch_detail__batch_no'
        ])

    if lpn_level:
        reserved_values_list.extend([
            'stock__lpn_number'
        ])

    if get_batch and get_location:
        values_list.append('lpn_number')
    stock_objs = StockDetail.objects.exclude(status=2) \
        .filter(**filter_dict) \
        .filter(filter_query) \
        .values(*values_list) \
        .annotate(
            total_qty=Sum('quantity'),
            receipt_types = ArrayAgg('receipt_type', distinct=True),
            receipt_numbers = ArrayAgg('receipt_number', distinct=True),
            transact_numbers = ArrayAgg('transact_number', distinct=True)
        ) \
        .order_by(order_data)
    #measurement types
    sku_uoms = [stock.get('sku_uom') for stock in stock_objs if stock.get('sku_uom')]
    uom_decimals = get_uom_decimals(company_id, uom_codes = sku_uoms)

    #Total Count
    temp_data['recordsTotal'] = stock_objs.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    stock_objs = stock_objs[start_index:stop_index]

    #SKU Details
    sku_codes, sku_ids = get_sku_details(stock_objs)

    #GET SKU Pack Details
    sku_pack_dict = get_sku_pack_data_with_sku_ids(sku_ids)

    #picklist reserved
    reserved_filters = {
        'user': warehouse_id,
        'status': 'open',
        'reserved_quantity__gt': 0,
        'stock__sku_id__in': sku_ids,
    }

    picklist_reserved = get_reserved_quantity(reserved_filters, reserved_values_list)

    allocated_filters = {
        'warehouse_id': warehouse_id,
        'status': 1,
        'stock__sku_id__in': sku_ids,
    }

    allocated_quantity = get_allocated_quantity(allocated_filters, reserved_values_list)

    cycle_status,_,_ = get_short_pick_cycle_status(warehouse_id,sku_codes)

    final_data = []
    location_ids = []
    reference_numbers = set()
    receipt_types = set()



    for stock in stock_objs:
        temp_stock_data = {}

        if stock.get('location_id'):
            location_ids.append(stock['location_id'])
        #Round off Based on UOM / Deciaml Limit Config
        uom_decimal_limit = uom_decimals.get(stock.get('sku__measurement_type'))
        round_off = uom_decimal_limit or decimal_limit

        sku_code, location, zone, batch_no, status, lpn_number = (
            stock.get('sku__sku_code'),
            stock.get('location__location',''),
            stock.get('location__zone__zone',''),
            stock.get('batch_detail__batch_no',''),
            stock.get('status', ''),
            stock.get('lpn_number', None)
        )

        key = (sku_code,)

        if location: key += (zone,location)
        if get_batch: key += (batch_no, )
        if lpn_level: key += (lpn_number, )
        reserved_qty = truncate_float(float(picklist_reserved.get(key, 0)) + float(allocated_quantity.get(key, 0)), round_off)
        total_qty = truncate_float(float(stock.get('total_qty')), round_off)

        zone_segregation = stock.get('location__zone__segregation')
        staging_area = ''
        if zone_segregation in {'inbound_staging', 'outbound_staging'}:
            receipt_numbers = set(stock.get('receipt_numbers', []))
            reference_numbers.update(receipt_numbers)
            transact_numbers = set(stock.get('transact_numbers', []))
            reference_numbers.update(transact_numbers)
            for receipt_type in stock.get('receipt_types', []):
                receipt_types.add(RECEIPT_TYPE_MAPPING.get(receipt_type))
            staging_area = zone_segregation


        temp_stock_data.update({
            'warehouse': warehouse.username,
            'sku_code': sku_code,
            'seller_id': stock.get('sku__seller__supplier_id'),
            'seller_name': stock.get('sku__seller__name'),
            'sku_desc': stock.get('sku__sku_desc'),
            'total_qty': total_qty,
            'reserved_qty': reserved_qty,
            'status': choice_mapping[stock.get('status')],
            'lpn_number': stock.get('lpn_number', ''),

            'sku_id': stock.get('sku_id'),
            'location_id': stock.get('location_id'),
            'batch_detail_id': stock.get('batch_detail_id'),
            'receipt_type': stock.get('receipt_types', [''])[0],
            'staging_area': staging_area,
            'receipt_numbers': stock.get('receipt_numbers', []) + stock.get('transact_numbers', []),
            'available_stock_status': choice_mapping,
            'available_reverse_stock_status': reverse_stock_choice_mapping,
        })


        #Fetching Packing Data
        temp_stock_data = get_sku_pack_quantity(sku_pack_dict, stock, total_qty, temp_stock_data)

        #If Location Level Stock
        if location:
            temp_stock_data.update({
                'zone': zone,
                'location': location
            })

        #If Batch Level Stock
        if get_batch:
            cycle_status_key = (sku_code,batch_no,status, location)
            batch_reference = stock.get('batch_detail__batch_reference')
            temp_stock_data.update({
                'batch_no': batch_no,
                'batch_reference': batch_reference,
                'batch_display_key': batch_reference or batch_no,
                'vendor_batch_no': stock.get('batch_detail__vendor_batch_no'),
                'mrp': stock.get('batch_detail__mrp'),
                'weight': stock.get('batch_detail__weight'),
                'inspection_lot_number': stock.get('batch_detail__inspection_lot_number'),
                'buy_price': stock.get('unit_price', 0),
                'is_cycle': cycle_status.get(cycle_status_key, "No"),
                'batch_wac': stock.get('batch_detail__json_data__batch_wac', 0) or 0,
                'batch_wac_updated_by': stock.get('batch_detail__json_data__batch_wac_updated_by', '') or '',
                'batch_wac_updated_date': stock.get('batch_detail__json_data__batch_wac_updated_date', '') or '',
            })
            date_fields=[
                ('batch_detail__manufactured_date', 'manufactured_date'),
                ('batch_detail__expiry_date', 'expiry_date'),
                ('batch_detail__retest_date', 'retest_date'),
                ('batch_detail__reevaluation_date', 'reevaluation_date'),
                ('batch_detail__best_before_date', 'best_before_date')
            ]
            for date_field, access_key in date_fields:
                if stock.get(date_field):
                    date_key = get_local_date_known_timezone(
                        timezone, stock.get(date_field), send_date=True
                    ).strftime(date_format)

                    temp_stock_data.update({
                        access_key: date_key
                    })

        final_data.append(temp_stock_data)


    request_dict = {
        'filters': {
            'sku_id__in': sku_ids,
            'location_id__in': location_ids,
            'status__in': [1, 2]
        }
    }
    serial_mixin = SerialNumberMixin(
        user=None,
        warehouse=warehouse,
        request_data=request_dict
    )
    serial_number_dict = serial_mixin.get_serial_numbers()
    serial_numbers = []
    serial_transactions = []
    if serial_number_dict.get('data'):
        serial_numbers = serial_number_dict.get('data')
    serial_transaction_request_dict = {
        'filters': {
            'sku_code__in': sku_codes,
            'reference_number__in': list(reference_numbers),
            'reference_type__in': list(receipt_types),
            'status': 1
        }
    }

    show_staging_serial_numbers = misc_dict.get('sku_serialisation', 'false')
    # validating if show_staging_serial_numbers config is on or off
    if show_staging_serial_numbers == 'true':
        #Get Serial Number Transaction Data
        serial_transaction_mixin = SerialNumberTransactionMixin(
            user=None,
            warehouse=warehouse,
            request_data=serial_transaction_request_dict
        )
        serial_transaction_dict = serial_transaction_mixin.get_existing_sntd()
        if serial_transaction_dict:
            serial_transactions = serial_transaction_dict
    final_data = merge_serial_numbers_with_stock(final_data, serial_numbers, lpn_level, serial_transactions)
    return final_data

def merge_serial_numbers_with_stock(final_data, serial_numbers, lpn_level=False, serial_transactions=None):
    """
    Merges serial numbers with stock data based on serial transactions and serial numbers.

    This function builds lookup dictionaries for serial transactions and serial numbers,
    then merges the corresponding serial numbers into the provided stock data based on
    SKU, location, batch, and optionally LPN (License Plate Number) level.

    Args:
        final_data (list): A list of dictionaries containing stock data that needs to be enriched with serial numbers.
        serial_numbers (list): A list of dictionaries containing serial numbers associated with stock items.
        lpn_level (bool, optional): Whether to include the LPN (License Plate Number) in the key for matching. Defaults to False.
        serial_transactions (list, optional): A list of dictionaries containing serial transactions. Defaults to None.

    Returns:
        list: The `final_data` list with each stock entry enriched with its corresponding serial numbers.
    """

    # Default empty list for serial_transactions if None is passed
    serial_transactions = serial_transactions or []
    # Pre-build lookup dictionaries for serial transactions and serial numbers
    serial_transaction_lookup = {}
    for serial in serial_transactions:
        # Construct the key based on lpn_level
        key = (
            serial.get('sku_code', ''),
            serial.get('batch_number', ''),
            serial.get('reference_type', ''),
            serial.get('reference_number', '')
        )
        if lpn_level:
            key = key + (serial['lpn_number'] or None,)

        # Add serial number to the lookup dictionary
        serial_transaction_lookup.setdefault(key, []).append(serial['serial_number'])

    serial_lookup = {}
    for serial in serial_numbers:
        lpn = None if not serial['lpn_number'] else serial['lpn_number']
        key = (
            serial['sku_id'],
            serial['location_id'],
            serial['batch_detail_id']
        )
        if lpn_level:
            key = key + (lpn,)
        if key not in serial_lookup:
            serial_lookup[key] = []
        serial_lookup[key].append(serial['serial_number'])

    for stock in final_data:
        serial_key = (
            stock.get('sku_id'),
            stock.get('location_id'),
            stock.get('batch_detail_id'),
        )
        receipt_type = RECEIPT_TYPE_MAPPING.get(stock.get('receipt_type'))
        # preparing the serial transaction key
        batch_number = '' if stock.get('batch_no') is  None else stock.get('batch_no')
        staging_entry = stock.get('staging_area')
        reference_numbers = set(stock.get('receipt_numbers', []))
        if lpn_level:
            serial_key = serial_key + (stock.get('lpn_number'),)

        # Assign serial numbers based on the keys
        if serial_key in serial_lookup and not staging_entry:
            stock['serial_numbers'] = serial_lookup[serial_key]
        elif serial_transaction_lookup and staging_entry:
            serials = []
            for reference_number in reference_numbers:
                serial_transation_key = (
                        stock.get('sku_code'),
                        batch_number,
                        receipt_type,
                        str(reference_number)
                )
                if lpn_level:
                    serial_transation_key = serial_transation_key + (stock.get('lpn_number'),)
                serials.extend(serial_transaction_lookup.get(serial_transation_key, []))
            stock['serial_numbers'] = serials
        else:
            stock['serial_numbers'] = []

    return final_data

@get_warehouse
def batch_level_stock_details(request, warehouse:User):
    data_dict = request.GET
    sku_code, status, batch_no, receipt_number, receipt_type = (
        data_dict.get('sku_code'), data_dict.get('status'),
        data_dict.get('batch_no'), data_dict.get('receipt_number'),
        data_dict.get('receipt_type')
    )
    filter_dict = {'sku__sku_code': sku_code, 'quantity__gt': 0}
    if not sku_code:
        return JsonResponse({"message": "SKU Code is required"}, status=400)
    if str(status):
        filter_dict.update({'status': reverse_stock_choice_mapping[status]})

    if batch_no:
        filter_dict.update({'batch_detail__batch_no': batch_no})

    if receipt_number:
        filter_dict.update({'receipt_number': receipt_number})

    if data_dict.get('receipt_type'):
        filter_dict.update({'receipt_type': data_dict.get('receipt_type')})

    values_list = ['sku__sku_code', 'sku__sku_desc','location__zone__zone',
                    'location__location', 'batch_detail__batch_no', 'status',
                    'receipt_type', 'receipt_number']

    stock_objs = StockDetail.objects.filter(sku__user = warehouse.id, **filter_dict).\
                values(*values_list).annotate(total_qty = Sum('quantity'))
    sku_codes = [stock.get('sku__sku_code') for stock in stock_objs]

    #picklist reserved
    filters = {'status': 'open', 'user': warehouse.id,
                'stock__sku__sku_code__in': sku_codes, 'reserved_quantity__gt': 0}
    reserved_values_list = [
        'stock__sku__sku_code', 'stock__location__zone__zone',
        'stock__location__location', 'stock__batch_detail__batch_no', 'stock__receipt_number',
        'stock__receipt_type'
    ]
    picklist_reserved = get_reserved_quantity(filters, reserved_values_list)

    final_data = []
    for stock in stock_objs:
        temp_data = {}
        sku_code, zone, location, batch_no, receipt_number, receipt_type = (
            stock.get('sku__sku_code'),
            stock.get('location__zone__zone'),
            stock.get('location__location'),
            stock.get('batch_detail__batch_no'),
            stock.get('receipt_number'),
            stock.get('receipt_type')
        )
        reserved_qty = picklist_reserved.get(
            (sku_code, zone, location, batch_no, receipt_number, receipt_type), 0
        )

        temp_data.update({
            'sku_code': sku_code,
            'sku_desc': stock.get('sku__sku_desc'),
            'zone': zone,
            'location': location,
            'batch_no': batch_no,
            'total_qty': stock.get('total_qty'),
            'reserved_qty': reserved_qty,
            'status': choice_mapping[stock.get('status')]
        })
        final_data.append(temp_data)

    return JsonResponse({"data": final_data}, status=200)

def get_stock_receipt_number(warehouse):
    receipt_number = StockDetail.objects.filter(sku__user= warehouse.id).only('receipt_number').\
        aggregate(Max('receipt_number'))['receipt_number__max']
    if receipt_number:
        receipt_number = receipt_number + 1
    else:
        receipt_number = 1
    return receipt_number

class InventoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows users to be viewed or edited.
    """
    def get_queryset(self, *args, **kwargs):
        queryset = StockDetail.objects.filter(
            sku__user=self.request.user.current_warehouse.id
        ).order_by('-creation_date')
        if self.request.GET.get('aggregate', None):
            queryset = StockDetail.objects.filter(
            sku__user=self.request.user.current_warehouse.id
            ).order_by('-creation_date').values(
                'sku', 'batch_detail__mrp', 'location', 'location__zone'
            ).annotate(
                total_quantity=Sum('quantity')
            )
        return queryset
    serializer_class = StockSerializer

def get_pr_related_stock(warehouse, sku_code, search_params, include_store_stock=False, uom_type=''):
    exclude_zones = get_exclude_zones(warehouse)
    sku_stocks_list = []
    stock_data = StockDetail.objects.exclude(
            Q(receipt_number=0) | Q(location__zone__zone__in=exclude_zones)
        ).filter(**search_params)

    picklist_exclude_locations = CycleCount.objects.filter(
        sku__user=warehouse.id, sku__sku_code=sku_code, status=1,run_type='short pick'
    ).values('sku_id', 'location__location').distinct()

    for loc in picklist_exclude_locations:
        sku_stocks_data = list(stock_data.filter(sku_id=loc['sku_id'],location__location=loc['location__location']).\
                               distinct().values_list('id', flat=True))

        if sku_stocks_data:
            sku_stocks_list = sku_stocks_list+sku_stocks_data
    if sku_stocks_list:
        sku_stocks_list = list(set(sku_stocks_list))
        stock_data = stock_data.exclude(id__in=sku_stocks_list)
    sku_pack_quantity = 0
    sku_pack_config = get_misc_value('sku_pack_config', warehouse.id)
    if sku_pack_config == 'true':
        sku_pack_master = SKUPackMaster.objects.filter(sku__sku_code=sku_code, sku__user=warehouse.id, status=1)
        if sku_pack_master:
            sku_pack_quantity = sku_pack_master[0].pack_quantity
    st_avail_qty = 0

    if include_store_stock:
        store_user_qs = UserGroups.objects.filter(user=warehouse.id)
        if store_user_qs.exists():
            store_user = store_user_qs[0].admin_user
            store_stock_data = StockDetail.objects.exclude(
                            Q(receipt_number=0) | Q(location__zone__zone__in=['DAMAGED_ZONE', 'QC_ZONE'])). \
                            filter(sku__user=store_user.id, sku__sku_code=sku_code)
            _, st_available_quantity = get_sku_stock_summary(store_stock_data, store_user)

            st_avail_qty = sum(map(
                lambda d: st_available_quantity[d] if st_available_quantity[d] > 0 else 0, st_available_quantity
            ))

    po_search_params = {'open_po__sku__user': warehouse.id,
                        'open_po__sku__sku_code': sku_code,
                        }
    po_quantity = PurchaseOrder.objects.exclude(status__in=['location-assigned', 'confirmed-putaway']).\
                filter(**po_search_params).values('open_po__sku__sku_code').\
                annotate(total_order=Sum('open_po__order_quantity'), total_received=Sum('received_quantity'))

    intransit_qty = 0
    if po_quantity:
        po_ordered_qty = po_quantity[0]['total_order']
        po_received_qty = po_quantity[0]['total_received']
        intransit_qty = po_ordered_qty - po_received_qty
    openpr_qty = 0
    ope_pr_qty_qs = PendingLineItems.objects.filter(sku_id = warehouse.id,
                            purchase_type='PR',
                            sku__sku_code=sku_code,
                        ). \
                        aggregate(openpr_qty=Sum('quantity'))
    openpr_qty = ope_pr_qty_qs['openpr_qty']

    avg_price = 0
    zones_data, available_quantity = get_sku_stock_summary(stock_data, warehouse, uom_type=uom_type)

    avail_qty = sum(map(lambda d: available_quantity[d] if available_quantity[d] > 0 else 0, available_quantity))

    sku_obj = SKUMaster.objects.filter(user=warehouse.id, sku_code=sku_code)
    if sku_obj.exists():
        avg_price = sku_obj[0].average_price
    return stock_data, st_avail_qty, intransit_qty, openpr_qty, avail_qty, \
            sku_pack_quantity, sku_pack_config, zones_data, avg_price

@get_warehouse
def get_sku_stock_check(request, warehouse:User, include_store_stock_=False):
    ''' Check and return sku level stock'''
    sku_code = request.GET.get('sku_code')
    uom_type = request.GET.get('uom_type', '')
    exclude_zones = get_exclude_zones(warehouse)
    exclude_locations = get_exclude_locations(warehouse)

    include_store_stock = request.GET.get('includeStoreStock', '')
    cur_dept = request.GET.get('dept', '')
    dept_avail_qty = 0
    if cur_dept:
        search_params1 = {'sku__user': warehouse.id}
        search_params1['sku__sku_code'] = sku_code
        _, st_avail_qty_dept, _, _, avail_qty_dept, \
        _, _, _, _ = get_pr_related_stock(
            warehouse, sku_code, search_params1, include_store_stock
        )
        dept_avail_qty = st_avail_qty_dept + avail_qty_dept
    search_params = {'sku__user': warehouse.id}
    if request.GET.get('sku_code', ''):
        search_params['sku__sku_code'] = sku_code
    if request.GET.get('location', ''):
        location_master = LocationMaster.objects.filter(zone__user=warehouse.id, location=request.GET['location'])
        if not location_master:
            return HttpResponse(dumps({'status': 0, 'message': 'Invalid Location'}))
        search_params['location__location'] = request.GET.get('location')

    if request.GET.get('pallet_code', ''):
        search_params['pallet_detail__pallet_code'] = request.GET.get('pallet_code')

        stock_detail = StockDetail.objects.exclude(
                Q(receipt_number=0) | Q(location__zone__zone__in=exclude_zones)
            ).exclude(
                location__location__in=exclude_locations
            ).filter(
                location__location=request.GET.get('location', ''),
                sku__user=warehouse.id,
                sku__sku_code=search_params['sku__sku_code'],
                pallet_detail__pallet_code=request.GET['pallet_code']
            )

        if not stock_detail:
            return HttpResponse(dumps({'status': 0, 'message': 'Invalid Location and Pallet code Combination'}))

    (stock_data, st_avail_qty, intransit_qty, openpr_qty, avail_qty,
        sku_pack_quantity, sku_pack_config, zones_data, avg_price) = get_pr_related_stock(
        warehouse, sku_code, search_params, include_store_stock, uom_type=uom_type
    )

    if not stock_data:
        if sku_pack_config:
            return HttpResponse(dumps({
                'status': 1, 'intransit_quantity': intransit_qty,
                'skuPack_quantity': sku_pack_quantity,
                'openpr_qty': openpr_qty, 'available_quantity': st_avail_qty,
                'is_contracted_supplier': False }))

        return HttpResponse(dumps({'status': 0, 'message': 'No Stock Found'}))
    return HttpResponse(dumps({'status': 1, 'data': zones_data, 'available_quantity': avail_qty+st_avail_qty,
                                'dept_avail_qty': dept_avail_qty, 'intransit_quantity': intransit_qty,
                                'skuPack_quantity': sku_pack_quantity, 'openpr_qty': openpr_qty,
                                'is_contracted_supplier': False, 'avg_price': avg_price
                            }))
@get_warehouse
def check_sku_pack_scan(request, warehouse:User):
    pack_id = request.GET.get('pack_id')
    status =''
    flag = 0
    try:
        pack_obj = SKUPackMaster.objects.get(pack_id = pack_id,sku__user = warehouse.id, status=1)
        if pack_obj :
            sku_code = pack_obj.sku.wms_code
            status = "Sku Pack  matched"
            flag = True
            quantity = pack_obj.pack_quantity
    except Exception as e:
            status = "Sku Pack Doesnot matched"
            flag = False
            quantity =0
            sku_code =0
            log.info('some thing went wrong  %s %s %s' % (str(warehouse.username),str(request.POST.dict()), str(e)))

    return HttpResponse(dumps({'status' :status,"sku_code":sku_code,"quantity":quantity,"flag":flag}))

@get_warehouse
def get_inventory_api(request, warehouse:User):
    log.info('Get Inventory request for %s and params are %s' % (
        str(request.user.username), str(request.body)))
    # variable declarations
    data = []
    limit, page_num = 10, 1
    error_status = []
    skus = []
    skus, search_params, picklist_search_params = {}, {}, {}
    batch_q_filters, stock_batch_q_filters = Q(), Q()

    # get misc values
    misc_types = ['stock_supplier_id']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    supplier_inventory = misc_dict.get('stock_supplier_id', 'false')

    # get decimal limit
    decimal_limit = get_decimal_value(warehouse.id)

    # get request data
    request_data = request.body

    if request_data:
        request_data = loads(request_data)
    else:
        request_data = request.GET
    try:
        if request_data:
            try:
                limit = request_data.get('limit', 10)
                page_num = request_data.get('pagenum', 1)
                skus, search_params, picklist_search_params, batch_q_filters, stock_batch_q_filters, errors = prepare_filter(request_data, warehouse)
                if errors:
                    return {'error': [{'message':errors}], 'status': 400}
            except Exception as e:
                return {'error': [{'message':'Invalid Payload'}], 'status': 400}

        # validate available skus
        error_skus = validate_sku_codes(warehouse, skus)
        error_status += [{'sku': error_sku, 'message': 'SKU not found', 'status': 5030} for error_sku in error_skus]

        # fetch exclude zones
        excluded_zones = []

        is_rtv = request_data.get('is_rtv', 'false')
        is_grn_reversal = request_data.get('is_grn_reversal', 'false')
        exclude_staging = True if is_rtv in ['true', True] or is_grn_reversal in ['true', True] else False

        if request_data.get('exclude_damaged_inventory', 'false') == 'true' or exclude_staging:
            excluded_zones = get_exclude_zones(warehouse, is_rtv=exclude_staging, exclude_staging = exclude_staging)

        # fetch picklist reserved quantity
        picklist_reserved, picklist_batch_reserved =  get_picklist_reserved_quantity(
            warehouse, picklist_search_params, excluded_zones, batch_q_filters
        )

        allocation_location_reserved, allocation_batch_reserved = get_stock_allocated_quantity(
            warehouse, picklist_search_params, excluded_zones, batch_q_filters
        )
        # preparing sku and location wise reserved quantity dict
        picklist_location_reserved  = prepare_location_data_dict(picklist_reserved)

        #prepare sku wise reserved_quantity dict
        picklist_reserved, allocation_reserved = prepare_picklist_allocation_reserved_data_dict(picklist_reserved, allocation_location_reserved)

        # preparing sku and batch wise reserved quantity dict
        picklist_batch_data_dict = prepare_picklist_allocation_batch_data_dict(picklist_batch_reserved)

        # fetch stock and batch data for the list of skus received in the request
        master_data, batch_data_dict = get_stock_and_batch_data(
            request_data, warehouse, excluded_zones, search_params,
            stock_batch_q_filters, supplier_inventory, is_rtv
        )

        master_data_skus = [data['sku__sku_code'] for data in master_data]
        skus = master_data_skus

        # fetch open order quantity for the skus
        sku_type_qty = get_open_order_quantity(skus, warehouse)

        page_info = scroll_data(request, master_data, limit=limit, request_type='GET', page_num = page_num)
        data_lis = []
        master_data = page_info['data']

        # fetching uom and batch based flag detail for the skus
        _, batch_based_dict = get_uom_and_batch_details(master_data)

        # Fetching ean numbers details for the skus
        ean_number_dict = get_sku_ean_numbers(skus, warehouse)
        unique_id = 0

        # Main Loop for preparing the inventory details for the skus
        for ind, data in enumerate(master_data):
            batch_details,available_qty, reserved_qty, total_qty, open_order_qty = prepare_batch_details_list(
                request_data, data, picklist_location_reserved, sku_type_qty,
                decimal_limit, supplier_inventory, batch_data_dict,
                picklist_batch_data_dict, batch_based_dict, warehouse, picklist_reserved,
                allocation_location_reserved, allocation_batch_reserved, allocation_reserved,is_rtv
            )
            data_list = prepare_stock_data_list(
                request,request_data, data, unique_id, supplier_inventory,
                available_qty, reserved_qty,total_qty,
                open_order_qty, batch_details, batch_based_dict, ean_number_dict
            )
            data_lis.extend(data_list)
        # Get serial numbers if any stock IDs were collected
        validate_location = validate_request_location(request_data, data_lis, warehouse)
        if not validate_location:
            return {'error': [{'message':'Invalid Location'}], 'status': 400}

        page_info['data'] = data_lis
        response_data = {
            'page_info': page_info.get('page_info', {}), 'status': 200,
            'messages': 'Success',
            'data': page_info['data']
        }
        response_data['status'] = 200
        if error_status:
            response_data['status'] = 207
            response_data['error'] = [{'message': error_status}]

        return response_data
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Get Inventory failed for %s and params are %s and error statement is %s' % (
        str(request.user.username), str(request.body), str(e)))
        response_data = {'error': 'Internal Server Error', 'status': 500}
        return response_data

def prepare_filter(request_data, warehouse):
    search_params = {}
    errors = ""
    picklist_search_params = {}
    batch_q_filters, stock_batch_q_filters = Q(), Q()

    skus = request_data.get('sku', [])
    if isinstance(skus, str) and skus and '[' or ']' in skus:
        skus = skus.strip("][").split(',')
    if not isinstance(skus, list):
        skus = [skus]
    skus = list(map(lambda sku: str(sku), skus))

    if skus:
        search_params['sku__sku_code__in'] = skus

    if request_data.get('category', ''):
        search_params['sku__sku_category'] = request_data.get('category', '')

    if request_data.get('sub_category', ''):
        search_params['sku__sub_category'] = request_data.get('sub_category', '')

    if request_data.get('is_rtv', 'false') in ['true', True]:
        rtv_for_blocked_stock = get_misc_value('rtv_for_blocked_stock', warehouse.id)
        if rtv_for_blocked_stock in ['true', True, 'True']:
            search_params['status'] = 6

    if request_data.get('updated_date'):
        updated_date = parser.parse(request_data['updated_date'])
        updated_date = datetime.datetime.combine(updated_date, datetime.time())
        search_params['updation_date__gt'] = updated_date

    if request_data.get('location'):
        search_params['location__location__iexact'] = request_data.get('location')
        picklist_search_params['stock__location__location__iexact'] = request_data.get('location')

    if request_data.get('zone'):
        search_params['location__zone__zone__iexact'] = request_data.get('zone')
        picklist_search_params['stock__location__zone__zone__iexact'] = request_data.get('zone')

    if request_data.get('lpn_number'):
        search_params['lpn_number'] = request_data.get('lpn_number')

    if 'status' in request_data:
        stock_status = request_data.get('status','')
        if stock_status and isinstance(stock_status, str):
            stock_status = reverse_stock_choice_mapping.get(stock_status,'')
            if stock_status == "":
                errors = "Invalid Status"
            else:
                search_params['status'] = stock_status

    if 'batch' in request_data:
        if request_data['batch']:
            stock_batch_q_filters |= Q(batch_detail__batch_no__iexact = request_data.get('batch')) | Q(batch_detail__batch_reference__iexact=request_data.get('batch'))
            batch_q_filters |= Q(stock__batch_detail__batch_no__iexact = request_data.get('batch')) | Q(stock__batch_detail__batch_reference__iexact=request_data.get('batch'))
        else:
            batch_q_filters |= Q(stock__batch_detail_id=None) | Q(stock__batch_detail__batch_no='')
            stock_batch_q_filters |= Q(batch_detail_id=None) | Q(batch_detail__batch_no='')

    if request_data.get('batch_reference'):
        search_params['batch_detail__batch_reference__iexact'] = request_data.get('batch_reference')
        picklist_search_params['stock__batch_detail__batch_reference__iexact'] = request_data.get('batch_reference')

    return skus, search_params, picklist_search_params, batch_q_filters, stock_batch_q_filters, errors

def validate_sku_codes(warehouse, skus):
    """
    Validates the given SKU codes against the SKUMaster records for a specific warehouse.

    Args:
        warehouse (Warehouse): The warehouse object.
        skus (list): A list of SKU codes to be validated.

    Returns:
        list: A list of SKU codes that are not found in the SKUMaster records for the warehouse.
    """
    error_skus = []
    if skus:
        sku_records = SKUMaster.objects.filter(user=warehouse.id, sku_code__in=skus).values('sku_code', 'id')
        error_skus = set(skus) - set(sku_records.values_list('sku_code', flat=True))
    return error_skus

def get_picklist_reserved_quantity(warehouse, picklist_search_params, excluded_zones, batch_q_filters):
    """
    Get the reserved quantity of stock items based on the given parameters.

    Args:
        warehouse (Warehouse): The warehouse object.
        picklist_search_params (dict): Additional search parameters for picklists.
        excluded_zones (list): List of excluded zones.
        batch_q_filters (Q): Additional filters for batch quantities.

    Returns:
        tuple: A tuple containing two dictionaries:
            - picklist_reserved: A dictionary mapping SKU codes to their reserved quantities.
            - picklist_batch_reserved: A dictionary mapping (SKU code, batch number) pairs to their reserved quantities.
    """

    picklist_location_reserved = (Picklist.objects.filter(
            status='open', user=warehouse.id, **picklist_search_params
        ).filter(batch_q_filters).\
            values_list('stock__sku__wms_code','location__location', 'stock__batch_detail__batch_no').distinct().annotate(reserved=Sum('reserved_quantity')))

    picklist_batch_reserved = Picklist.objects.filter(
            status='open', user = warehouse.id, **picklist_search_params
        ).\
        filter(batch_q_filters).values_list('stock__sku__wms_code', 'stock__batch_detail__batch_no'). \
        distinct().annotate(reserved=Sum('reserved_quantity'))

    return picklist_location_reserved, picklist_batch_reserved

def get_stock_allocated_quantity(warehouse, allocation_search_params, excluded_zones, batch_q_filters):
    """
    Get the allocated quantity of stock items based on the given parameters.

    Args:
        warehouse (Warehouse): The warehouse object.
        allocation_search_params (dict): Additional search parameters for allocations.
        excluded_zones (list): List of excluded zones.
        batch_q_filters (Q): Additional filters for batch quantities.

    Returns:
        tuple: A tuple containing two dictionaries:
            - allocation_location_reserved: Maps (sku_code, location, batch_no) to allocated quantities
            - allocation_batch_reserved: Maps (sku_code, batch_no) to allocated quantities
    """
    allocation_data = StockAllocation.objects.filter(
            status=1, warehouse_id=warehouse.id, **allocation_search_params
        ).filter(batch_q_filters).\
        values_list('stock__sku__sku_code', 'stock__location__location', 'stock__batch_detail__batch_no', 'quantity')

    allocation_location_dict = {}
    allocation_batch_dict = {}

    for item in allocation_data:
        sku_code, location, batch_no, quantity = item

        location_key = (sku_code, location, batch_no)
        allocation_location_dict[location_key] = allocation_location_dict.get(location_key, 0) + quantity

        batch_key = (sku_code, batch_no)
        allocation_batch_dict[batch_key] = allocation_batch_dict.get(batch_key, 0) + quantity

    return allocation_location_dict, allocation_batch_dict

def prepare_picklist_allocation_batch_data_dict(picklist_batch_reserved):
    """
    Prepare a dictionary of picklist batch data.

    Args:
        picklist_batch_reserved (list): A list of picklist batch data.

    Returns:
        dict: A dictionary where the keys are tuples of picklist batch values and the values are the corresponding data.

    Example:
        >>> picklist_batch_reserved = [('sku_code_1', 'batch_number_1', 'reserved_quantity_1'), ('sku_code_2', 'batch_number_2', 'reserved_quantity_2')]
        >>> prepare_batch_data_dict(picklist_batch_reserved)
        {(sku_code_1, 'batch_number_1'): 'reserved_quantity_1', (sku_code_2, 'batch_number_2'): 'reserved_quantity_2'}
    """
    picklist_batch_data_dict = {}

    for picklist_batch in picklist_batch_reserved:
        picklist_batch_data_dict[(picklist_batch[0], picklist_batch[1])] = picklist_batch[2]

    return picklist_batch_data_dict

def prepare_location_data_dict(picklist_reserved):
    picklist_batch_data_dict = {}
    for picklist_batch in picklist_reserved:
        picklist_batch_data_dict[(picklist_batch[0], picklist_batch[1], picklist_batch[2])] = picklist_batch[3]

    return picklist_batch_data_dict

def prepare_picklist_allocation_reserved_data_dict(picklist_reserved, allocation_reserved_dict):
    """
    Prepare a dictionary of reserved data.

    Args:
        picklist_reserved (list): A list of picklist reserved data.
        allocation_reserved_dict (dict): A dictionary of allocation reserved data by (sku_code, location, batch_no).
    Returns:
        tuple: Two dictionaries where the keys are SKU codes and the values are total reserved quantities.

    Example:
        >>> picklist_reserved = [('sku_code_1', 'reserved_quantity_1'), ('sku_code_2', 'reserved_quantity_2')]
        >>> prepare_reserved_data_dict(picklist_reserved)
        {'sku_code_1': 'reserved_quantity_1', 'sku_code_2': 'reserved_quantity_2'}
    """
    picklist_batch_data_dict = {}
    allocation_data_dict = {}
    for picklist_batch in picklist_reserved:
        if picklist_batch[0] in picklist_batch_data_dict:
            picklist_batch_data_dict[picklist_batch[0]] += picklist_batch[3]
        else:
            picklist_batch_data_dict[picklist_batch[0]] = picklist_batch[3]

    for key, quantity in allocation_reserved_dict.items():
        sku_code = key[0]
        if sku_code in allocation_data_dict:
            allocation_data_dict[sku_code] += quantity
        else:
            allocation_data_dict[sku_code] = quantity

    return picklist_batch_data_dict, allocation_data_dict

def get_stock_and_batch_data(
        request_data, warehouse, excluded_zones, search_params,
        stock_batch_q_filters, supplier_inventory, is_rtv
    ):
    """
    Retrieve stock and batch data based on the provided parameters.

    Parameters:
    - request_data (dict): A dictionary containing request-specific data.
    - warehouse (Warehouse): The warehouse for which data is requested.
    - excluded_zones (list): List of zones to be excluded from the data retrieval.
    - search_params (dict): Additional search parameters for data filtering.
    - stock_batch_q_filters (Q): Django Q object representing additional filters for stock and batch queries.
    - supplier_inventory (bool): Flag indicating whether to include supplier inventory data.

    Returns:
    - tuple: A tuple containing stock data and batch data as follows:
        - stock_data (QuerySet): QuerySet containing stock data.
        - batch_data_dict (dict): Dictionary containing batch data organized by SKU code.
    """
    stock_data = get_stock_data(
        request_data, warehouse, excluded_zones, search_params,
        stock_batch_q_filters, supplier_inventory, is_rtv
    )

    batch_data_dict = get_stock_batch_data(
        request_data, warehouse, excluded_zones,
        search_params, stock_batch_q_filters, supplier_inventory, is_rtv
    )

    return stock_data, batch_data_dict

def get_stock_data(
        request_data, warehouse, excluded_zones, search_params, stock_batch_q_filters, supplier_inventory, is_rtv
    ):
    """
    Retrieves stock data based on the provided parameters.

    Args:
        request_data (dict): The request data containing the source and adhoc_count parameters.
        warehouse (object): The warehouse object.
        excluded_zones (list): The list of excluded zones.
        search_params (dict): The search parameters.
        stock_batch_q_filters (Q object): The stock batch query filters.
        supplier_inventory (str): The supplier inventory flag.

    Returns:
        QuerySet: The stock data matching the provided parameters.
    """
    if request_data.get('source') and request_data.get('source') == 'APP':
        exclude_dict = {}
        values_list = [
            'sku__sku_code', 'sku__sku_desc', 'sku__sku_category',
            'sku__sku_brand', 'sku__sku_size', 'location__location',
            'location__zone__zone', 'sku__image_url', 'sku__measurement_type',
            'sku__batch_based', 'sku__scan_picking', 'sku_id', 'location_id',
            'sku__seller__supplier_id', 'sku__seller__name', 'location__check_digit',
        ]
        if is_rtv == 'false':
            values_list.append('lpn_number')
        if supplier_inventory == 'true':
            values_list.append('supplier__supplier_id')

        if request_data.get('adhoc_count'):
            exclude_dict = {
                'location__zone__segregation__in': ['inbound_staging', 'outbound_staging']
            }

        master_data = StockDetail.objects.exclude(status=2).exclude(location__zone__zone__in=excluded_zones).\
            filter(sku__user=warehouse.id, quantity__gt=0, **search_params).filter(stock_batch_q_filters).exclude(**exclude_dict).\
            values(*values_list).distinct().annotate(total=Sum('quantity'), stock_value=Sum(F('quantity') * F('unit_price'))).order_by('sku_id')
        sku_ids = set()
        location_ids = set()
        stock_items = []

        for item in master_data:
            sku_ids.add(item.get('sku_id'))
            location_ids.add(item.get('location_id'))
            stock_items.append(item)

        # Get serial numbers if we have stock items
        if stock_items:
            request_dict = {
                'filters': {
                    'sku_id__in': list(sku_ids),
                    'location_id__in': list(location_ids),
                    'status': 1
                }
            }
            serial_mixin = SerialNumberMixin(
                user=None,
                warehouse=warehouse,
                request_data=request_dict
            )
            serial_number_dict = serial_mixin.get_serial_numbers()

            if serial_number_dict.get('data'):
                merge_serial_numbers_with_stock(stock_items, serial_number_dict['data'])
    else:
        master_data = StockDetail.objects.exclude(status=2).exclude(location__zone__zone__in=excluded_zones).\
            values('sku__sku_code', 'sku__sku_desc', 'sku__sku_category', 'sku__sku_brand', 'sku__measurement_type','sku__batch_based', 'sku_id', 'sku__seller__supplier_id', 'sku__seller__name').distinct().\
            annotate(total=Sum('quantity'), stock_value=Sum(F('quantity') * F('unit_price'))).\
            filter(sku__user=warehouse.id, **search_params).filter(stock_batch_q_filters).order_by('sku_id')

    return master_data

def get_batch_data_dict_for_inventory_view(
        warehouse, excluded_zones, search_params, stock_batch_q_filters, supplier_inventory, is_rtv
    ):
    batch_data_dict = {}
    values_list = [
        'sku__sku_code','location__location', 'batch_detail_id', 'batch_detail__batch_no',
        'batch_detail__expiry_date','batch_detail__mrp','batch_detail__manufactured_date',
        'batch_detail__buy_price','batch_detail__weight', 'status', 'batch_detail__batch_reference',
        'batch_detail__retest_date', 'batch_detail__reevaluation_date', 'batch_detail__best_before_date',
        'batch_detail__inspection_lot_number', 'batch_detail__vendor_batch_no', 'sku_id', 'location_id'
    ]
    if is_rtv == 'false':
        values_list.append('lpn_number')
    if supplier_inventory == 'true':
        values_list.append('supplier__supplier_id')

    batch_data = StockDetail.objects.exclude(status=2).exclude(location__zone__zone__in=excluded_zones).\
    values(*values_list).distinct().annotate(total=Sum('quantity')).filter(sku__user = warehouse.id, quantity__gt = 0,**search_params).filter(stock_batch_q_filters).\
    exclude(location__zone__segregation__in=['inbound_staging','outbound_staging'],location__zone__storage_type__in=['dock','qc','pnd']).order_by('sku_id', 'batch_detail_id')

    sku_ids = set()
    location_ids = set()

    all_batch_details = []

    for batch in batch_data:
        supplier_id = ''
        if supplier_inventory == 'true':
            supplier_id = batch.get('supplier__supplier_id','')

        sku_ids.add(batch.get('sku_id'))
        location_ids.add(batch.get('location_id'))

        carton = batch.get('lpn_number', '') or ''
        batch_unique_key = (batch.get('sku__sku_code'), supplier_id, carton, batch.get('location__location'))
        batch_data_dict.setdefault(batch_unique_key, [])
        batch_detail = {
                            'quantity':0,
                            'batch_no': batch.get('batch_detail__batch_no'),
                            'batch_reference': batch.get('batch_detail__batch_reference'),
                            'expiry_date': batch.get('batch_detail__expiry_date'),
                            'retest_date': batch.get('batch_detail__retest_date'),
                            'reevaluation_date': batch.get('batch_detail__reevaluation_date'),
                            'manufactured_date': batch.get('batch_detail__manufactured_date'),
                            'best_before_date': batch.get('batch_detail__best_before_date'),
                            'inspection_lot_no': batch.get('batch_detail__inspection_lot_number'),
                            'vendor_batch_no': batch.get('batch_detail__vendor_batch_no'),
                            'mrp': batch.get('batch_detail__mrp'),
                            'unit_price': batch.get('batch_detail__unit_price'),
                            'weight': batch.get('batch_detail__weight'),
                            'total': batch.get('total'),
                            'batch_detail_id': batch.get('batch_detail_id', None),
                            'sku_id': batch.get('sku_id', None),
                            'location_id': batch.get('location_id', None),
                            'status': choice_mapping.get(batch.get('status')),
                            'serial_numbers': []
                        }
        batch_data_dict[batch_unique_key].append(batch_detail)
        all_batch_details.append(batch_detail)

    if all_batch_details:
        request_dict = {
            'filters': {
                'sku_id__in': list(sku_ids),
                'location_id__in': list(location_ids),
                'status__in': [1,2]
            }
        }
        serial_mixin = SerialNumberMixin(
            user=None,
            warehouse=warehouse,
            request_data=request_dict
        )
        serial_number_dict = serial_mixin.get_serial_numbers()

        if serial_number_dict.get('data'):
            merge_serial_numbers_with_stock(all_batch_details, serial_number_dict['data'])

    return batch_data_dict

def get_batch_data_dict(warehouse, excluded_zones, search_params, stock_batch_q_filters):
    batch_data = StockDetail.objects.exclude(status = 2).exclude(location__zone__zone__in=excluded_zones).values_list(
        'sku__sku_code', 'sku__sku_desc', 'sku__sku_category', 'sku__sku_brand',
        'batch_detail__batch_no', 'batch_detail__manufactured_date',
        'batch_detail__expiry_date', 'batch_detail__mrp',
        'batch_detail__buy_price','sku__cost_price', 'batch_detail__batch_reference',
        'batch_detail__retest_date', 'batch_detail__reevaluation_date',
        'batch_detail__best_before_date', 'batch_detail__inspection_lot_number',
        'batch_detail__vendor_batch_no', 'batch_detail__json_data', 'sku_id', 'batch_detail_id',
        'sku__seller__supplier_id', 'sku__seller__name'
    ).annotate(total=Sum('quantity')).filter(sku__user = warehouse.id, quantity__gt=0, **search_params).filter(stock_batch_q_filters).order_by('sku_id', 'batch_detail_id')
    batch_data_dict = {}

    for batch in batch_data:
        sku_id = batch[17]
        batch_detail_id = batch[18]

        batch_data_dict.setdefault(batch[0], [])
        batch_detail = {
            'total': batch[21], 'batch_no': batch[4],
            'mfg_date': batch[5], 'exp_date': batch[6],
            'mrp': batch[7], 'buy_price': batch[8],
            'cost_price': batch[9], 'batch_reference': batch[10],
            'retest_date': batch[11], 'reevaluation_date': batch[12],
            'best_before_date': batch[13], 'inspection_lot_no': batch[14],
            'vendor_batch_no': batch[15], 'json_data': batch[16],
            'sku_id': sku_id,
            'batch_detail_id': batch_detail_id,
            'seller_id': batch[19],
            'seller_name': batch[20],
            'serial_numbers': []
        }
        batch_data_dict[batch[0]].append(batch_detail)

    return batch_data_dict

def get_stock_batch_data(
        request_data, warehouse, excluded_zones, search_params,
        stock_batch_q_filters, supplier_inventory, is_rtv
    ):
    if request_data.get('source') and request_data.get('source')=='APP':
        # Batch_data_dict for inventory view in mobile
        batch_data_dict = get_batch_data_dict_for_inventory_view(
            warehouse, excluded_zones, search_params, stock_batch_q_filters, supplier_inventory, is_rtv
        )
    else:
        # batch data_dict for external api
        batch_data_dict = get_batch_data_dict(warehouse, excluded_zones, search_params, stock_batch_q_filters)

    return batch_data_dict

def get_open_order_quantity(skus, warehouse):
    """
    Retrieve the total quantity of open orders for the specified SKUs in a given warehouse.

    Parameters:
    - skus (list): List of SKU codes for which open order quantity is requested.
    - warehouse (Warehouse): The warehouse for which open order quantity is to be retrieved.

    Returns:
    - dict: A dictionary mapping SKU codes to their corresponding total open order quantities.
            The dictionary is structured as follows:
            { 'SKU1': total_quantity1, 'SKU2': total_quantity2, ... }
    """
    sku_type_qty = dict(OrderDetail.objects.filter(user = warehouse.id, quantity__gt=0, status=1, sku__sku_code__in = skus).values_list(
            'sku__sku_code').distinct().annotate(Sum('quantity')))
    return sku_type_qty

def get_uom_and_batch_details(master_data):
    """
    Extract SKU measurement types and batch-based information from a list of master data.

    Parameters:
    - master_data (list): List of dictionaries containing master data information.

    Returns:
    - tuple: A tuple containing two elements:
        - sku_uoms (list): List of SKU measurement types extracted from the master data.
        - batch_based_dict (dict): Dictionary mapping SKU codes to their corresponding batch-based status.
                                  The dictionary is structured as { 'SKU1': batch_based1, 'SKU2': batch_based2, ... }
    """
    sku_uoms = [data.get('sku__measurement_type', '') for data in master_data]
    batch_based_dict = {data.get('sku__sku_code'): data.get('sku__batch_based') for data in master_data}
    return sku_uoms, batch_based_dict

def prepare_batch_dict_for_app(
        supplier_inventory, data, sku_code,
        batch_data_dict, picklist_batch_data_dict, round_off, allocation_location_reserved
    ):
    batch_details = []
    reserved, supplier_id = 0, ''
    if supplier_inventory == 'true':
        supplier_id = data.get('supplier__supplier_id','')

    #Carton Details
    carton = data.get('lpn_number','') or ''

    batch_unique_key = (sku_code, supplier_id, carton, data.get('location__location'))
    if batch_unique_key in batch_data_dict.keys():
        batches = batch_data_dict[batch_unique_key]
        for bat in batches:
            batch_wise_reserved = picklist_batch_data_dict.get((sku_code,data.get('location__location'),bat.get('batch_no')), 0) or 0
            allocated_reserved_qty = allocation_location_reserved.get((sku_code,data.get('location__location'),bat.get('batch_no')), 0) or 0
            reserved_quantity = batch_wise_reserved+allocated_reserved_qty
            reserved += reserved_quantity
            batch_dict = {}
            total_qty = bat.get('total',0)
            batch_dict['batch_no'] = bat.get('batch_no','')
            batch_dict['batch_detail_id'] = bat.get('batch_detail_id','')
            batch_dict['batch_reference'] = bat.get('batch_reference', '')
            batch_dict['batch_display_key'] = batch_dict['batch_reference'] or batch_dict['batch_no']
            batch_dict['manufactured_date'] = format_date(timezone.get_current_timezone().zone, bat.get('manufactured_date')) if bat.get('manufactured_date') else None
            batch_dict['retest_date'] = format_date(timezone.get_current_timezone().zone, bat.get('retest_date')) if bat.get('retest_date') else None
            batch_dict['reevaluation_date'] = format_date(timezone.get_current_timezone().zone, bat.get('reevaluation_date')) if bat.get('reevaluation_date') else None
            batch_dict['best_before_date'] = format_date(timezone.get_current_timezone().zone, bat.get('best_before_date')) if bat.get('best_before_date') else None
            batch_dict['inspection_lot_number'] = bat.get('inspection_lot_no', '')
            batch_dict['vendor_batch_number'] = bat.get('vendor_batch_no', '')
            batch_dict['expiry_date'] = format_date(timezone.get_current_timezone().zone, bat.get('expiry_date')) if bat.get('expiry_date') else None
            batch_dict['mrp'] = bat.get('mrp','')
            batch_dict['weight'] = bat.get('weight','')
            batch_dict['buy_price'] = bat.get('unit_price','')
            batch_dict['reserved_quantity'] = truncate_float(reserved_quantity,round_off)
            batch_dict['total_quantity'] = truncate_float(total_qty, round_off)
            batch_dict['status'] = bat.get('status')
            batch_dict['available_quantity'] = truncate_float(total_qty -reserved_quantity, round_off)
            batch_dict['serial_numbers'] = bat.get('serial_numbers',[]) or []
            if batch_dict['available_quantity'] < 0:
                batch_dict['available_quantity'] = 0
            batch_details.append(batch_dict)

    return batch_details, reserved

def prepare_batch_data_dict_api(sku_code, batch_data_dict, picklist_batch_data_dict, round_off, warehouse, allocation_batch_data_dict):
    batch_details = []
    if sku_code not in batch_data_dict:
        return batch_details

    for bat in batch_data_dict[sku_code]:
        batch_dict = create_batch_dict(sku_code, bat, picklist_batch_data_dict, round_off, warehouse, allocation_batch_data_dict)
        batch_details.append(batch_dict)

    return batch_details

def create_batch_dict(sku_code, bat, picklist_batch_data_dict, round_off, warehouse, allocation_batch_data_dict):
    timezone = get_user_time_zone(warehouse)
    picklist_reserved = picklist_batch_data_dict.get((sku_code, bat['batch_no']), 0)
    allocation_reserved = allocation_batch_data_dict.get((sku_code, bat['batch_no']), 0)
    total_reserved = picklist_reserved + allocation_reserved
    batch_dict = {
        'supplier_name': '',
        'seller_id': bat.get('sku__seller__supplier_id'),
        'seller_name': bat.get('sku__seller__name'),
        'manufacturer_name': '',
        'batch': bat['batch_no'],
        'batch_reference': bat.get('batch_reference'),
        'batch_display_key': bat.get('batch_reference') or bat['batch_no'],
        'reserved_quantity' : total_reserved,
        'total_quantity': truncate_float(bat['total'], round_off),
        'available_quantity': calculate_available_quantity(sku_code, bat, picklist_batch_data_dict, round_off, allocation_batch_data_dict),
        'mrp': bat['mrp'] or 0,
        'buy_price': bat['buy_price'],
        'cost_price': bat['cost_price'],
        'inspection_lot_no': bat['inspection_lot_no'],
        'vendor_batch_no': bat['vendor_batch_no'],
        'mfg_date': format_date(timezone, bat['mfg_date']) if bat['mfg_date'] else None,
        'exp_date': format_date(timezone, bat['exp_date']) if bat['exp_date'] else None,
        'retest_date': format_date(timezone, bat['retest_date']) if bat['retest_date'] else None,
        'reevaluation_date': format_date(timezone, bat['reevaluation_date']) if bat['reevaluation_date'] else None,
        'best_before_date': format_date(timezone, bat['best_before_date']) if bat['best_before_date'] else None,
        'serial_numbers': bat.get('serial_numbers', []) or [],
    }
    if bat.get('json_data'):
        if bat['json_data'].get('supplier_name'):
            batch_dict['supplier_name'] = bat['json_data']['supplier_name']
        if bat['json_data'].get('manufacturer_name'):
            batch_dict['manufacturer_name'] = bat['json_data']['manufacturer_name']
    return batch_dict

def calculate_available_quantity(sku_code, bat, picklist_batch_data_dict, round_off, allocation_batch_data_dict):
    total_quantity = bat['total'] if bat['total'] else 0
    picklist_quantity = picklist_batch_data_dict.get((sku_code, bat['batch_no']), 0)
    allocated_quantity = allocation_batch_data_dict.get((sku_code, bat['batch_no']), 0)
    reserved_qty = picklist_quantity+allocated_quantity
    available_quantity = max(0, total_quantity - reserved_qty)
    return truncate_float(available_quantity, round_off)

def prepare_batch_details_list(
        request_data, data, picklist_location_reserved, sku_type_qty, decimal_limit, supplier_inventory,
        batch_data_dict, picklist_batch_data_dict, batch_based_dict,warehouse, picklist_reserved,
        allocation_location_reserved, allocation_batch_data_dict, allocation_reserved, is_rtv
    ):
    """
    Prepare batch details based on the provided parameters.
    """
    round_off = decimal_limit
    reserved = 0
    sku_code = data.get('sku__sku_code')
    total = data.get('total') if data.get('total') else 0
    batch_details = []
    batch_level_reserved = 0

    if request_data.get('adhoc_count') or (request_data.get('source') and request_data.get('source')=='APP'):
        if batch_based_dict.get(sku_code) == 0:
            batch_details = []
        else:
            batch_details_list,  batch_level_reserved = prepare_batch_dict_for_app(
                supplier_inventory, data, sku_code, batch_data_dict, picklist_location_reserved, round_off, allocation_location_reserved
            )
            batch_details.extend(batch_details_list)
    else:
        batch_details_list = prepare_batch_data_dict_api(sku_code, batch_data_dict, picklist_batch_data_dict, round_off,warehouse, allocation_batch_data_dict)

        batch_details.extend(batch_details_list)

    if request_data.get('source') and request_data.get('source')=='APP' and not is_rtv:
       reserved = batch_level_reserved
    else:
        picklist_qty = picklist_reserved.get(sku_code, 0)
        allocation_qty = allocation_reserved.get(sku_code, 0)
        reserved = picklist_qty + allocation_qty
    quantity = total - reserved
    open_order_qty = sku_type_qty.get(sku_code, 0)
    quantity = quantity - open_order_qty
    if quantity < 0:
        quantity = 0

    available_qty, reserved_qty, total_qty, open_order_qty = (
                truncate_float(quantity, round_off), truncate_float(reserved, round_off),
                truncate_float(total, round_off), truncate_float(open_order_qty, round_off)
            )

    return batch_details, available_qty, reserved_qty, total_qty, open_order_qty


def prepare_stock_data_list(
    request,request_data, data, unique_id, supplier_inventory,
    available_qty, reserved_qty, total_qty,
    open_order_qty, batch_details, batch_based_dict,
    ean_number_dict
):
    """
     Prepare stock details based on the provided parameters.
    """
    data_lis = []
    if request_data.get('source') and request_data.get('source')=='APP':
        unique_id = unique_id + 1
        image_url = data.get('sku__image_url','')
        if image_url and "http" not in image_url:
            image_url = request.build_absolute_uri('/') + image_url
        image_url = image_url.replace("https://udaan.azureedge.net/products", "https://ud-img.azureedge.net/w_120/u/products")
        supplier_id = data.get('supplier__supplier_id','')
        if supplier_inventory == 'true':
            supplier_id = data.get('supplier__supplier_id')

        data_lis.append(
            OrderedDict((
                ('sku', data.get("sku__sku_code")),
                ('available_quantity', available_qty),
                ('reserved_quantity', reserved_qty),
                ('total_quantity', total_qty),
                ('open_order_quantity', open_order_qty),
                ('batch_details', batch_details),
                ('sku_desc', data.get('sku__sku_desc','')),
                ('sku_brand', data.get('sku__sku_brand','')),
                ('sku_size', data.get('sku__sku_size','')),
                ('location', data.get('location__location')),
                ('zone', data.get('location__zone__zone')),
                ('lpn_number', data.get('lpn_number','') or ''),
                ('sku_image', image_url),
                ('supplier_id', supplier_id),
                ('seller_id', data.get('sku__seller__supplier_id', '') or ''),
                ('seller_name', data.get('sku__seller__name', '') or ''),
                ('batch_based', batch_based_dict.get(data.get("sku__sku_code"),'')),
                ('is_scannable', data.get('sku__scan_picking', 0)),
                ('id', unique_id),
                ('serial_numbers', data.get('serial_numbers')),
                ('check_digit', data.get('location__check_digit')),
                ('ean_numbers', ean_number_dict.get(data.get("sku__sku_code"),[]))
            )))
    else:
        data_lis.append(OrderedDict((
            ('sku', data.get("sku__sku_code")),
            ('sku_desc', data.get("sku__sku_desc")),
            ('seller_id', data.get('sku__seller__supplier_id', '') or ''),
            ('seller_name', data.get('sku__seller__name', '') or ''),
            ('sku_uom', data.get("sku__measurement_type")),
            ('available_quantity', available_qty),
            ('reserved_quantity', reserved_qty),
            ('total_quantity', total_qty),
            ('open_order_quantity', open_order_qty),
            ('batch_details', batch_details)
        )))

    return data_lis

def validate_request_location(request_data, data_lis, warehouse):
    """
    Validate the request location against the warehouse and data list.

    Parameters:
    - request_data (dict): A dictionary containing request-specific data.
    - data_lis (list): A list of data used for validation.
    - warehouse (Warehouse): The warehouse for which validation is performed.

    Returns:
    - bool: True if the location is valid, False otherwise.
    """
    location_check = True
    if len(data_lis) == 0 and request_data.get('location'):
        loc = LocationMaster.objects.filter(zone__user = warehouse.id, location__iexact = request_data.get('location'))
        if not loc.exists():
            location_check = False
    return location_check


@get_warehouse
def get_serial_numbers_list(request, warehouse:User):
    serial_number_dict = []
    wms_code = request.GET.get('sku_code')
    source_loc = request.GET.get('source_loc')
    batch_no = request.GET.get('batch_no')
    check_sku = SKUMaster.objects.filter(user = warehouse.id,sku_code = wms_code,enable_serial_based = 1,status=1).values_list('sku_code',flat=True)
    if check_sku.exists():
        if batch_no != 'none':
            stock_id = list(StockDetail.objects.filter(
                        sku__user = warehouse.id,sku__sku_code = check_sku[0],sku__enable_serial_based = 1,
                        quantity__gt =0,location__location = source_loc, status = 1,
                        batch_detail__batch_no = batch_no
                    ).values_list('id',flat=True))
        else:
            stock_id = list(StockDetail.objects.filter(
                sku__user = warehouse.id,sku__sku_code = check_sku[0],sku__enable_serial_based = 1,
                quantity__gt =0,location__location = source_loc, status = 1
                ).values_list('id',flat=True))

        if stock_id:
            serial_number_dict = (list(SerialNumberMapping.objects.filter(
                stock__in = stock_id,status=1
            ).values_list('serial_number', flat=True)))
    return HttpResponse(dumps({'serial_numbers': serial_number_dict}))


def get_putaway_and_non_sellable_quantity(warehouse_ids, sku_ids=[]):
    """
    Retrieves the putaway and non-sellable quantities for the given warehouse IDs.

    Args:
        warehouse_ids (list): List of warehouse IDs.

    Returns:
        tuple: A tuple containing the following dictionaries:
            - pen_putaway_quantity: Dictionary of SKU codes and their corresponding putaway quantities.
            - pen_return_putaway: Dictionary of SKU codes and their corresponding return putaway quantities.
            - pull_to_locate_quantity: Dictionary of SKU codes and their corresponding pull-to-locate quantities.
            - non_sellable_quantity: Dictionary of SKU codes and their corresponding non-sellable quantities.
            - jo_pending_putaway: Dictionary of SKU codes and their corresponding job order pending putaway quantities.
    """

    pen_putaway_quantity, pen_return_putaway, jo_pending_putaway, pull_to_locate_quantity = {}, {}, {}, {}

    po_location_qs = POLocation.objects.filter(sku__user__in = warehouse_ids, status = 1)
    if sku_ids:
        po_location_qs = po_location_qs.filter(sku__in = sku_ids)

    pending_putaway_data = list(po_location_qs.values(
        'sku__sku_code','putaway_type').annotate(sum=Sum('quantity')))

    putaway_type_dict = {
        'cancelled_picklist': pull_to_locate_quantity,
        'job_order': jo_pending_putaway,
        'po_grn': pen_putaway_quantity,
        'sales_return': pen_return_putaway
    }
    for data in pending_putaway_data:
        putaway_type = data.get('putaway_type')
        sku_code = data.get('sku__sku_code')
        quantity = data.get('sum')

        if putaway_type in putaway_type_dict:
            putaway_type_dict[putaway_type][sku_code] = quantity

    stock_detail_qs = StockDetail.objects.filter(
       Q(location__zone__segregation="non_sellable")|
       Q(location__zone__zone__in =['DAMAGED_ZONE','QC_ZONE']),
        quantity__gt = 0,
        sku__user__in = warehouse_ids
    )
    if sku_ids:
        stock_detail_qs = stock_detail_qs.filter(sku__in = sku_ids)

    non_sellable_quantity = dict(stock_detail_qs.values_list('sku__sku_code').annotate(total = Sum('quantity')))

    return pen_putaway_quantity, pen_return_putaway, pull_to_locate_quantity, non_sellable_quantity, jo_pending_putaway


def get_current_stock(sku_ids, zones=None):
    basic_filter = {
        'sku_id__in': sku_ids,
        'quantity__gt': 0
    }
    if zones:
        basic_filter['location__zone__zone__in'] = zones
    stock_query = StockDetail.objects.filter(
            **basic_filter
        ).values(
            'sku_id'
        ).annotate(
            stock=Sum('quantity'),
            sku_code = F('sku__sku_code'),
            sku_desc = F('sku__sku_desc')
        )
    stock = pd.DataFrame(stock_query)
    basic_filter1 = {
        'stock__sku_id__in': sku_ids,
        'status': 'open',
        'reserved_quantity__gt': 0
    }
    if zones:
        basic_filter1['stock__location__zone__zone__in'] = zones
    reserved_query = Picklist.objects.filter(
        **basic_filter1
    ).values(
        'stock__sku_id'
    ).annotate(
        sku_id=F('stock__sku_id'),
        reserved_qty=Sum('reserved_quantity'),
    )
    reserved = pd.DataFrame(reserved_query)
    if not reserved.empty:
        results = stock.merge(reserved, on=['sku_id'], how='left')
    else:
        results = stock
        results['reserved_qty'] = 0
    results = results.fillna(0)
    if not results.empty:
        results['available_qty'] = results.stock - results.reserved_qty
    else:
        return pd.DataFrame(columns=['sku_id','sku_code','sku_desc', 'available_qty', 'reserved_qty'])
    return results


class StockDetailTransact():
    """
        Stock Detail Transaction Controller for inserting and updating stock records
    """

    def default_status(self, warehouse_id: int):
        # check for qc flag for default status
        sku_qc_check = self.sku.qc_check
        filter_params = {
            "transaction_type": "after_putaway",
            "warehouse_id":warehouse_id
        }
        warehouse_qc = QCConfiguration.objects.filter(**filter_params)
        warehouse_qc_check = warehouse_qc[0].status if warehouse_qc.exists() else False

        if sku_qc_check and warehouse_qc_check:
            self.stock_status = 0

    def insert_stock(self,
            warehouse: User,
            receipt_number: int,
            receipt_date: datetime,
            receipt_type: str,
            sku: SKUMaster,
            location: LocationMaster,
            quantity: int,
            original_quantity: int = 0,
            pallet_detail: PalletDetail = None,
            batch_detail: BatchDetail = None,
            grn_number: str = '',
            carton: int = None,
            supplier: int = None,
            price_type: str = "user_input",
            unit_price: float = 0,
            avg_price_rt: float = 0
        ):

        #setting default stock status
        self.sku = sku
        self.stock_status = 1
        self.default_status(warehouse.id)

        create_stock_detail = {
            "receipt_number": receipt_number,
            "receipt_date": receipt_date,
            "receipt_type": receipt_type,
            "sku": sku,
            "location": location,
            "quantity": quantity,
            "original_quantity": original_quantity,
            "pallet_detail": pallet_detail,
            "batch_detail": batch_detail,
            "grn_number": grn_number,
            "carton_id": carton,
            "supplier": supplier,
            "price_type": price_type,
            "unit_price": unit_price,
            "avg_price_rt": avg_price_rt,
            "status": self.stock_status,
            "account_id": warehouse.userprofile.id
        }

        stock_data = StockDetail.objects.create(**create_stock_detail)
        return stock_data

    def create_or_update_stockrecord(self, stock_detail, existing_filter_params, available_quantity,  aux_data, from_status, updating_status):
        #check if stock exists for same unique values and incoming status
        existing_stock_obj = StockDetail.objects.filter(**existing_filter_params, location_id=stock_detail.location_id).first()
        if existing_stock_obj:
            existing_stock_obj.quantity = float(Decimal(str(existing_stock_obj.quantity)) + available_quantity)
            existing_stock_obj.original_quantity = float(Decimal(str(existing_stock_obj.original_quantity)) + available_quantity)
            existing_stock_obj.aux_data = aux_data
            existing_stock_obj.aux_data['from_status'] = from_status
            existing_stock_obj.save()
        else:
            #creating a new record if not present
            new_instance = deepcopy(stock_detail.__dict__)
            del new_instance['_state']
            del new_instance['id']
            del new_instance['creation_date']
            del new_instance['updation_date']
            new_instance['status'] = updating_status
            new_instance['quantity'] = float(available_quantity)
            new_instance['original_quantity'] = float(available_quantity)
            aux_data['from_status'] = from_status
            new_instance['aux_data'] = aux_data
            StockDetail.objects.create(**new_instance)

    def update_stock_status(self, warehouse_id: int, stock_items_list: list = []):

        """
            Updating the Locked sku Stock Status
        """
        decimal_limit = int(get_decimal_value(warehouse_id))
        for stock_item in stock_items_list:
            sku_code = stock_item.get('sku_code')
            zone = stock_item.get('zone')
            batch_number = stock_item.get('batch_number')
            updating_quantity = stock_item.get('quantity')
            from_status = stock_item.get('from_status')
            updating_status = stock_item.get('to_status')
            aux_data = stock_item.get('aux_data', {})
            auto_pick = stock_item.get('auto_pick', 'default')
            parent_aux_data = aux_data.copy()
            parent_aux_data['parent'] = True

            filter_params = {
                "sku__sku_code": sku_code,
                "sku__user": warehouse_id,
                "batch_detail__batch_no": batch_number,
                "status": from_status,
                "quantity__gt": 0
            }

            existing_filter_params = {
                "sku__sku_code": sku_code,
                "sku__user": warehouse_id,
                "batch_detail__batch_no": batch_number,
                "status": updating_status,
            }
            if zone:
                filter_params['location__zone__zone'] = zone
                existing_filter_params['location__zone__zone'] = zone

            with transaction.atomic('default'):
                stock_detail_objs = StockDetail.objects.filter(**filter_params)
                total_quantity = stock_detail_objs.aggregate(total_quantity=Sum('quantity'))['total_quantity']
                total_quantity = round(total_quantity, decimal_limit)
                if stock_detail_objs.exists() and total_quantity >= updating_quantity:

                    if auto_pick in ['first', 'last', 'default']:
                        # auto pick logic
                        if auto_pick == 'first':
                            stock_detail_objs = stock_detail_objs.order_by('creation_date')
                        elif auto_pick == 'last':
                            stock_detail_objs = stock_detail_objs.order_by('-creation_date')
                        elif auto_pick == 'default':
                            stock_detail_objs = stock_detail_objs.order_by('quantity')

                        #updating the stock as per auto pick
                        quantity_difference = Decimal(str(updating_quantity))
                        for stock_detail in stock_detail_objs:
                            if quantity_difference <= 0:
                                break
                            stock_quantity = Decimal(str(stock_detail.quantity))
                            available_quantity = min(stock_quantity, quantity_difference)
                            stock_detail.quantity = float(stock_quantity - available_quantity)
                            stock_detail.aux_data = parent_aux_data
                            stock_detail.save()
                            quantity_difference -= available_quantity

                            self.create_or_update_stockrecord(stock_detail, existing_filter_params, available_quantity, aux_data, from_status, updating_status)

        return "Created Successfully", False


    def reverse_stock(self, warehouse_id: int, inspection_lot_no: Union[str, int]):
        try:
            with transaction.atomic('default'):
                # Get the original stock item using the inspection_lot_no
                original_stock_item = StockDetail.objects.filter(
                    sku__user=warehouse_id,
                    aux_data__inspection_lot_no=inspection_lot_no,
                    aux_data__parent=True
                )

                for parent_obj in original_stock_item:
                    # Get the sum of the quantities of all the split stock items
                    split_quantity_obj = StockDetail.objects.filter(
                        sku__user=warehouse_id,
                        sku_id=parent_obj.sku_id,
                        batch_detail_id=parent_obj.batch_detail_id,
                        location_id=parent_obj.location_id,
                        aux_data__inspection_lot_no=inspection_lot_no,
                        aux_data__from_status=parent_obj.status
                    )

                    total_split_quantity = split_quantity_obj.aggregate(total_split_quantity=Sum('quantity'))['total_split_quantity']

                    if total_split_quantity + parent_obj.quantity <= parent_obj.original_quantity:
                        # Allow the stock reversal
                        parent_obj.quantity = total_split_quantity + parent_obj.quantity
                        parent_obj.save()

                        # Update the quantity of all the split stock items to zero
                        split_quantity_obj.update(quantity=0, original_quantity=0)
                        log.info("stock reversal success for Lotnumber %s with parent_obj %s", str(inspection_lot_no), str(parent_obj.id))
                    else:
                        log.info("stock reversal failed for Lotnumber %s with parent_obj %s", str(inspection_lot_no), str(parent_obj.id))
                        return JsonResponse({"error": "Invalid stock reversal Failed"}, status=500)

                return JsonResponse({"message": "Stock reversed successfully"}, status=200)

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("stock reversal failed with error %s", str(e))
            return JsonResponse({"error": "Invalid stock reversal Failed"}, status=500)

@get_warehouse
def stock_status_update(request, warehouse:User):
    request_data = loads(request.body)
    sku_code = request_data.get('sku_code')
    batch_number = request_data.get('batch_number')
    location = request_data.get('location')
    from_status = request_data.get('from_status')
    to_status = request_data.get('to_status')
    lpn_number = request_data.get('lpn_number')

    stock_filters = {
        "sku__sku_code": sku_code,
        "sku__user": warehouse.id,
        "location__location": location,
        "status": from_status,
        "quantity__gt": 0
    }
    if batch_number:
        stock_filters['batch_detail__batch_no'] = batch_number
    if lpn_number:
        stock_filters['lpn_number'] = lpn_number

    stock_objs = StockDetail.objects.exclude(
        location__zone__segregation__in = ['inbound_staging', 'outbound_staging', 'replenishment_staging_area', 'nte_staging_area']
    ).exclude(location__zone__storage_type='wip_area').exclude(status = 2).filter(**stock_filters)

    if not stock_objs.exists():
        return JsonResponse({"message": "Invalid Stock Details"}, status=400)

    stock_update_list = []
    for stock_obj in stock_objs:
        stock_obj.status = to_status
        stock_obj.remarks = 'Manual Status Change'
        stock_update_list.append(stock_obj)

    StockDetail.objects.bulk_update(stock_update_list, ['status', 'remarks'])
    return JsonResponse({"message": "Stock status updated successfully"}, status=200)

class StockStatusUpdateView(ListView):

    def post(self, request, *args, **kwargs):
        data = loads(request.body)
        accessible_warehouses = request.accessible_warehouses

        #payload data validations
        serializer = StockStatusUpdateDataSerializer(data={'items': data}, context={'accessible_warehouses': accessible_warehouses})
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as error:
            return JsonResponse({"error": error.detail}, status=400)

        #validated payload
        payload_data = serializer.validated_data

        #get warehouse related to payload
        warehouse_name = payload_data[0].get('warehouse')
        warehouse_obj = accessible_warehouses.get(warehouse_name)
        warehouse_id = warehouse_obj.id

        # updating stock status
        stock_detail_transact = StockDetailTransact()
        result, error_message = stock_detail_transact.update_stock_status(warehouse_id, payload_data)

        if error_message:
            return JsonResponse({"error": error_message}, status=400)

        return JsonResponse({"message": result}, status=200)

    def get(self, request):
        return JsonResponse({"error": "method not allowed"}, status=405)


class StockReversalAPIView(ListView):

    def post(self, request, *args, **kwargs):
        data = loads(request.body)
        accessible_warehouses = request.accessible_warehouses

        #payload data validations
        serializer = StockReversalSerializer(data=data, context={'accessible_warehouses': accessible_warehouses})
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as error:
            return JsonResponse({"error": error.detail}, status=400)

        #validated payload
        payload_data = serializer.validated_data

        # Get the validated data from the serializer
        warehouse_name = payload_data['warehouse']
        inspection_lot_no = payload_data['inspection_lot_no']

        warehouse_obj = accessible_warehouses.get(warehouse_name)
        warehouse_id = warehouse_obj.id

        # Call the reverse_stock function with the provided data
        stock_detail_transact = StockDetailTransact()
        result = stock_detail_transact.reverse_stock(warehouse_id, inspection_lot_no)

        return result

def update_or_create_batch(warehouse, batch_dict):
    batch_no, sku_id = (
        batch_dict.get('batch_no', ''),
        batch_dict.get('sku_id', '')
    )
    default_batch_dict = {key: value for key, value in batch_dict.items() if key not in ('sku_id', 'batch_no', 'warehouse_id')}
    batch_obj,_ = BatchDetail.objects.update_or_create(
        sku_id=sku_id,
        batch_no=batch_no,
        warehouse_id = warehouse.id,
        account_id = warehouse.userprofile.id,
        defaults=default_batch_dict
    )
    return batch_obj

def create_stock_details(creation_dict, return_obj = False):
    stock_obj = StockDetail.objects.create(**creation_dict)
    return stock_obj if return_obj  else None

def update_or_create_stockdetail(stock_dict):
    filter_dict = {
        'sku_id': stock_dict.get('sku_id'),
        'receipt_type':  stock_dict.get('receipt_type'),
        'receipt_number': stock_dict.get('receipt_number'),
        'location_id': stock_dict.get('location_id'),
        'status': stock_dict.get('status'),
        'batch_detail_id': stock_dict.get('batch_detail_id'),
    }
    stock_obj = StockDetail.objects.filter(**filter_dict)
    if not stock_obj:
        stock_obj = create_stock_details(stock_dict, return_obj=True)
    else:
        stock_obj = stock_obj[0]
        stock_obj.quantity += stock_dict.get('quantity')
        stock_obj.original_quantity += stock_dict.get(
            'original_quantity'
        )
        stock_obj.save()
    return stock_obj

def update_or_create_batch(warehouse, batch_dict):
    batch_no, sku_id = (
        batch_dict.get('batch_no', ''),
        batch_dict.get('sku_id', '')
    )
    default_batch_dict = {key: value for key, value in batch_dict.items() if key not in ('sku_id', 'batch_no', 'warehouse_id')}
    batch_obj,_ = BatchDetail.objects.update_or_create(
        sku_id=sku_id,
        batch_no=batch_no,
        warehouse_id = warehouse.id,
        account_id = warehouse.userprofile.id,
        defaults=default_batch_dict
    )
    return batch_obj

def get_or_create_batch(warehouse, batch_dict, update = False, draft = False, extra_attributes=None):
    #Modify Batch Characters
    batch_dict = modify_batch_characters(warehouse, batch_dict)
    error_list, batch_obj = [], None
    batch_no, sku_id, batch_reference, inspection_lot_no, retest_date = (
        batch_dict.get('batch_no', ''),
        batch_dict.get('sku_id', ''),
        batch_dict.get('batch_reference', ''),
        batch_dict.get('inspection_lot_number') or '',
        batch_dict.get('retest_date', None)
    )
    #If batch no is empty and if asn, no need of validation
    if batch_reference and not batch_no:
        batch_details, _ = get_batch_data(warehouse, sku_id, batch_no = None, batch_reference = batch_reference)
        if batch_details and batch_details[0].get('batch_no'):
            batch_no = batch_details[0].get('batch_no')

    if not batch_no:
        batch_no = get_incremental_batch_no(warehouse)

    exclude_keys = ['sku_id', 'batch_no', 'warehouse_id', 'extra_attributes']

    default_batch_dict = {key: value for key, value in batch_dict.items() if key not in exclude_keys}

    # Add 'account_id' to the default_batch_dict
    default_batch_dict['account_id'] = warehouse.userprofile.id

    if update:
        # Deleting below keys as need to update
        keys_to_remove = ['batch_reference', 'inspection_lot_number', 'retest_date']
        default_batch_dict = {key: default_batch_dict[key] for key in default_batch_dict if key not in keys_to_remove}
    # Use get_or_create to fetch the object or create a new one
    try:
        batch_obj, created = BatchDetail.objects.get_or_create(
            sku_id=sku_id,
            batch_no=batch_no,
            warehouse_id = warehouse.id,
            defaults=default_batch_dict
        )
        if not created:
            if update:
                batch_obj.retest_date = retest_date
                batch_obj.batch_reference = batch_reference
            if inspection_lot_no or inspection_lot_no in [0, '0']:
                batch_obj.inspection_lot_number = inspection_lot_no
            if not draft:
                batch_obj.status = 1
            batch_obj.save()
        elif draft:
            batch_obj.status = 2
            batch_obj.save()

        if created and extra_attributes and batch_dict.get('extra_attributes'):
            save_master_attributes(batch_obj, batch_dict.get('extra_attributes'), extra_attributes)

        generate_batch_key(batch_obj, warehouse)

    except Exception as e:
        log.error('Batch Creation Error: %s', str(e))
        error_list.append(str(e))

    return error_list, batch_obj

def save_master_attributes(batch_obj, batch_dict, extra_attributes):
    attr_data = {}
    for key in extra_attributes:
        if key in batch_dict:
            attr_data[key] = batch_dict.get(key)
    if attr_data:
        create_or_update_master_attributes([batch_obj.id], 'extra_batch_attributes', [{batch_obj.id: attr_data}], batch_obj.warehouse)


def generate_batch_key(batch_detail, warehouse):
    batch_key = get_batch_key(batch_detail, warehouse)
    if batch_key:
        new_batch_key = BatchKey.objects.create(
            batch_identifier = batch_key,
            account_id = warehouse.userprofile.id,
            json_data = {"created_by": "system_generated"}
        )
        batch_detail.batch_identifier.add(new_batch_key)

def get_batch_key(batch_detail, warehouse):
    batch_key_obj = BatchDetail.objects.filter(id = batch_detail.id, batch_identifier__json_data__created_by = "system_generated")
    misc_type = ["enable_batch_key"]
    batch_key_config_values = get_multiple_misc_values(misc_type, warehouse.id)
    batch_key_prefix = warehouse.userprofile.stockone_code
    enable_batch_key = batch_key_config_values.get('enable_batch_key', "false")
    if enable_batch_key == "true" and not batch_key_obj.exists():
        incremental_number = get_incremental(warehouse, 'batch_key' , default_val = 1)
        seven_digit_number = str(incremental_number).zfill(7)
        batch_key_with_prefix= batch_key_prefix + str(seven_digit_number) if batch_key_prefix else seven_digit_number
        return batch_key_with_prefix
    return None


def get_incremental_batch_no(warehouse, batch_prefix='BATCH', length=0):
    '''
    Incremental BA to SA Reference
    '''
    batch_ref = "incremental_batch"
    _, _, batosa_reference, _, inc_status = get_user_prefix_incremental(
        warehouse, batch_ref, '',  job_order_ref = True, length = length
    )
    if inc_status:
        prefix_dict = {
            'user_id': warehouse.id, 'product_category': '',
            'sku_category': '', 'type_name': batch_ref,
            'prefix': batch_prefix, 'account_id': warehouse.userprofile.id
        }
        UserPrefixes.objects.create(**prefix_dict)
        _, _, batosa_reference, _, inc_status = get_user_prefix_incremental(
            warehouse, batch_ref, '', job_order_ref = True, length = length
        )
    return batosa_reference

def modify_batch_characters(warehouse , batch_details):
    try:
        batch_characters = get_misc_object('batch_character_mapping', warehouse.id)
        batch_character_mapping = {}
        if batch_characters:
            batch_character_mapping =  batch_characters.json_data or {}
        if not batch_character_mapping:
            return batch_details

        batch_keys = ['batch_no', 'batch_reference']

        def replace_batch_characters(value, mapping):
            return ''.join(mapping.get(char, char) for char in value)

        for key in batch_keys:
            if key in batch_details:
                batch_details[key] = replace_batch_characters(batch_details[key], batch_character_mapping)
    except Exception as e:
        log.error("Error in modifying batch characters and error is %s"% str(e))

    return batch_details

def validate_batch_details(warehouse, batch_details, update=False, cycle_count=False, validate_dates = True, validate_retest_date = True):
    '''
    Validated Batch Details with the existing Batch Details
    '''
    batch_details = modify_batch_characters(warehouse , batch_details)
    sku_code, warehouses = None, None
    company_level_batch = get_misc_value('company_level_batch', warehouse.id)
    if company_level_batch == 'true':
        sku_code = SKUMaster.objects.get(id = batch_details.get('sku_id')).sku_code
        warehouses = list(UserProfile.objects.filter(company_id=warehouse.userprofile.company.id, warehouse_level=3).values_list('user_id', flat=True))
    existing_batch_details, values_list = get_batch_data(
        warehouse, batch_details.get('sku_id'),
        batch_details.get('batch_no'), batch_details.get('batch_reference'),
        sku_code = sku_code, warehouses = warehouses
    )
    error_list, batch_id = validate_batch_detils_with_existing(
        warehouse, batch_details, existing_batch_details, values_list,
        update=update, cycle_count=cycle_count, validate_dates=validate_dates,
        validate_retest_date=validate_retest_date
    )
    return error_list, batch_id

def validate_batch_detils_with_existing(
        warehouse, batch_details, existing_batch_details, values_list, update=False,
        cycle_count=False, validate_dates=True, validate_retest_date=True
    ):
    '''
    Validate and return errors if batch_details not matches, and return obj if matches else
    return new_creation_list
    '''
    timezone_ = get_user_time_zone(warehouse)
    error_list, return_obj, invalid_keys = [], [], []
    uniq_key = (batch_details.get('sku_id'), batch_details.get('batch_no'))
    if existing_batch_details:
        existing_batch_details = existing_batch_details[0]

        #If only Batch Reference and not batch no given, assigning batch_no to the input batch details
        if not batch_details.get('batch_no') and batch_details.get('batch_reference'):
            if existing_batch_details.get('batch_no'):
                batch_details['batch_no'] = existing_batch_details['batch_no']

        #Validates Given Batch Attributes with existing batch attributes
        #and returns invalid keys
        invalid_keys = validate_batch_attributes(
            batch_details, existing_batch_details, values_list,
            timezone_, invalid_keys, update, cycle_count
        )
        if invalid_keys:
            error_list.append("Same Batch Cannot Have Multiple Batch Attributes, Validate %s Keys" % str(', '.join(invalid_keys)))
        if uniq_key not in error_list:
            return_obj = existing_batch_details.get('id')

    #Manufacture and Expiry Date CHeck
    manufactured_date, expiry_date, retest_date = (
        batch_details.get('manufactured_date'), batch_details.get('expiry_date'), batch_details.get('retest_date')
    )
    if validate_dates:
        error_list = validate_expiry_checks(manufactured_date, expiry_date, retest_date, error_list)

    if validate_retest_date:
        error_list = validate_retest_date_expiry(retest_date, error_list)

    return error_list, return_obj

def validate_batch_attributes(batch_details, existing_batch_details, values_list, timezone_, invalid_keys, update, cycle_count):
    date_formats = ['manufactured_date', 'expiry_date', 'retest_date']
    for key in values_list:

        exclude_keys = ['id', 'sku_id']
        if not cycle_count:
            exclude_keys.extend(['inspection_lot_number', 'retest_date'])

        if key in exclude_keys:
            continue

        if update and key in ['batch_reference']:
            continue
        if key in date_formats and key in batch_details:
            given_key, db_key = batch_details.get(key, None), existing_batch_details.get(key, None)
            if db_key:
                db_key = get_local_date_known_timezone(timezone_, db_key)

            if isinstance(given_key, str):
                given_key = datetime.datetime.strptime(given_key, "%Y-%m-%d")
                given_key = given_key.replace(tzinfo=timezone.utc)
                given_key = get_local_date_known_timezone(timezone_, given_key)
            elif given_key:
                given_key = given_key.strftime("%d %b, %Y %I:%M %p")

            if db_key != given_key:
                invalid_keys.append(key)
        else:
            if key in batch_details and batch_details.get(key, '') != existing_batch_details.get(key, ''):
                invalid_keys.append(key)
    return invalid_keys

def validate_expiry_checks(manufactured_date, expiry_date, retest_date, error_list):
    if manufactured_date and expiry_date and expiry_date < manufactured_date:
        error_list.append("The expiry date should be greater than the manufactured date.")
    if manufactured_date and retest_date and retest_date < manufactured_date:
        error_list.append("The retest date should be greater than the manufactured date.")

    return error_list

def validate_retest_date_expiry(retest_date, error_list):
    if retest_date and retest_date < timezone.now():
        error_list.append("The retest date should be greater than the current date")
    return error_list

def get_batch_data(warehouse, sku_ids, batch_no = None, batch_reference = None, sku_code = None, warehouses = None):
    batch_objs = []
    values_list = [
        'sku_id', 'batch_no', 'mrp',
        'manufactured_date', 'expiry_date',
        'weight', 'batch_reference', 'vendor_batch_no',
        'retest_date', 'inspection_lot_number', 'id'
    ]
    if sku_code and warehouses:
        filter_dict = {'warehouse_id__in': warehouses, 'sku__sku_code': sku_code}
    else:
        filter_dict = {'warehouse_id': warehouse.id, 'sku_id': sku_ids}
    if batch_no:
        filter_dict.update({'batch_no': batch_no})
    if batch_reference:
        filter_dict.update({'batch_reference': batch_reference})
    if batch_no or batch_reference:
        batch_objs = list(BatchDetail.objects.filter(**filter_dict).values(*values_list))

    return batch_objs, values_list

@get_warehouse
def get_batch_details(request, warehouse:User):
    request_data = request.GET
    sku_code, batch_no = request_data.get('sku_code'), request_data.get('batch_no')
    if not sku_code or not batch_no:
        return JsonResponse({'message': 'SKU Code and Batch No is Mandatory'}, status = 400)
    sku_obj = SKUMaster.objects.filter(user = warehouse.id, sku_code = sku_code)
    if not sku_obj:
        return JsonResponse({'message': 'Invalid SKU Code'}, status = 400)
    sku_id = sku_obj[0].id
    batch_details, _ = get_batch_data(warehouse,  sku_id, batch_no)
    timezone = get_user_time_zone(warehouse)
    for batch in batch_details:
        batch.update(
            (key, get_local_date_known_timezone(timezone, value, send_date=True).strftime("%d/%m/%Y"))
            for key, value in batch.items()
            if value and key in ['manufactured_date', 'expiry_date', 'retest_date', 'reevaluation_date', 'best_before_date']
        )
    batch_details = batch_details[0] if batch_details else {}
    return JsonResponse({"data": batch_details}, status = 200)

@get_warehouse
def get_stock_status_list(request, warehouse:User):
    '''
    Get stock status
    '''
    return JsonResponse(dict(INVENTORY_CHOICES), status=200)

class BatchDetailSet(WMSListView):

    def update_date_search_params(self, date_key, error_message):
        date_value = self.request_data.get(date_key)
        if date_value:
            date = parse_date_format(date_value)
            if date:
                self.search_params.update({date_key: date})
            else:
                self.errors.append(error_message)

    def get_search_params(self):

        company_level_batch = get_misc_value('company_level_batch', self.warehouse.id)
        self.search_params = {}
        # Check if this is a request for the batch details listing page
        is_listing_page = 'datatable' in self.request_data

        # For the batch details listing page, only show batches from the current warehouse
        if is_listing_page:
            self.search_params.update({'warehouse': self.warehouse.id})
        else:
            # For other requests, check if company_level_batch is enabled
            if company_level_batch == 'true':
                # fetch all warehouses under the same company
                warehouses = list(UserProfile.objects.filter(company_id=self.warehouse.userprofile.company.id, warehouse_level=3).values_list('user_id', flat=True))
                self.search_params.update({'warehouse__in': warehouses})
            else:
                self.search_params.update({'warehouse': self.warehouse.id})

        date_params = [
            ('creation_date__gte', 'Invalid Creation Date'),
            ('updation_date__gte', 'Invalid Updation Date'),
            ('creation_date__lt', 'Invalid Creation Date'),
            ('updation_date__lt', 'Invalid Updation Date'),
            ('expiry_date__gte', 'Invalid Expiry Date'),
            ('batch_identifier__creation_date__gte', 'Invalid Batch Key Creation Date'),
            ('batch_identifier__creation_date__lt', 'Invalid Batch Key Creation Date'),
        ]
        self.exclude_params = {}
        if self.request_data.get('sku_code'):
            self.search_params.update({'sku__sku_code': self.request_data.get('sku_code')})

        if self.request_data.get('batch_no'):
            self.search_params.update({'batch_no': self.request_data.get('batch_no')})

        if self.request_data.get('batch_reference'):
            self.search_params.update({'batch_reference': self.request_data.get('batch_reference')})

        for date_key, error_message in date_params:
            self.update_date_search_params(date_key, error_message)

        if self.request_data.get('vendor_batch_no'):
            self.search_params.update({'vendor_batch_no': self.request_data.get('vendor_batch_no')})

        if self.request_data.get('batch_key'):
            self.search_params.update({'batch_identifier__batch_identifier': self.request_data.get('batch_key')})

        if self.request_data.get('system_generated_batch_key', 'false') == 'true':
            self.search_params.update({'batch_identifier__json_data__created_by': 'system_generated'})

        if self.request_data.get('draft', 1) == 'false':
            self.exclude_params.update({'status': 2})


    def get(self, *args, **kwargs):
        self.set_user_credientials()
        request = self.request
        self.request_data = self.request.GET

        data_list, self.errors = [], []
        limit, total_count = 10, 0
        sku_attributes_dict = {}

        if self.request_data .get('limit'):
            limit = self.request_data ['limit']

        self.get_search_params()
        if self.errors:
            return JsonResponse({'error': self.errors}, status = 400)
        batch_objs = BatchDetail.objects.filter(**self.search_params).\
                        exclude(**self.exclude_params).order_by('-creation_date')
        total_count = batch_objs.count()
        page_info = scroll_data(request, batch_objs, limit=limit, request_type='GET')
        batch_objs = page_info['data']
        timezone = get_user_time_zone(self.warehouse)
        if batch_objs:
            batch_attributes = list(batch_objs.annotate(concatenated_identifiers=StringAgg(F('batch_identifier__batch_identifier'), delimiter=',')) .values(
                'id', 'sku__sku_code', 'batch_no', 'batch_reference', 'retest_date',
                'reevaluation_date', 'inspection_lot_number', 'weight', 'mrp',
                'receipt_number', 'manufactured_date', 'expiry_date',
                'transact_type', 'vendor_batch_no', 'best_before_date', 'creation_date', 'updation_date',
                'concatenated_identifiers', 'json_data', 'sku__sku_desc'
                )
            )

            sku_code_list, batch_ids = [],[]
            for batch_obj in batch_attributes:
                sku_code_list.append(batch_obj.get('sku__sku_code'))
                batch_ids.append(batch_obj.get('id'))

            batch_extra_attributes = get_extra_attributes(batch_ids, 'extra_batch_attributes')

            if 'send_sku_attributes' in self.request_data:
                sku_attributes_dict = get_custom_sku_attributes_and_values({'sku__user' : self.warehouse.id, 'sku__sku_code__in': sku_code_list})


            for batch_obj in batch_attributes:
                batch_id = batch_obj.get('id')
                extra_attributes = batch_extra_attributes.get(str(batch_id), {})

                manufactured_date, expiry_date, retest_date, best_before_date, reevaluation_date = (
                    batch_obj.get('manufactured_date'), batch_obj.get('expiry_date'),
                    batch_obj.get('retest_date'), batch_obj.get('best_before_date'),
                    batch_obj.get('reevaluation_date')
                )
                sku_code, sku_desc = batch_obj.get('sku__sku_code'), batch_obj.get('sku__sku_desc')

                data_dict = OrderedDict(
                    (
                        ('sku_code', sku_code),
                        ('sku_desc', sku_desc),
                        ('sku_info', f"{sku_code} ({sku_desc})"),
                        ('batch_no', batch_obj.get('batch_no')),
                        ('batch_reference', batch_obj.get('batch_reference')),
                        ('vendor_batch_no', batch_obj.get('vendor_batch_no')),
                        ('inspection_lot_number', batch_obj.get('inspection_lot_number')),
                        ('mrp', batch_obj.get('mrp', 0)),
                        ('manufactured_date', format_date(timezone, manufactured_date) if manufactured_date else None),
                        ('expiry_date', format_date(timezone, expiry_date) if expiry_date else None),
                        ('retest_date', format_date(timezone, retest_date) if retest_date else None),
                        ('reevaluation_date', format_date(timezone, reevaluation_date) if reevaluation_date else None),
                        ('best_before_date', format_date(timezone, best_before_date) if best_before_date else None),
                        ('creation_date', get_local_date_known_timezone(timezone, batch_obj.get('creation_date'))),
                        ('updation_date', get_local_date_known_timezone(timezone, batch_obj.get('updation_date'))),
                        ('batch_keys',batch_obj.get('concatenated_identifiers').split(',') if batch_obj.get('concatenated_identifiers') else []),
                        ('split_batch_keys',batch_obj.get('concatenated_identifiers', '')),
                        ('json_data',batch_obj.get('json_data') if batch_obj.get('json_data') else {}),
                        ('extra_attributes', extra_attributes)
                    )
                )
                if 'send_sku_attributes' in self.request_data:
                    sku_code = batch_obj.get('sku__sku_code')
                    data_dict['sku_attributes'] = sku_attributes_dict.get(sku_code, {})
                data_dict.update(extra_attributes)
                data_list.append(data_dict)

        page_info['data'] = data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = total_count
        page_info['error'] = [{'message': ''}]
        return page_info

    def put(self, *args, **kwargs):
        '''Updates BatchDetails'''
        self.errors = False
        self.set_user_credientials()
        self.request_data = loads(self.request.body)
        if not isinstance(self.request_data, list) or not self.request_data:
            return JsonResponse({'error': "Invalid Payload"}, status = 400)

        update_stock_status = get_misc_value('update_inventory_expiry_status', self.warehouse.id)
        try:
            #GET Batch Detail Fields
            self.get_batch_detail_fields()

            #GET Available Batch Details
            self.get_available_batches()

            #Validate Batch Details
            self.validate_batch_details()

            #Update Batch Detail
            self.update_batch_details()

            if update_stock_status == 'true':
                # Update Stock Status to Expired / NEAR_EXPIRED
                self.update_stock_status_to_expired_near_expired()

            message, status = self.get_error_status_details()

            return JsonResponse({'message' : message}, status=status)

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            message = 'Batch Detail Update failed for %s, exception %s. Data: %s'%(
                self.user.username,str(e),self.request.body
            )
            log.info(message)
            return JsonResponse({'error': 'Batch Detail Updation Failed'}, status = 400)

    def update_stock_status_to_expired_near_expired(self):
        """
        Updates StockDetail.status based on expiry date changes captured in self.expiry_details.
        Applies:
        - EXPIRED if new_expiry <= now
        - NEAR_EXPIRED if shelf life ≤ 0
        - AVAILABLE if shelf life > 0 (on extension)
        """
        EXPIRED = 7
        NEAR_EXPIRED = 8
        AVAILABLE = 1

        if not hasattr(self, 'expiry_details') or not self.expiry_details:
            return

        batch_ids = list(self.expiry_details.keys())
        stocks = StockDetail.objects.filter(batch_detail_id__in=batch_ids).select_related('batch_detail__sku')
        now = datetime.datetime.utcnow()
        bulk_update_stocks = []

        def calculate_shelf_life_days(expiry_date, customer_shelf_life):
            """
            Returns remaining shelf life in percentage.
            """
            expiry_date = expiry_date.replace(tzinfo=None)
            customer_shelf_life = customer_shelf_life.total_seconds() / timedelta(days=1).total_seconds()
            calc1_date = expiry_date - datetime.timedelta(
                hours=customer_shelf_life * 24)
            try:
                date_diff = calc1_date.replace(tzinfo=None) - datetime.datetime.utcnow()
            except Exception as e:
                date_diff = datetime.datetime.combine(calc1_date, datetime.time(00, 00)) - datetime.datetime.utcnow()
            shelf_life = round(((date_diff.total_seconds()/60)/60)/24, 2)
            return shelf_life

        for stock in stocks:
            batch = stock.batch_detail
            batch_id = batch.id
            change = self.expiry_details.get(batch_id)
            if not change:
                continue

            old_expiry = change['old_expiry_date'].replace(tzinfo=None)
            new_expiry = change['new_expiry_date'].replace(tzinfo=None)
            customer_shelf_life = getattr(batch.sku, 'customer_shelf_life', None)

            new_status = None
            reason = None

            # Reduction cases
            if new_expiry < old_expiry:
                if new_expiry <= now:
                    new_status = EXPIRED
                    reason = 'Manual - Expiry Date Reduction'
                elif customer_shelf_life:
                    shelf_life = calculate_shelf_life_days(new_expiry, customer_shelf_life)
                    if shelf_life <= 0:
                        new_status = NEAR_EXPIRED
                        reason = 'Manual - Expiry Date Reduction'

            # Extension cases
            elif new_expiry > old_expiry and customer_shelf_life:
                if stock.status == EXPIRED and new_expiry > now:
                    new_status = AVAILABLE
                    reason = 'Manual - Expiry Date Extension'
                elif stock.status == NEAR_EXPIRED:
                    shelf_life = calculate_shelf_life_days(new_expiry, customer_shelf_life)
                    if shelf_life > 0:
                        new_status = AVAILABLE
                        reason = 'Manual - Expiry Date Extension'

            if new_status is not None:
                stock.status = new_status
                stock.remarks = reason
                bulk_update_stocks.append(stock)

        if bulk_update_stocks:
            StockDetail.objects.bulk_update(bulk_update_stocks, ['status', 'remarks'])

    def get_error_status_details(self):
        message, status = "Updated Successfully", 200
        if self.rejected_data and not self.validated_data:
            message, status =  self.rejected_data, 400
        elif self.rejected_data and self.validated_data:
            message, status = self.rejected_data, 207
        return message, status

    def get_batch_detail_fields(self):
        self.batch_detail_fields = [
            'sku_id', 'batch_no', 'buy_price', 'mrp',
            'manufactured_date', 'expiry_date',
            'weight', 'batch_reference', 'vendor_batch_no',
            'retest_date', 'reevaluation_date',
            'best_before_date', 'inspection_lot_number', 'sku_code',
            'json_data'
        ]

        self.date_formats = [
            'manufactured_date', 'expiry_date', 'retest_date',
            'reevaluation_date', 'best_before_date'
        ]

    def validate_batch_details(self):
        '''
        Validates Request Data
        '''
        given_request_data = self.request_data
        self.validated_data = []
        self.rejected_data = []

        for request_data in given_request_data:
            #Validate Batch Detail keys
            errors = ["Invalid Batch Detail key: %s" % key
                for key in request_data
                if key not in self.batch_detail_fields]
            request_data['errors'] = []
            request_data['errors'].extend(errors)

            #SKU and Batch Details Mandatory Check
            sku_code, batch_no, batch_reference, vendor_batch_no = (
                request_data.get('sku_code'), request_data.get('batch_no'),
                request_data.get('batch_reference'), request_data.get('vendor_batch_no')
            )
            if not sku_code or not (batch_no or batch_reference or vendor_batch_no):
                request_data['errors'].append("SKU and Batch Details are Mandatory")

            #Validate Dates
            for date in self.date_formats:
                if request_data.get(date):
                    is_valid = self.check_date_format(request_data.get(date))
                    if not is_valid:
                        request_data['errors'].append(
                            "Invalid Date Format for %s, It should be (YYYY-MM-DD)" % str(date)
                        )

            #Manufacture and Expiry Check
            expiry_date, manufactured_date = (
                request_data.get('expiry_date'), request_data.get('manufactured_date')
            )
            if expiry_date and manufactured_date and expiry_date < manufactured_date:
                request_data['errors'].append("Expiry Date Should be Greater than Manufactured Date")

            #Validating Batch Details from DB
            unique_reference = self.prepare_filter_dict(request_data)

            if (unique_reference not in self.available_batches_with_batch_nos) and (unique_reference not in self.available_batches_with_batch_references):
                request_data['errors'].append("Invalid Batch Details")

            if not request_data.get('errors'):
                self.validated_data.append(request_data)
            else:
                self.rejected_data.append(request_data)

    def get_available_batches(self):
        # Initialize dictionaries to hold batches with batch numbers and references
        self.available_batches_with_batch_nos, self.available_batches_with_batch_references = {}, {}

        # Initialize sets to hold unique values
        sku_codes, batch_nos, batch_references = set(), set(), set()

        # Iterate through request_data once and collect all unique values
        for data in self.request_data:
            sku_codes.add(data.get('sku_code'))
            batch_nos.add(data.get('batch_no')) if data.get('batch_no') else None
            batch_references.add(data.get('batch_reference')) if data.get('batch_reference') else None

        company_level_batch = get_misc_value('company_level_batch', self.warehouse.id)
        batch_filters = {'sku__sku_code__in': sku_codes}

        if company_level_batch == 'true' and hasattr(self.warehouse.userprofile, 'company'):
            # If company level batch is enabled, get all warehouses in the company
            company_id = self.warehouse.userprofile.company.id
            warehouses = list(UserProfile.warehouse_objects.get_company_warehouses(
                company_id=company_id
            ).values_list('user_id', flat=True))

            batch_filters['warehouse_id__in'] = warehouses
        else:
            # Otherwise, limit to the current warehouse
            batch_filters['warehouse'] = self.warehouse

        if batch_nos:
            batch_filters['batch_no__in'] = batch_nos
        if batch_references:
            batch_filters['batch_reference__in'] = batch_references

        if batch_nos or batch_references:
            # Query BatchDetail objects based on the constructed filters
            batch_objs = BatchDetail.objects.select_related('sku').filter(**batch_filters)

            # Populate available batches dictionaries
            for batch_obj in batch_objs:
                sku_code = batch_obj.sku.sku_code
                # Handle batch_no
                if batch_obj.batch_no:
                    key = (sku_code, batch_obj.batch_no)
                    if key not in self.available_batches_with_batch_nos:
                        self.available_batches_with_batch_nos[key] = []
                    self.available_batches_with_batch_nos[key].append(batch_obj)

                # Handle batch_reference
                if batch_obj.batch_reference:
                    key = (sku_code, batch_obj.batch_reference)
                    if key not in self.available_batches_with_batch_references:
                        self.available_batches_with_batch_references[key] = []
                    self.available_batches_with_batch_references[key].append(batch_obj)

    def prepare_filter_dict(self, request_data):
        unique_key = ()
        sku_code = request_data.get('sku_code')
        if request_data.get('batch_reference'):
            unique_key = (sku_code, request_data.get('batch_reference'))
        if request_data.get('batch_no'):
            unique_key = (sku_code, request_data.get('batch_no'))

        return unique_key

    def check_date_format(self, date):
        try:
            datetime.datetime.strptime(date, date_format)
            return True
        except ValueError:
            return False

    def update_batch_details(self):
        '''
        Update Batch Details
        '''
        user_timezone = self.warehouse.userprofile.timezone
        timezone = user_timezone if user_timezone else 'Asia/Calcutta'
        requested_data = self.validated_data
        bulk_update_batches = []

        self.expiry_details = {}

        for request_data in requested_data:

            #Validating Batch Details from DB
            unique_reference = self.prepare_filter_dict(request_data)
            batch_objs = (self.available_batches_with_batch_nos.get(unique_reference)) or (self.available_batches_with_batch_references.get(unique_reference))
            if not batch_objs:
                continue

            for batch_obj in batch_objs:
                field_attributes = ['batch_no', 'batch_reference', 'buy_price', 'inspection_lot_number', 'mrp', 'json_data']

                for field in field_attributes:
                    if field == 'json_data' and request_data.get(field):
                        json_data = request_data.get(field)
                        if "batch_wac" in  json_data:
                            batch_wac = None
                            with contextlib.suppress(Exception):
                                batch_wac = float(json_data.pop("batch_wac",None))
                            json_data_dict = {
                                "batch_wac": batch_wac,
                                "batch_wac_updated_by": self.user.username,
                                "batch_wac_updated_date": str(datetime.datetime.now()),
                                **json_data
                            }
                            setattr(batch_obj, field, json_data_dict)
                            continue
                    if request_data.get(field):
                        setattr(batch_obj, field, request_data.get(field))

                for date in self.date_formats:
                    if not request_data.get(date):
                        continue
                    ist_timezone = pytz.timezone(timezone)
                    year, month, day, hour, minute, second, _, _, _ = datetime.datetime.strptime(request_data.get(date), date_format).timetuple()
                    formatted_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))
                    if date in ['expiry_date', 'best_before_date', 'retest_date']:
                        hour, minute = 23, 59
                        formatted_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))

                    #Old and New Expiry Dates
                    if date == 'expiry_date' and batch_obj.expiry_date != formatted_date:
                        self.expiry_details[batch_obj.id] = {'old_expiry_date': batch_obj.expiry_date, 'new_expiry_date': formatted_date}

                    setattr(batch_obj, date, formatted_date)

                bulk_update_batches.append(batch_obj)

        self.batch_detail_fields.remove('sku_code')
        if bulk_update_batches:
            BatchDetail.objects.bulk_update_with_rounding(bulk_update_batches,  self.batch_detail_fields)

    def post(self, *args, **kwargs):
        log.info("POST Request with user name %s and arguments %s, %s" % (str(self.request.user),str(args),str(kwargs)))
        self.set_user_credientials()
        warehouse = self.warehouse
        request_data = json.loads(self.request.body)
        _, validated_list, error_list  = batch_attributes_bulk_validation(warehouse, request_data)
        message, status = self.get_status_details(validated_list, error_list)
        return JsonResponse({'message': message}, status = status)

    def get_status_details(self, validated_list, error_list):
        message, status = "Batch Details Created Successfully", 200
        if error_list and not validated_list:
            message, status =  error_list, 400
        elif error_list and validated_list:
            message, status = error_list, 207
        return message, status

def get_sku_pack_data_with_sku_ids(sku_ids):
    '''Fetching SKUPack Details with SKU ids'''
    sku_pack_dict = {}
    sku_packs = list(
        SKUPackMaster.objects.filter(sku_id__in = sku_ids, status = 1).order_by('-pack_quantity').values('sku_id', 'pack_quantity', 'pack_id')
    )
    for each_pack in sku_packs:
        sku_pack_dict.setdefault(each_pack['sku_id'], []).append(each_pack)

    return sku_pack_dict

def get_sku_pack_repr(sku_pack_qtys, location_quantity):
    pack_repr_lis = []
    for sku_pack in sku_pack_qtys:
        if not location_quantity or not sku_pack['pack_quantity']:
            break
        if sku_pack['pack_quantity'] <= location_quantity:
            alloted = int(location_quantity/sku_pack['pack_quantity'])
            if alloted:
                alloted_qty = alloted * sku_pack['pack_quantity']
                pack_repr_lis.append('%s %s' % (str(alloted), sku_pack['pack_id']))
                location_quantity -= alloted_qty
    else:
        if location_quantity:
            pack_repr_lis.append('%s %s' % (str(int(location_quantity)), 'Ea'))
    pack_repr = ', '.join(pack_repr_lis)
    return pack_repr

class CustomStockLedger(WMSListView):

    def save_file_path(self, excel_file, status, generated_report_id=''):
        data = {
            "warehouse": self.warehouse,
            "report": self.report,
            "user": self.request_user
        }

        if generated_report_id:
            generated_report = GeneratedReport.objects.get(id=generated_report_id)
        else:
            generated_report = GeneratedReport(**data)

        generated_report.generated_file = excel_file
        generated_report.status = status
        generated_report.save()

        return generated_report.id

    def validate_warehouses(self, user_warehouses, report_warehouses):
        if set(report_warehouses).issubset(set(user_warehouses)):
            return None

        invalid_warehouses = set(report_warehouses) - set(user_warehouses)
        return list(invalid_warehouses)

    def get(self,*args, **kwargs):
        self.set_user_credientials()
        request = self.request
        request_data = request.GET

        #validate warehouses
        try:
            try:
                report_warehouses = ast.literal_eval(request_data.get("Warehouse", '[]'))
                invalid_warehouses = self.validate_warehouses(self.request.accessible_warehouses.keys(), report_warehouses)
                if invalid_warehouses:
                    return JsonResponse({"message": "Invalid Warehouses: %s" % ', '.join(invalid_warehouses)}, status=400)
            except Exception as e:
                log.warning("invalid warehouse key passed: %s with error %s" % (request_data.get("Warehouse"), str(e)))
                return JsonResponse({"message": "Invalid Warehouses Requested"}, status=400)
            self.report = Report.objects.get(id=request_data['id'])
            self.request_user = self.request.user
            self.generated_report = self.save_file_path('', 'Running')
            df = prepare_custom_stock_ledger_data(request_data, self.warehouse.id)
            new_columns = [col.replace("_", " ") for col in df.columns]
            df.rename(columns=dict(zip(df.columns, new_columns)), inplace=True)
            time_stamp = datetime.datetime.now().strftime('%Y%m%d%H%M')
            file_name = str(self.report.name) + str(time_stamp) + ".csv"
            path = 'static/excel_files/'
            folder_check(path)
            path = path + file_name
            df.to_csv(path, index=False, header=True, quoting=1)
            excel_file = File(open(path, 'rb'),name=file_name)
            self.save_file_path(excel_file, 'Completed', self.generated_report)
            if os.path.exists(path):
                os.remove(path)
            return JsonResponse({"status": "Success"}, status=200)
        except Exception as e:
            log.info(e)
            self.save_file_path('', 'Failed', self.generated_report)
            return JsonResponse({"status": "Report Generation Failed"}, status= 400)

def get_inventory_callback(warehouse, sku_codes, extra_filter = {}):
    factory = RequestFactory()
    request_data = {'sku_code': ','.join(sku_codes), 'limit': 10000, 'callback': 'true'}
    inventory_detail_set = InventoryDetailSet()
    if 'location__zone_id__in' in extra_filter:
        zones = list(ZoneMaster.objects.filter(id__in = extra_filter['location__zone_id__in']).values_list('zone', flat=True))
        request_data['zone'] = ','.join(zones)
    elif extra_filter.get('zones'):
        request_data['zone'] = ','.join(extra_filter['zones'])

    if extra_filter.get('detail_view'):
        request_data['detail_view'] = extra_filter['detail_view'][0] if isinstance(extra_filter['detail_view'], list) else extra_filter['detail_view']

    request = factory.get('', request_data)
    request.user = warehouse
    request.warehouse = warehouse
    inventory_detail_set.request = request
    callback_data = inventory_detail_set.get()
    return callback_data

class InventoryDetailSet(WMSListView):

    def get_search_params(self):
        (
            self.asn_search_params, self.pending_putaway_search_params,
            self.reserved_search_params, self.sku_search_params,
            self.open_order_search_params, self.combo_search_params
        )= {}, {}, {}, {}, {}, {}

        self.is_combo_details_required = get_misc_value('combo_details_in_inventory_callback', self.warehouse.id)
        self.staging_filter = False
        self.show_open_quantity = True
        if self.request_data.get('sku_code'):
            sku_codes = self.request_data.get('sku_code').split(',')
            self.sku_search_params['sku_code__in'] = sku_codes
            self.pending_putaway_search_params['sku__sku_code__in'] = sku_codes
            self.asn_search_params['purchase_order__open_po__sku__sku_code__in'] = sku_codes
            self.reserved_search_params['sku__sku_code__in'] = sku_codes
            self.open_order_search_params['sku__sku_code__in'] = sku_codes

        if self.request_data.get('zone'):
            zones = self.request_data.get('zone').split(',')
            self.sku_search_params['stockdetail__location__zone__zone__in'] = zones
            self.combo_search_params['stockdetail__location__zone__zone__in'] = zones
            self.reserved_search_params['location__zone__zone__in'] = zones
            order_type_zone_mapping = OrderTypeZoneMapping.objects.filter(status = 1, user_id = self.warehouse.id)
            if order_type_zone_mapping.exists():
                order_types = list(order_type_zone_mapping.filter(zone__zone__in = zones).values_list('order_type', flat=True))
                if not order_types:
                   self.show_open_quantity = False
                self.open_order_search_params['order_type__in'] = order_types
            self.staging_filter = True

        if self.request_data.get('location'):
            location = self.request_data.get('location')
            self.sku_search_params['stockdetail__location__location'] = location
            self.combo_search_params['stockdetail__location__location'] = location
            self.reserved_search_params['location__location'] = location
            self.staging_filter = True

        if self.request_data.get('updated_date'):
            parsed_date = parser.parse(self.request_data.get('updated_date'))
            localized_date = pytz.timezone(self.timezone).localize(parsed_date)
            self.sku_search_params['updation_date__gte'] = localized_date


    def get_open_asn_details(self):

        # Initialize dictionaries for SKU and batch level data
        self.asn_sku_ids, self.asn_details_dict = [], {}
        self.batch_level_asn_dict = {}  # Dictionary for batch-level ASN data

        # Set up values and filter parameters
        filter_params = {'asn_user': self.warehouse.id, 'status__in': [1, 2], 'quantity__gt': 0}
        filter_params.update(self.asn_search_params)

        values_list = [
            'purchase_order__open_po__sku_id'
        ]
        if self.detail_view in ['batch', 'location_batch']:
            values_list.append('batch_detail_id')

        # Get all ASN data in a single query
        asn_data = ASNSummary.objects.filter(**filter_params).values(
            *values_list
        ).annotate(asn_qty=Sum('quantity'))

        # Process data for both SKU and batch level
        for item in asn_data:
            sku_id = item.get('purchase_order__open_po__sku_id')
            qty = item.get('asn_qty', 0)

            # SKU level data
            if sku_id not in self.asn_sku_ids:
                self.asn_sku_ids.append(sku_id)
            self.asn_details_dict[sku_id] = self.asn_details_dict.get(sku_id, 0) + qty

            # Batch level data
            key = (sku_id,)
            if self.detail_view in ['batch', 'location_batch']:
                key += (item.get('batch_detail_id'),)
            
            if len(key) > 1:
                self.batch_level_asn_dict[key] = self.batch_level_asn_dict.get(key, 0) + qty

    def get_pending_putaway_details(self):
        # Initialize dictionaries
        self.pending_putaway_sku_ids, self.pending_putaway_details_dict = [], {}
        self.batch_level_pending_putaway_dict = {}  # For batch-level data

        values_list = ['sku_id']
        if self.detail_view in ['batch', 'location_batch']:
            values_list.append('batch_detail_id')

        if self.detail_view == 'location_batch':
            values_list.append('location_id')

        # Get all pending putaway data in a single query
        pending_data = POLocation.objects.exclude(putaway_type='cancelled_picklist').filter(
            status=1, sku__user=self.warehouse.id, quantity__gt=0,
            **self.pending_putaway_search_params
        ).values(*values_list).annotate(qty=Sum('quantity'))

        # Process data for both SKU and batch level
        for item in pending_data:
            sku_id = item.get('sku_id')
            qty = item.get('qty', 0)

            # SKU level data
            if sku_id not in self.pending_putaway_sku_ids:
                self.pending_putaway_sku_ids.append(sku_id)
            self.pending_putaway_details_dict[sku_id] = self.pending_putaway_details_dict.get(sku_id, 0) + qty

            # Batch level data - no longer using location
            batch_id = item.get('batch_detail_id')
            key = (sku_id,)
            if self.detail_view in ['batch', 'location_batch']:
                key += (batch_id,)
            if self.detail_view == 'location_batch':
                location_id = item.get('location_id')
                key += (location_id,)
            if len(key) > 1:
                self.batch_level_pending_putaway_dict[key] = self.batch_level_pending_putaway_dict.get(key, 0) + qty

    def get_reserved_quantity_details(self):
        # Initialize dictionaries
        self.reserved_sku_ids, self.reserved_dict = [], {}
        self.batch_level_reserved_dict = {}

        # Create filter parameters
        filter_params = {'status': 'open', 'user': self.warehouse.id, 'reserved_quantity__gt': 0}
        if self.updated_sku_codes:
            self.reserved_search_params.update({'sku__sku_code__in': self.updated_sku_codes})

        # Combine filter parameters
        picklist_filter = filter_params.copy()
        picklist_filter.update(self.reserved_search_params)

        values_list = ['stock__sku_id']
        if self.detail_view in ['batch', 'location_batch']:
            # If filter level is batch or location_batch, include batch_detail_id
            values_list.append('stock__batch_detail_id')
        if self.detail_view == 'location_batch':
            values_list.append('stock__location_id')

        # OPTIMIZATION: Get all picklist data in a single query
        picklist_data = Picklist.objects.filter(
            **picklist_filter
        ).values(
            *values_list
        ).annotate(
            reserved_qty=Sum('reserved_quantity')
        )

        # Process picklist data for both SKU-level and batch-level dictionaries
        sku_reserved_dict = {}

        for item in picklist_data:
            sku_id = item.get('stock__sku_id')
            batch_id = item.get('stock__batch_detail_id')
            reserved_qty = item.get('reserved_qty', 0)

            # Add to SKU-level dictionary
            current_qty = sku_reserved_dict.get(sku_id, 0)
            sku_reserved_dict[sku_id] = current_qty + reserved_qty

            # Add to batch-level dictionary if batch information exists
            key = (sku_id,)
            if self.detail_view in ['batch', 'location_batch']:
                key += (batch_id,)
            if self.detail_view == 'location_batch':
                # If filter level is location_batch, include location_id in the key
                location_id = item.get('stock__location_id')
                key += (location_id,)

            if len(key) > 1:
                # Update the batch level reserved dictionary
                self.batch_level_reserved_dict[key] = self.batch_level_reserved_dict.get(key, 0) + reserved_qty

        # Update self.reserved_dict and self.reserved_sku_ids
        for sku_id, qty in sku_reserved_dict.items():
            if sku_id not in self.reserved_sku_ids:
                self.reserved_sku_ids.append(sku_id)
            self.reserved_dict[sku_id] = qty

        # Now handle allocation data similarly
        allocation_filter_params = {
            'status': 1,
            'warehouse_id': self.warehouse.id,
        }
        if self.updated_sku_ids:
            allocation_filter_params.update({'stock__sku_id__in': self.updated_sku_ids})

        # OPTIMIZATION: Get all allocation data in a single query
        allocation_data = StockAllocation.objects.filter(
            **allocation_filter_params
        ).values(
            *values_list
        ).annotate(
            allocated_qty=Sum('quantity')
        )

        # Process allocation data for both SKU-level and batch-level dictionaries
        for item in allocation_data:
            sku_id = item.get('stock__sku_id')
            batch_id = item.get('stock__batch_detail_id')
            allocated_qty = item.get('allocated_qty', 0)

            # Add to SKU-level dictionary
            if sku_id not in self.reserved_sku_ids:
                self.reserved_sku_ids.append(sku_id)

            current_qty = self.reserved_dict.get(sku_id, 0)
            self.reserved_dict[sku_id] = current_qty + allocated_qty

            # Update the batch level reserved dictionary
            if len(key) > 1:
                current_batch_qty = self.batch_level_reserved_dict.get(key, 0)
                self.batch_level_reserved_dict[key] = current_batch_qty + allocated_qty

    def get_open_order_quantity_detials(self):
        self.open_order_sku_ids, self.open_order_dict, self.open_order_details_dict = [], {}, {}
        if self.show_open_quantity:
            values_list = ['sku_id', 'sku__sku_code']
            annotated_values = {'open_order_quantity': Sum('quantity')}
            filter_params = {'user': self.warehouse.id, 'quantity__gt': 0, 'status': 1}
            if self.updated_sku_codes:
                self.open_order_search_params.update({'sku__sku_code__in': self.updated_sku_codes})

            open_orders = OrderDetail.objects.filter(**filter_params,  **self.open_order_search_params).values(*values_list).annotate(**annotated_values)
            for data in open_orders:
                self.open_order_sku_ids.append(data.get('sku_id'))
                self.open_order_dict[data.get('sku_id')] = data.get('open_order_quantity')
                self.open_order_details_dict[data.get('sku__sku_code')] = data.get('open_order_quantity')

    def get_master_data(self):
        self.child_parent_details, self.parent_combo_sku_detail, self.combo_sku_code_list, self.parent_sku_code_list = {}, {}, set(), set()
        updated_sku_codes = []
        self.updated_sku_ids =[]
        required_data_list = [
            'id', 'sku_code', 'sku_desc', 'cost_price',
            'scan_picking', 'batch_based'
        ]
        annotate_dict = {'stockdetailquantity': Sum('stockdetail__quantity')}
        required_data_dict = {
            'sellerid': F('seller__supplier_id'),
            'seller_name': F('seller__name'),
        }
        if self.detail_view in ['batch', 'location_batch']:
            required_data_dict.update({
                'mfg_date': F('stockdetail__batch_detail__manufactured_date'),
                'exp_date': F('stockdetail__batch_detail__expiry_date'),
                'retest_date': F('stockdetail__batch_detail__retest_date'),
                'reevaluation_date': F('stockdetail__batch_detail__reevaluation_date'),
                'best_before_date': F('stockdetail__batch_detail__best_before_date'),
                'inspection_lot_no': F('stockdetail__batch_detail__inspection_lot_number'),
                'batch_mrp': F('stockdetail__batch_detail__mrp'),
                'batch_no': F('stockdetail__batch_detail__batch_no'),
                'buy_price': F('stockdetail__batch_detail__buy_price'),
                'batch_reference': F('stockdetail__batch_detail__batch_reference'),
                'batch_detail_id': F('stockdetail__batch_detail_id'),
                'vendor_batch_no': F('stockdetail__batch_detail__vendor_batch_no')
            })
        if self.detail_view == 'location_batch':
            required_data_dict.update({
                'location_id': F('stockdetail__location_id'),
                'location': F('stockdetail__location__location'),
                'stock_zone': F('stockdetail__location__zone__zone')
            })
        filter_dict = {'user': self.warehouse.id, 'status': 1}

        if 'updation_date__gte' in self.sku_search_params:
            stock_sku_ids = list(
                StockDetail.objects.filter(
                    updation_date__gte = self.sku_search_params['updation_date__gte'],
                    sku__user = self.warehouse.id
                    ).values_list('sku_id', flat=True)
                )
            order_sku_ids = list(
                OrderDetail.objects.filter(
                    updation_date__gte = self.sku_search_params['updation_date__gte'],
                    user = self.warehouse.id
                ).values_list('sku_id', flat=True)
            )
            picklist_sku_ids = list(
                Picklist.objects.filter(
                    updation_date__gte = self.sku_search_params['updation_date__gte'],
                    user = self.warehouse.id
                ).values_list('sku_id', flat=True)
            )

            po_location_sku_ids = list(
                POLocation.objects.filter(
                    updation_date__gte = self.sku_search_params['updation_date__gte'],
                    sku__user = self.warehouse.id
                ).values_list('sku_id', flat=True)
            )

            del self.sku_search_params['updation_date__gte']
            filter_dict.update({'id__in': stock_sku_ids + order_sku_ids + picklist_sku_ids + po_location_sku_ids})

        _, self.batch_level_cycle_stock_data, self.total_cycle_stock_data = get_short_pick_cycle_status(
            self.warehouse.id, detail_view = self.detail_view
        )

        # Get distinct SKUs for pagination
        sku_query = SKUMaster.objects.filter(
            **filter_dict, **self.sku_search_params
        ).distinct()

        # Count and paginate at the SKU level
        self.total_count = sku_query.count()
        self.page_info = scroll_data(self.request, sku_query, limit=self.limit, request_type='GET')

        # Get the paginated SKU IDs
        paginated_sku_ids = [sku.id for sku in self.page_info['data']]

        filter_dict.update({'id__in': paginated_sku_ids})
        # Now fetch the complete data for the paginated SKUs
        master_data = SKUMaster.objects.filter(
            **filter_dict, **self.sku_search_params
        ).values(*required_data_list, **required_data_dict).annotate(**annotate_dict).order_by('id')

        if self.is_combo_details_required == 'true':
            master_data = self.check_and_return_combo_sku_master_data(master_data, required_data_list, required_data_dict, annotate_dict)
            updated_sku_codes, self.updated_sku_ids = [], []
            if master_data:
                sku_id_pairs = [(data.get("sku_code"), str(data.get("id"))) for data in master_data if data.get("sku_code") and data.get("id")]
                if sku_id_pairs:
                    updated_sku_codes, self.updated_sku_ids = zip(*sku_id_pairs)
                    updated_sku_codes, self.updated_sku_ids = list(updated_sku_codes), list(self.updated_sku_ids)

        sku_ids = [str(data.get('id')) for data in master_data]

        self.staging_stock_data, self.batch_level_staging_stock_dict = get_staging_stock_sum_dict(
            self.warehouse.id, self.detail_view, sku_ids, self.staging_filter
        )

        request_dict = {
            'filters': {
                'sku_id__in': sku_ids,
                'status': 1
            }
        }
        serial_mixin = SerialNumberMixin(
            user=None,
            warehouse=self.warehouse,
            request_data=request_dict
        )
        serial_number_dict = serial_mixin.get_serial_numbers()

        return master_data, updated_sku_codes, serial_number_dict

    def fetch_sku_master_data(self, sku_codes, extra_filters=None, required_data_list=None, required_data_dict=None, annotate_dict=None):
        filters = {
            'user': self.warehouse.id,
            'sku_code__in': sku_codes,
        }
        if extra_filters:
            filters.update(extra_filters)
        return list(SKUMaster.objects.filter(**filters)
                            .values(*(required_data_list), **(required_data_dict))
                            .annotate(**(annotate_dict))
                            .order_by('-sku_code'))

    def check_and_return_combo_sku_master_data(self, master_data, required_data_list, required_data_dict, annotate_dict):
        # Extract current SKU codes from provided master data
        existing_sku_codes = {data.get('sku_code') for data in master_data if data.get('sku_code')}

        # Populate combo SKU-related data
        self.get_combo_sku_related_sku_code(existing_sku_codes)

        combined_master_data = list(master_data)

        # Only fetch additional combo SKUs if they're related to our paginated SKUs
        # This ensures we maintain pagination at the SKU level
        if self.combo_search_params:
            # When combo search params are provided, split child and parent SKUs
            sku_fetch_plan = [
                {
                    "skus": set(self.combo_sku_code_list) - existing_sku_codes,
                    "filters": self.combo_search_params
                },
                {
                    "skus": set(self.parent_sku_code_list) - existing_sku_codes,
                    "filters": None
                }
            ]
        else:
            # When no combo search params, fetch all at once
            sku_fetch_plan = [
                {
                    "skus": set(self.combo_sku_code_list).union(self.parent_sku_code_list) - existing_sku_codes,
                    "filters": None
                }
            ]

        # Fetch and combine master data
        for fetch_task in sku_fetch_plan:
            if fetch_task["skus"]:
                fetched_data = self.fetch_sku_master_data(
                    list(fetch_task["skus"]),
                    extra_filters=fetch_task["filters"],
                    required_data_list=required_data_list,
                    required_data_dict=required_data_dict,
                    annotate_dict=annotate_dict
                )
                combined_master_data += fetched_data

        return combined_master_data

    def get_combo_sku_related_sku_code(self, sku_codes):

        # Step 1: Fetch all SKURelation records related to the initial sku_codes
        combo_filters = {'product_sku__user': self.warehouse.id, 'bom_type': 1, 'status': 1}

        combo_sku_data = list(
            BOMMaster.objects.filter(
                (Q(product_sku__sku_code__in=sku_codes) | Q(material_sku__sku_code__in=sku_codes)), **combo_filters
            ).values('product_sku__sku_code', 'material_sku__sku_code')
        )

        parent_sku_codes, child_sku_codes = [], []
        for combo_sku in combo_sku_data:
            parent_sku_codes.append(combo_sku.get('product_sku__sku_code'))
            child_sku_codes.append(combo_sku.get('material_sku__sku_code'))

        combo_sku_data = list(
            BOMMaster.objects.filter(
                (Q(product_sku__sku_code__in=parent_sku_codes) | Q(material_sku__sku_code__in=child_sku_codes)), **combo_filters
            ).values('product_sku__sku_code', 'material_sku__sku_code')
        )

        # Step 2: Extract all unique SKUs (parent and member)
        all_sku_codes = set(chain.from_iterable(
            [combo_sku['product_sku__sku_code'], combo_sku['material_sku__sku_code']]
            for combo_sku in combo_sku_data
        ))

        # Step 3: Fetch related SKURelation records for the SKUs collected so far
        final_related_combo_skus = list(
            BOMMaster.objects.filter(
                (Q(product_sku__sku_code__in=all_sku_codes) | Q(material_sku__sku_code__in=all_sku_codes)),
                **combo_filters
            ).values_list('product_sku__sku_code', flat=True)
        )

        combo_sku_quantity_dict = list(
            BOMMaster.objects.filter(
                Q(product_sku__sku_code__in = final_related_combo_skus),
                **combo_filters
            ).values('product_sku__sku_code', 'material_sku__sku_code', 'material_quantity')
        )

        #Step4: Fetch Related Childs and Parent Details for final combo skus

        for data in combo_sku_quantity_dict:
            parent_sku_code = data.get('product_sku__sku_code')
            combo_sku_code = data.get('material_sku__sku_code')
            self.combo_sku_code_list.add(combo_sku_code)
            self.parent_sku_code_list.add(parent_sku_code)
            quantity = data.get('material_quantity')
            self.parent_combo_sku_detail.setdefault(parent_sku_code, {})[combo_sku_code] = quantity
            self.child_parent_details.setdefault(combo_sku_code, []).append(parent_sku_code)


    def get_return_data(self):
        sku_data = {}
        for data in self.master_data:
            sku_id = data.get('id')
            sku_code = data.get('sku_code')
            is_batch_based = data.get('batch_based', 0) == 1

            if sku_code not in sku_data:
                # Initialize data once per SKU

                total_quantity = 0
                if (not is_batch_based and self.detail_view == 'batch') or self.detail_view == 'sku':
                    total_quantity = data.get('stockdetailquantity', 0) or 0
                
                reserved_quantity = self.reserved_dict.get(sku_id, 0)
                open_order_quantity = self.open_order_dict.get(sku_id, 0)
                asn_quantity = self.asn_details_dict.get(sku_id, 0)
                putaway_quantity = self.pending_putaway_details_dict.get(sku_id, 0)
                cycle_stock_quantity = getattr(self, 'total_cycle_stock_data', {}).get(sku_id, 0)
                staging_stock_quantity = getattr(self, 'staging_stock_data', {}).get(sku_id, 0)

                available_quantity = max(0, total_quantity
                                        - reserved_quantity
                                        - open_order_quantity
                                        - cycle_stock_quantity
                                        - staging_stock_quantity)

                sku_data[sku_code] = {
                    'sku_code': sku_code,
                    'sku_desc': data.get('sku_desc'),
                    'seller_id': data.get('sellerid'),
                    'seller_name': data.get('seller_name'),
                    'cost_price': data.get('cost_price', 0),
                    'batch_based': data.get('batch_based', 0),
                    'is_scannable': data.get('is_scannable', 0),
                    'serial_numbers': [],
                    'total_quantity': total_quantity,
                    'reserved_quantity': reserved_quantity,
                    'open_order_quantity': open_order_quantity,
                    'asn_quantity': asn_quantity,
                    'putaway_quantity': putaway_quantity,
                    'cycle_stock_quantity': cycle_stock_quantity,
                    'staging_stock_quantity': staging_stock_quantity,
                    'available_quantity': available_quantity,
                    'batch_details': {},
                    'sku_id': sku_id
                }

            # Handle batch data
            if self.detail_view == 'sku' or (not is_batch_based and self.detail_view == 'batch'):
                continue

            batch_id = data.get('batch_detail_id')
            batch_no = data.get('batch_no')
            location = data.get('location')
            location_id = data.get('location_id')
            quantity = data.get('stockdetailquantity', 0) or 0
            sku_info = sku_data[sku_code]
            sku_info['total_quantity'] += quantity

            # Recalculate available quantity after updating total_quantity
            sku_info['available_quantity'] = max(0, sku_info['total_quantity']
                                                - sku_info['reserved_quantity']
                                                - sku_info['open_order_quantity']
                                                - sku_info['cycle_stock_quantity']
                                                - sku_info['staging_stock_quantity'])

            batch_key, batch_key_str, asn_batch_key, serial_key = (sku_id,), '', '', (sku_code,)
            if self.detail_view in ['batch', 'location_batch']:
                batch_key += (batch_id,)
                batch_key_str = str(batch_no)
                asn_batch_key = (sku_id, batch_id)
                serial_key += (batch_no,)
            if self.detail_view == 'location_batch':
                batch_key += (location_id,)
                batch_key_str = str(batch_no) + '-' + str(location_id)
                serial_key += (location,)

            batch_reserved_qty = self.batch_level_reserved_dict.get(batch_key, 0)
            batch_putaway_qty = self.batch_level_pending_putaway_dict.get(batch_key, 0)
            batch_asn_qty = self.batch_level_asn_dict.get(asn_batch_key, 0)
            batch_cycle_qty = self.batch_level_cycle_stock_data.get(batch_key, 0) or 0
            batch_staging_qty = self.batch_level_staging_stock_dict.get(batch_key, 0) or 0

            serial_numbers = self.get_serial_df(sku_code, batch_no, location, self.detail_view) or []

            # Add serials to SKU level
            sku_info['serial_numbers'].extend(serial_numbers)

            return_batch_dict = {
                'batch': batch_no,
                'batch_reference': data.get('batch_reference', ''),
                'batch_display_key': batch_no or data.get('batch_reference'),
                'total_quantity': quantity,
                'mrp': data.get('batch_mrp', 0),
                'buy_price': data.get('buy_price', 0.0),
                'cost_price': data.get('cost_price', 0.0),
                'inspection_lot_no': data.get('inspection_lot_no', ''),
                'vendor_batch_no': data.get('vendor_batch_no', ''),
                'mfg_date': format_date(self.timezone, data.get('mfg_date')),
                'exp_date': format_date(self.timezone, data.get('exp_date')),
                'retest_date': format_date(self.timezone, data.get('retest_date')),
                'reevaluation_date': format_date(self.timezone, data.get('reevaluation_date')),
                'best_before_date': format_date(self.timezone, data.get('best_before_date')),
                'serial_numbers': serial_numbers,
                'reserved_quantity': batch_reserved_qty,
                'putaway_quantity': batch_putaway_qty,
                'cycle_stock_quantity': batch_cycle_qty,
                'staging_stock_quantity': batch_staging_qty,
                'available_quantity': max(0, quantity - batch_reserved_qty - batch_cycle_qty - batch_staging_qty)
            }
            if self.detail_view == 'location_batch':
                return_batch_dict['location'] = location
                return_batch_dict['zone'] = data.get('stock_zone')
            else:
                return_batch_dict['asn_quantity'] = batch_asn_qty

            if batch_key_str not in sku_info['batch_details']:
                sku_info['batch_details'][batch_key_str] = return_batch_dict

        # Final formatting
        for sku_code, sku_info in sku_data.items():
            sku_info['batch_details'] = list(sku_info['batch_details'].values())
            sku_info.pop('sku_id', None)
            self.data_list.append(sku_info)

    def get_serial_df(self, sku_code, batch_number, location, detail_view):
        """
        Retrieve serial numbers based on SKU, and conditionally by batch and location
        depending on the detail_view.

        Args:
            sku_code (str): SKU code to filter by.
            batch_number (str or None): Batch number to filter by (optional).
            location (str): Location to filter by when required.
            detail_view (str): 'batch', 'location', or 'location_batch'.

        Returns:
            list[str]: List of matching serial numbers.
        """
        serial_numbers = []

        for serial in self.serial_number_dict.get("data", []):
            if serial.get("sku_code") != sku_code:
                continue

            # If filter level includes 'batch' and batch_number is provided, apply batch filter
            if detail_view in ['batch', 'location_batch'] and batch_number:
                if serial.get("batch_number") != batch_number:
                    continue

            # If filter level includes 'location', apply location filter
            if detail_view in ['location_batch']:
                if serial.get("location_name") != location:
                    continue

            serial_numbers.append(serial.get("serial_number"))

        return serial_numbers


    def calculate_parent_quantity_details(self):
        if not (self.parent_sku_code_list and self.combo_sku_code_list and self.parent_combo_sku_detail):
            return

        #Step 2: Prepare Parents Open Order Quantity Details
        parent_open_order_details = {}
        for sku_code, open_order_quantity in self.open_order_details_dict.items():
            if sku_code in self.parent_sku_code_list:
                parent_open_order_details[sku_code] = open_order_quantity

        # Step 1: Prepare Child SKU's Stock Data
        combo_stock_details = {}
        for data in self.data_list:
            sku_code = data.get('sku_code')

            if sku_code in self.combo_sku_code_list:

                open_stock_of_parent = 0

                parent_sku_of_childs = self.child_parent_details.get(sku_code, [])
                for parent_sku in parent_sku_of_childs:
                    child_requirements = self.parent_combo_sku_detail.get(parent_sku, {})

                    if sku_code in self.parent_combo_sku_detail.get(parent_sku, []):
                        open_stock_qty_of_parent = parent_open_order_details.get(parent_sku, 0)
                        open_stock_qty_of_child = open_stock_qty_of_parent * child_requirements.get(sku_code, 0)
                        open_stock_of_parent += open_stock_qty_of_child

                data['open_order_quantity'] += open_stock_of_parent
                # Recalculate available quantity after updating open_order_quantity
                # Ensure all quantities are properly initialized
                total_quantity = data.get('total_quantity', 0) or 0
                reserved_quantity = data.get('reserved_quantity', 0) or 0
                open_order_quantity = data.get('open_order_quantity', 0) or 0
                cycle_stock_quantity = data.get('cycle_stock_quantity', 0) or 0
                staging_stock_quantity = data.get('staging_stock_quantity', 0) or 0

                data['available_quantity'] = max(0, total_quantity
                                              - open_order_quantity
                                              - reserved_quantity
                                              - cycle_stock_quantity
                                              - staging_stock_quantity)
                combo_stock_details[sku_code] = data

        #Main Loop to calculate the data
        for data in self.data_list:
            parent_sku_code = data.get('sku_code')
            child_requirements = self.parent_combo_sku_detail.get(parent_sku_code, {})
            if not child_requirements:
                continue

            # Calculate minimum sets based on child stock availability by excluding the open order quanties of parents
            min_possible_sets = float('inf')
            for child_sku_code, multiplier in child_requirements.items():
                available_stock = combo_stock_details.get(child_sku_code, {}).get('available_quantity', 0)

                if multiplier > 0:
                    possible_sets = available_stock // multiplier
                    min_possible_sets = min(min_possible_sets, possible_sets)

            data['available_quantity'] = min_possible_sets if min_possible_sets != float('inf') else 0

            # Ensure all quantities are properly initialized
            available_quantity = data.get('available_quantity', 0) or 0
            reserved_quantity = data.get('reserved_quantity', 0) or 0
            open_order_quantity = data.get('open_order_quantity', 0) or 0
            cycle_stock_quantity = data.get('cycle_stock_quantity', 0) or 0
            staging_stock_quantity = data.get('staging_stock_quantity', 0) or 0

            # Calculate total quantity as the sum of all components
            data['total_quantity'] = available_quantity + reserved_quantity + open_order_quantity + cycle_stock_quantity + staging_stock_quantity

    def get_inventory_details(self):
        self.get_open_asn_details()

        self.get_pending_putaway_details()

        self.master_data, self.updated_sku_codes, self.serial_number_dict = self.get_master_data()

        self.get_open_order_quantity_detials()

        self.get_reserved_quantity_details()

        self.get_return_data()

        self.calculate_parent_quantity_details()

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = self.request.GET
        self.timezone = get_user_time_zone(self.warehouse)
        self.data_list, self.errors = [], []
        self.limit, self.total_count = 10, 0

        #Search Params
        self.get_search_params()
        if self.request_data.get('limit'):
            self.limit = self.request_data['limit']
        self.callback = self.request_data.get('callback', 'false') or 'false'

        self.detail_view = self.request_data.get('detail_view', 'sku') or 'sku'

        #GET Inventory Details
        self.get_inventory_details()

        if self.callback == 'true':
            return self.data_list
        self.page_info['data'] = self.data_list
        self.page_info['message'] = "Success"
        self.page_info['status'] = 200
        self.page_info['page_info']['total_count'] = self.total_count
        self.page_info['error'] = self.errors
        return self.page_info

def get_current_stock_data(warehouse, filters):
    sku_codes = filters.get('sku_codes', [])
    sku_filters = {'user': warehouse.id, 'status': 1}
    if sku_codes:
        sku_filters['sku_code__in'] = sku_codes
    sku_codes = SKUMaster.objects.filter(**sku_filters).values_list("sku_code",flat=True)
    zone_filters, exclude_filters = {'user': warehouse.id}, {}
    if filters.get('zones'):
        zone_filters['zone__in'] = filters.get('zones')
    else:
        zone_filters['segregation'] = 'sellable'
        exclude_filters = {'zone': 'QC_ZONE'}
    zones=list(ZoneMaster.objects.filter(**zone_filters).exclude(**exclude_filters).values_list("zone",flat=True))
    request_data = {'zones': zones}
    total_inventory = []
    skus = [skus for skus in sku_codes.iterator(chunk_size=100)]
    while skus:
        inventory_data = get_inventory_callback(warehouse, skus[:101], request_data)
        total_inventory.append(inventory_data)
        skus = skus[101:]
    return total_inventory

def reduce_stocks(warehouse, stock_data):
    '''
    Reduce Stocks
    '''
    log.info(("Reducing Stocks from Staging Location for Username %s and Data %s") % (
        str(warehouse.username), str(stock_data)
    ))
    try:
        decimal_limit = get_decimal_value(warehouse.id)
        update_stocks = []
        for stocks in stock_data:

            stock_filters = {
                'sku__user': warehouse.id,
                'sku_id': stocks.get('sku_id'),
                'receipt_number__in': stocks.get('receipt_numbers'),
                'status': stocks.get('status')
            }
            if stocks.get('receipt_type'):
                stock_filters.update({
                    'receipt_type': stocks.get('receipt_type')
                })
            if stocks.get('location'):
                stock_filters.update({
                    'location__location': stocks.get('location')
                })
            if stocks.get('batch_detail_id'):
                stock_filters.update({
                    'batch_detail_id': stocks.get('batch_detail_id')
                })
            if stocks.get('location_id'):
                stock_filters.update({
                    'location_id': stocks.get('location_id')
                })
            if stocks.get('lpn_number'):
                stock_filters.update({
                    'lpn_number': stocks.get('lpn_number')
                })

            stock_objs = StockDetail.objects.filter(**stock_filters)

            move_quantity = stocks.get('quantity')
            for stock in stock_objs:
                #Stock Reduction
                if stock.quantity > move_quantity:
                    stock.quantity -= move_quantity
                    move_quantity = 0
                    update_stocks.append(stock)

                elif stock.quantity <= move_quantity:
                    move_quantity -= stock.quantity
                    stock.quantity = 0
                    update_stocks.append(stock)

                move_quantity = truncate_float(move_quantity, decimal_limit)
                if move_quantity == 0:
                    break

        StockDetail.objects.bulk_update_with_rounding(update_stocks, ['quantity'])

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(("Reducing Stocks from Staging Location Failed for Username %s Data %s, Exception %s") % (
            str(warehouse.username), str(stock_data), str(e)
        ))

def batch_attributes_bulk_validation(warehouse, data_list, uploads = False):
    """
    Perform batch attributes bulk validation.

    Args:
        warehouse: The warehouse object.
        data_list: The list of data to be validated.

    Returns:
        A tuple containing the status and the updated data list after validation.
    """
    configured_batch_attributes, configured_non_batch_attributes, batch_key_config = get_misc_details(warehouse)

    ### batch attributes validation
    data_list = validate_configured_batch_attributes_data(
        data_list, configured_batch_attributes, configured_non_batch_attributes
    )

    ###sku validation
    sku_codes_list, _ = get_sku_codes(data_list)
    sku_details_dict, _ = get_sku_master_dict(warehouse, sku_codes_list)
    data_list = validate_sku_data(data_list, sku_details_dict)

    ### sku batch validation
    data_list = validate_sku_batch_data(data_list, sku_codes_list, warehouse)

    ###dates validation
    data_list = validate_date_format(data_list, warehouse)

    ###duplicate_records_validation
    data_list = validate_duplicate_records(data_list)

    ### batch key validation
    available_batch_keys = get_batch_key_list(warehouse)
    data_list = validate_batch_key(warehouse, data_list, available_batch_keys, batch_key_config)

    #Return Data List with Errors incase of Uploads else return only Errors
    error_status, validated_list, error_list = format_error_list(data_list)
    if error_status and uploads:
        return error_status, data_list, validated_list

    ###batch and batch key creation
    final_error_status = create_batch_and_batch_key(validated_list, warehouse)

    error_list = drop_columns_for_error(error_list)
    return error_status or final_error_status, validated_list, error_list

def format_error_list(data_list):
    error_status, validated_list, error_list = False, [], []

    for data in data_list:
        if data.get('status'):
            error_list.append(data)
            error_status = True
        else:
            validated_list.append(data)

    return error_status, validated_list, error_list

def get_misc_details(warehouse):
    """
    Retrieve configuration details related to the warehouse.

    Args:
        warehouse: The warehouse object for which to retrieve the details.

    Returns:
        A tuple containing the configured batch attributes, configured non-batch attributes,
        and the enable batch key flag.

    """
    misc_types = ['additional_batch_attributes', 'non_mandatory_batch_attributes',
                  'enable_batch_key']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)

    configured_batch_attributes, configured_non_batch_attributes = [], []
    #Additional batch details
    additional_batch_details, non_mandatory_batch_attributes = (
        misc_dict.get('additional_batch_attributes'),
        misc_dict.get('non_mandatory_batch_attributes'),
    )
    enable_batch_key = misc_dict.get('enable_batch_key', 'false')

    if additional_batch_details not in ['false', '', None, False]:
        configured_batch_attributes = additional_batch_details.split(',')

    if non_mandatory_batch_attributes not in ['false', '', None, False]:
        configured_non_batch_attributes = non_mandatory_batch_attributes.split(',')

    return configured_batch_attributes, configured_non_batch_attributes, enable_batch_key


def validate_configured_batch_attributes_data(batch_detail_list, configured_batch_attributes, configured_non_batch_attributes):
    """
    Validates the configured batch attributes data.

    Args:
        warehouse (str): The name of the warehouse.
        batch_detail_list (list): A list of dictionaries containing batch details.
        configured_batch_attributes (list): A list of configured batch attributes.
        configured_non_batch_attributes (list): A list of configured non-batch attributes.

    Returns:
        tuple: A tuple containing the status (True if there are validation errors, False otherwise) and the DataFrame
               with the updated 'status' column.

    """
    # Convert the list of dictionaries to a DataFrame
    df = pd.DataFrame(batch_detail_list)

    # Replace empty strings, None, 'None', '0', and 0 with NaN
    df = df.replace(['', None, 'None'], pd.NA)

    # Add a 'status' column initialized with an empty string
    df['status'] = ''

    # Process batch attributes
    for batch_attribute in configured_batch_attributes:
        if batch_attribute == 'batch_number':
            batch_attribute = 'batch_no'
        if batch_attribute == 'vendor_batch_number':
            batch_attribute = 'vendor_batch_no'

        if batch_attribute in df.columns:
            if batch_attribute == 'mrp':
                df[batch_attribute] = df[batch_attribute].astype(float, errors='ignore')

            missing = df[batch_attribute].isna()
            if missing.any() and batch_attribute not in configured_non_batch_attributes:
                df.loc[missing, 'status'] += f'{batch_attribute.replace("_", " ").title()} is Mandatory. '

    # Define the list of batch attributes
    batch_attributes = [
        'batch_number', 'batch_reference', 'best_before_date', 'expiry_date', 'inspection_lot_number', 'mrp',
        'manufactured_date', 'reevaluation_date', 'retest_date', 'vendor_batch_number', 'weight'
    ]

    # Rename columns back for final validation
    df = df.rename(columns={'batch_no': 'batch_number', 'vendor_batch_no': 'vendor_batch_number'})

    # Validate non-mandatory attributes
    for col in df.columns:
        if col in batch_attributes and col not in configured_batch_attributes:
            has_value = df[col].notna()
            if has_value.any():
                df.loc[has_value, 'status'] += f'{col.replace("_", " ").title()} is a Non-Mandatory batch Attribute which cannot be updated. '

    ##replace NaN with ""
    df = df.fillna('')

    return df.to_dict('records')

def validate_sku_data(data_list, sku_details_dict):
    """
    Validates the SKU data by merging it with the SKU details dictionary.

    Args:
        data_list (list): A list of dictionaries containing SKU data.
        sku_details_dict (dict): A dictionary containing SKU details.

    Returns:
        tuple: A tuple containing a boolean status indicating if any validation errors occurred,
               and a list of dictionaries representing the merged and validated SKU data.
    """
    df = pd.DataFrame(data_list)

    # Convert skudict to DataFrame
    sku_df = pd.DataFrame.from_dict(sku_details_dict, orient='index').reset_index()
    sku_df.rename(columns={'index': 'sku_code'}, inplace=True)

    # Merge the DataFrames
    sku_df['sku_code'] = sku_df['sku_code'].astype(str)
    df['sku_code'] = df['sku_code'].astype(str)
    merged_df = pd.merge(df, sku_df, on='sku_code', how='left')

    # Ensure the 'status' column exists in the DataFrame
    if 'status' not in merged_df.columns:
        merged_df['status'] = ''

    # check if sku is presnt in the system
    if 'sku_id' not in merged_df.columns:
        merged_df['status'] += merged_df.apply(
            lambda row: f"SKU:- {row['sku_code']} not found in the system.", axis=1
        )
    else:
        missing_sku = merged_df['sku_id'].isna()
        if missing_sku.any():
            merged_df.loc[missing_sku, 'status'] += merged_df.apply(
                lambda row: f"SKU {row['sku_code']} not found in the system.", axis=1)

        merged_df.loc[merged_df['batch_based'] == 0, 'status'] += merged_df.apply(
            lambda row: f"Batch cannot be created for batch-disabled SKU {row['sku_code']}.", axis=1)

    return merged_df.to_dict('records')

def get_batch_key_list(warehouse):
    """
    Retrieves a list of batch keys for a given warehouse.

    Args:
        warehouse (str): The name of the warehouse.

    Returns:
        list: A list of batch keys associated with the given warehouse.
    """
    batch_keys = BatchKey.objects.filter(batch_details__warehouse=warehouse).values_list('batch_identifier', flat=True)
    return batch_keys

def validate_batch_key(warehouse, data_list, available_batch_keys, batch_key_config):
    """
    Validates the batch keys in the given data list based on the available batch keys and configuration.

    Args:
        data_list (list): A list of dictionaries representing the data to be validated.
        available_batch_keys (list): A list of available batch keys.
        batch_key_config (str): A string indicating the configuration status of batch keys.

    Returns:
        tuple: A tuple containing a boolean status indicating if the validation passed or not, and a list of dictionaries representing the validated data with status messages.

    """
    df = pd.DataFrame(data_list)

    stockone_code = warehouse.userprofile.stockone_code
    # Convert batch_key column to list of values
    df['batch_key'] = df['batch_key'].astype(str)
    df['batch_key'] = df['batch_key'].apply(lambda x: x.split(',') if x not in [None, ''] else [])

    # Initialize the status column
    if 'status' not in df.columns:
        df['status'] = ''

    # Check if batch_key_config is false
    if batch_key_config == 'false' and df['batch_key'].apply(len).sum() > 0:
        df['status'] += 'Batch Key Cannot be added, Batch Key is not configured for the warehouse.'

    # Check for duplicate batch keys within the DataFrame
    all_batch_keys = [key.strip() for sublist in df['batch_key'].tolist() for key in sublist]
    duplicate_keys = {key for key in all_batch_keys if all_batch_keys.count(key) > 1}

    if duplicate_keys:
        df['status'] = df['status'] + df['batch_key'].apply(
            lambda keys: f"Duplicate Batch Key(s) {', '.join([key for key in keys if key in duplicate_keys])} found." if any(key in duplicate_keys for key in keys) else '')

    # Check if any batch keys already exist in available_batch_keys
    if available_batch_keys:
        invalid_batch_keys = df['batch_key'].apply(lambda keys: any(key in available_batch_keys for key in keys))
        if invalid_batch_keys.any():
            df.loc[invalid_batch_keys, 'status'] = df.loc[invalid_batch_keys].apply(
                lambda row: row['status'] + f" Batch Key(s) {', '.join([key for key in row['batch_key'] if key in available_batch_keys])} already exists in the system.", axis=1)

    # Check if any batch key is given with prefix stockone_code
    df['invalid_prefix'] = df['batch_key'].apply(
        lambda keys: any(key.startswith(stockone_code) for key in keys)
    )
    if df['invalid_prefix'].any():
        df.loc[df['invalid_prefix'], 'status'] = df.loc[df['invalid_prefix']].apply(
            lambda row: row['status'] + f" Batch Key(s) {', '.join([key for key in row['batch_key'] if key.startswith(stockone_code)])} should not have prefix {stockone_code}.", axis=1)

    # Clean up the status messages
    df['status'] = df['status'].str.strip().str.rstrip('.')

    return df.to_dict('records')

def validate_duplicate_records(data_list):
    """
    Validates the given data list for duplicate records based on specific columns.

    Args:
        data_list (list): A list of dictionaries representing the data.

    Returns:
        tuple: A tuple containing a boolean value indicating the presence of duplicate records and a list of dictionaries
               representing the modified data with status messages.
    """

    df = pd.DataFrame(data_list)

    # Initialize the status column
    df['status'] = df['status'].astype(str).apply(lambda x: '' if x == 'None' else x)

    # Check for duplicates based on 'sku_code' and 'batch_number' if both columns have values
    non_empty_sku_batch = df['sku_code'].notna() & df['sku_code'].str.strip().astype(bool) & \
                          df['batch_number'].notna() & df['batch_number'].str.strip().astype(bool)
    duplicate_records_sku_batch = df[non_empty_sku_batch].duplicated(subset=['sku_code', 'batch_number'], keep=False)
    if duplicate_records_sku_batch.any():
        df.loc[duplicate_records_sku_batch.index, 'status'] += 'Duplicate Record: sku_code, batch_number; '

    # Check for duplicates based on 'sku_code' and 'batch_reference' if both columns have values
    non_empty_sku_reference = df['sku_code'].notna() & df['sku_code'].str.strip().astype(bool) & \
                              df['batch_reference'].notna() & df['batch_reference'].astype(bool)
    duplicate_records_sku_reference = df[non_empty_sku_reference].duplicated(subset=['sku_code', 'batch_reference'], keep=False)
    if duplicate_records_sku_reference.any():
        df.loc[duplicate_records_sku_reference.index, 'status'] += 'Duplicate Record: sku_code, batch_reference; '

    df['status'] = df['status'].str.rstrip('; ')

    return df.to_dict('records')

def validate_date_format(data_list, warehouse):
    """
    Validates the date format for each data row in the given data list.

    Args:
        data_list (list): A list of dictionaries containing data rows.
        warehouse (Warehouse): An instance of the Warehouse class.

    Returns:
        tuple: A tuple containing the status (bool) and a dictionary representation of the data list.

    Raises:
        None

    """
    user_timezone = warehouse.userprofile.timezone
    timezone = user_timezone if user_timezone else 'Asia/Calcutta'

    for data_row in data_list:
        manufactured_date, expiry_date, retest_date, reevaluation_date, best_before_date = (
        data_row.get('manufactured_date'),
        data_row.get('expiry_date'),
        data_row.get('retest_date'),
        data_row.get('reevaluation_date'),
        data_row.get('best_before_date')
        )
        status = []
        data_row, status = validate_dates(
        manufactured_date, expiry_date, retest_date, reevaluation_date, best_before_date,
        timezone, status, data_row)

        data_row['status'] = (data_row.get('status', '') + ', '.join(status)).strip(', ')

    df = pd.DataFrame(data_list)

    return df.to_dict('records')

def get_available_sku_batch(sku_codes_list, warehouse):
    batch_data = list(BatchDetail.objects.filter(warehouse = warehouse.id, sku__sku_code__in = sku_codes_list).values_list('sku__sku_code', 'batch_no'))
    return batch_data

def validate_sku_batch_data(data_list, sku_codes_list, warehouse):
    """
    Validates the SKU batch data against the available SKU batches in the warehouse.

    Args:
        data_list (list): A list of dictionaries representing the SKU batch data.
        sku_codes_list (list): A list of SKU codes to validate against.
        warehouse (str): The name of the warehouse to validate against.

    Returns:
        tuple: A tuple containing a boolean status indicating if any duplicates were found,
               and a list of dictionaries representing the validated SKU batch data.
    """
    # Convert the data list to a pandas DataFrame
    df = pd.DataFrame(data_list)

    # Get available SKU batches from the provided function
    available_sku_batch = get_available_sku_batch(sku_codes_list, warehouse)

    # Convert available SKU batches to a DataFrame
    validation_df = pd.DataFrame(available_sku_batch, columns=['sku_code', 'batch_number'])

    # Merge the input DataFrame with the validation DataFrame to find duplicates
    merged_df = df.merge(validation_df, on=['sku_code', 'batch_number'], how='left', indicator=True)

    # Ensure the 'status' column exists
    if 'status' not in df.columns:
        df['status'] = ''

    # Update the 'status' column for duplicates
    df.loc[merged_df['_merge'] == 'both', 'status'] += 'SKU batch combination already exists. Cannot Create.'

    return df.to_dict('records')


def create_batch_and_batch_key(data_list, warehouse):
    """
    Create BatchDetail objects and BatchKey objects based on the provided data.

    Args:
        data_list (list): A list of dictionaries containing the batch data.
        warehouse (Warehouse): The warehouse object associated with the batches.

    Returns:
        str: A string indicating the result of the batch creation process. Possible values are "Success" or "Creation Failed".
    """
    try:
        new_batch_data = []
        batch_keys_to_create = []
        sku_batch_dict = {}

        date_fields = [
            'manufactured_date', 'expiry_date', 'retest_date',
            'reevaluation_date', 'best_before_date'
        ]

        # Create BatchDetail objects and prepare data for BatchKey creation
        for batch_data in data_list:
            new_batch_detail = {
                'sku_id': batch_data.get('sku_id'),
                'mrp': batch_data.get('mrp') if batch_data.get('mrp') else 0,
                'warehouse_id': warehouse.id,
                'account_id': warehouse.userprofile.id,
                'batch_no' : batch_data.get('batch_number', ''),
                'batch_reference': batch_data.get('batch_reference', ''),
                'inspection_lot_number': batch_data.get('inspection_lot_number', ''),
                'vendor_batch_no': batch_data.get('vendor_batch_number', ''),
                'weight': batch_data.get('weight') if batch_data.get('weight') else '',
            }

            for field in date_fields:
                if batch_data.get(field):
                    new_batch_detail[field] = batch_data[field].strftime(date_time_format)

            # Create the BatchDetail object and add it to the list
            batch_detail = BatchDetail(**new_batch_detail)
            new_batch_data.append(batch_detail)

            # Prepare BatchKey objects and map SKU batch information to batch keys
            sku_batch_key = (batch_data.get('sku_code'), batch_data.get('batch_number'))
            sku_batch_dict[sku_batch_key] = batch_data.get('batch_key', [])
            for batch_key in batch_data.get('batch_key', []):
                batch_keys_to_create.append(
                    BatchKey(
                        batch_identifier=batch_key,
                        account_id = warehouse.userprofile.id,
                        json_data = {"created_by": warehouse.username}
                    )
                )

        # Bulk create the BatchDetail objects
        created_batch_details = BatchDetail.objects.bulk_create_with_rounding(new_batch_data)

        # Bulk create the BatchKey objects
        created_batch_keys = BatchKey.objects.bulk_create(batch_keys_to_create)

        # Create a mapping from batch identifier to BatchKey object
        batch_key_map = {bk.batch_identifier: bk for bk in created_batch_keys}

        # Associate each BatchDetail with its corresponding BatchKey(s)
        for created_batch_detail in created_batch_details:
            sku_batch_key = (created_batch_detail.sku.sku_code, created_batch_detail.batch_no)
            if sku_batch_key in sku_batch_dict:
                batch_keys = sku_batch_dict[sku_batch_key]
                for batch_key in batch_keys:
                    if batch_key in batch_key_map:
                        created_batch_detail.batch_identifier.add(batch_key_map[batch_key])
        return False

    except Exception as e:
        log.info(("Batch Creation Failed for user %s with data %s: %s") % (warehouse.username, data_list, e))
        return True

def drop_columns_for_error(data_list):
    """
    Drops specific columns from a DataFrame if they exist in the DataFrame's columns.

    Args:
        df (pandas.DataFrame): The DataFrame from which columns need to be dropped.

    Returns:
        pandas.DataFrame: The DataFrame with specified columns dropped, if they exist.
    """
    df = pd.DataFrame(data_list)
    columns_to_drop = ['sku_id', 'batch_based', 'invalid_prefix']
    columns_to_drop = [col for col in columns_to_drop if col in df.columns]
    df = df.drop(columns=columns_to_drop)
    return df.to_dict('records')

@get_warehouse
def get_user_incremental_batch_no(request, warehouse:User):
    """
    Generate an incremental batch number for the user associated with the warehouse.
    """
    batch_no = ''

    # Fetch the batch prefix config values for the given warehouse ID
    batch_prefix = get_misc_value('incremental_batch_prefix', warehouse.id)

    # Check if the batch prefix is valid (not 'false' or 'null')
    if batch_prefix not in ['false', 'null']:
        # Generate an incremental batch number with a specified zero count (padding) of 8
        batch_no = get_incremental_batch_no(warehouse, batch_prefix, length=8)

    # Return the batch number as a JSON response with a status code of 200
    return JsonResponse({'batch_no': batch_no}, status=200)

def get_restricted_stock_status(warehouse):
    status_list = [2]
    restricted_stock_status = get_misc_value("restricted_adjustment_status", warehouse.id).split(',')
    for status in restricted_stock_status:
        if status in reverse_stock_choice_mapping:
            status_list.append(reverse_stock_choice_mapping.get(status))
    return status_list

def get_available_stock_details(warehouse, sku_ids=None, location_ids=None):
    '''
    Get Available Stock Details
    '''
    stock_filters = {'quantity__gt': 0, 'sku__user': warehouse.id}
    if sku_ids:
        stock_filters['sku_id__in'] = sku_ids
    if location_ids:
        stock_filters['location_id__in'] = location_ids

    restricted_stock_status = get_restricted_stock_status(warehouse)

    exclude_dict = {}
    if restricted_stock_status:
        exclude_dict['status__in'] = restricted_stock_status

    #Current Stocks
    values_list = ['sku__sku_code', 'location__zone__zone', 'location__location', 'location_id', 'status', 'batch_detail_id', 'sku_id', 'lpn_number']

    stock_details = StockDetail.objects.select_related('location', 'location__zone', 'sku', 'batch_detail').exclude(**exclude_dict).exclude(
        location__zone__segregation__in=['inbound_staging', 'outbound_staging']
    ).exclude(
        location__zone__storage_type__in =  ['wip_area', 'replenishment_staging_area', 'nte_staging_area']
    ).filter(**stock_filters).values(*values_list).annotate(stock_quantity=Sum('quantity'))

    return [
        {
            'sku_code': stock['sku__sku_code'],
            'location': stock['location__location'],
            'quantity': stock['stock_quantity'],
            'sku_id': stock['sku_id'],
            'location_id': stock['location_id'],
            'stock_status': stock['status'],
            'batch_detail_id': stock['batch_detail_id'],
            'lpn_number' : stock['lpn_number']
        }
        for stock in stock_details
    ]

def get_search_params(request_data):
    search_params = {'status': 1}
    if request_data.get('sku_code'):
        search_params.update({'sku__sku_code': request_data['sku_code']})
    header_level = True if request_data.get('header_level', '') == 'true' else False
    return search_params, header_level

def get_stock_objects(warehouse_id, search_params):
    '''
    Get Stocks
    '''
    stock_detail = StockDetail.objects.exclude(
        location__zone__storage_type__in = ['replenishment_staging_area', 'nte_staging_area']
    ).filter(sku__user=warehouse_id, quantity__gt=0).filter(**search_params)
    return stock_detail

def get_available_stock_df(stock_detail):
    stock_df = pd.DataFrame(
        stock_detail.values(
            sku_detail_id = F('sku_id'),
            sku_code = F('sku__sku_code')
        ).annotate(available_quantity = Sum('quantity'))
    )
    return stock_df

def get_reserved_stock_df(stock_df, warehouse_id):
    '''
    Calculate the reserved quantity for available stock
    '''
    if stock_df.empty:
        return stock_df

    reserved_filter = {
        'sku_id__in': stock_df['sku_detail_id'].unique(),
        'status__in': ['open', 'Allocated'],
        'user': warehouse_id,
        'reserved_quantity__gt': 0
    }
    picklist_obj = Picklist.objects.filter(**reserved_filter).values(
            sku_detail_id = F('stock__sku_id')
        ).annotate(reserved_qty=Sum('reserved_quantity'))

    reserved_df = pd.DataFrame(picklist_obj)

    if not reserved_df.empty:

        stock_df = pd.merge(
            stock_df, reserved_df, on=['sku_detail_id'], how='left'
        )
        # Apply fillna(0) only to specific columns
        columns_to_fill = ['reserved_qty']  # Replace with your actual columns
        stock_df[columns_to_fill] = stock_df[columns_to_fill].fillna(0)
    else:
        stock_df['reserved_qty'] = 0

    return stock_df

def get_available_stocks(warehouse_id, stock_detail, frame_df=None):
    decimal_limit = get_decimal_value(warehouse_id)

    stock_df = get_available_stock_df(stock_detail)

    stock_df = get_reserved_stock_df(stock_df, warehouse_id)

    if not stock_df.empty:
        stock_df['available_quantity'] = stock_df['available_quantity'].fillna(0)
        if 'reserved_qty' not in stock_df.columns:
            stock_df['reserved_qty'] = 0

        stock_df['reserved_qty'] = stock_df['reserved_qty'].fillna(0)
        for index, stock_record in stock_df.iterrows():
            stock_df.loc[index, 'quantity'] = float(
                Decimal(str(stock_record['available_quantity'])) - Decimal(str(stock_record['reserved_qty']))
            )

    if frame_df:
        return stock_df, decimal_limit

    if not stock_df.empty:
        stock_df = stock_df.to_dict(orient='records')

    return stock_df, decimal_limit

@get_warehouse
def get_inventory(request, warehouse:User):
    try:
        request_data = request.GET
    except TypeError:
        return JsonResponse({'message': 'Please send proper data'}, safe=False)

    warehouse_id = warehouse.id

    search_params, header_level = get_search_params(request_data)

    if header_level and search_params.get('sku__sku_code'):
        #Available Stocks
        stock_detail = get_stock_objects(warehouse_id, search_params)

        stock_df, decimal_limit = get_available_stocks(warehouse_id, stock_detail)

        return_data = []
        for data in stock_df:
            if data['available_quantity'] <= 0:
                continue
            return_data.append(data)
        return JsonResponse({'data': return_data}, status = 200)

    return JsonResponse({'message': 'Please Send Proper Data'}, status = 400)



class InventorySyncView(WMSListView):
    '''
    Inventory Sync View for bulk create or upload of stock details
    '''
    def post(self, *args, **kwargs):
        from core_operations.views.integration.integration import webhook_integration_3p
        '''Create/Updates Stocks'''
        self.set_user_credientials()
        self.request_data_dict = loads(self.request.body)

        # Check if the request data is a dict and contains a valid 'data_list'
        if not (isinstance(self.request_data_dict, dict) and 'data_list' in self.request_data_dict and isinstance(self.request_data_dict['data_list'], list) and len(self.request_data_dict['data_list'])):
            return JsonResponse({'message': "Invalid Payload: 'data_list' must be a non-empty list"}, status=400)
        try:
            self.request_data = self.request_data_dict['data_list']
            self.inventory_data_list = []
            self.error_data_list = []
            #Validate the input data
            self.validate_data()

            if not self.inventory_data_list and self.error_data_list:
                return JsonResponse({'message': self.error_data_list}, status = 400)

            #Validate Batch Details
            self.process_stock_records()

            #Inventory Callback
            filters = {'sku_codes': self.sku_codes, 'zones_data': self.sku_zones_dict}
            webhook_integration_3p(self.warehouse.id, "inventory_adjustment", filters)

            message, status = 'Batch Detail Updated Successfully', 200
            if self.inventory_data_list and self.error_data_list:
                status, message = 207, self.error_data_list
            return JsonResponse({'message' : message}, status=status)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            message = 'Stock Detail Sync failed for %s, exception %s. Data: %s'%(
                self.user.username,str(e), self.request_data_dict
            )
            log.info(message)
            return JsonResponse({'message': 'Stock Creation/Updation Failed', 'error': str(e)}, status=400)

    def validate_data(self):
        """
        Validate the input data for correct SKU and location, and check for duplicates.
        """
        # Step 1: Convert input list to DataFrame for easier manipulation
        df = pd.DataFrame(self.request_data)

        # Step 2: Fetch SKUs and Locations from the database based on the provided sku_codes and locations
        sku_codes = df['sku_code'].unique()
        locations = df['location'].unique()

        # Fetch SKUs and Locations from the master tables
        sku_master_filters = {
            'user': self.warehouse.id,
            'sku_code__in': sku_codes,
            'batch_based': 0
        }
        self.skus_dict = dict(SKUMaster.objects.filter(**sku_master_filters).values_list('sku_code', 'id'))
        location_master_filters = {
            'zone__user': self.warehouse.id,
            'location__in': locations
        }

        self.locations_dict, self.zones_dict = {}, {}
        location_objs = list(
            LocationMaster.objects.filter(**location_master_filters).select_related('zone').values('location', 'id', 'zone_id')
        )
        for obj in location_objs:
            self.locations_dict[obj['location']] = obj['id']
            self.zones_dict[obj['id']] = obj['zone_id']

        # Step 5: Check for duplicate SKU-location pairs in the input data
        duplicates = df.duplicated(subset=['sku_code', 'location'], keep=False)  # Mark all duplicates

        # Step 6: Validate each row in the DataFrame and collect errors
        for _, row in df.iterrows():
            record_errors = []

            sku_code = row.get('sku_code', '').strip()
            location = row.get('location', '').strip()
            quantity = row.get('quantity', 0)

            # Validate if sku_code, location, and quantity are not empty
            if pd.isnull(sku_code) or pd.isnull(location) or pd.isnull(quantity):
                record_errors.append("SKU Code, Location, Quantity are mandatory fields")
                continue

            record_errors = self.validate_quantity(quantity, record_errors)
            # Check for invalid SKU
            if not self.skus_dict.get(sku_code):
                record_errors.append("Invalid SKU Code")

            # Check for invalid location
            if not self.locations_dict.get(location):
                record_errors.append("Invalid Location")

            # Check for duplicate SKU-location pair
            if duplicates.loc[_]:
                record_errors.append("Duplicate SKU-Location pair")

            # If there are errors for this record, append them to the errors list in the desired format
            if len(record_errors):
                row['Status'] = record_errors
                self.error_data_list.append(row.to_dict())
            else:
                row['sku_id'] = self.skus_dict.get(sku_code)
                row['location_id'] = self.locations_dict.get(location)
                row['quantity'] = float(quantity)
                self.inventory_data_list.append(row)

    def process_stock_records(self):
        """
        Process the valid records by updating quantities or creating new records if they don't exist.
        """
        sku_ids, location_ids = set(), set()

        # Step 1: Collect SKU and Location IDs
        for record in self.inventory_data_list:
            sku_ids.add(record.get('sku_id'))
            location_ids.add(record.get('location_id'))

        # Fetch existing stock details
        stock_objs = StockDetail.objects.filter(
                sku__user=self.warehouse.id,
                sku_id__in=sku_ids,
                location_id__in=location_ids
            ).select_related('sku', 'location')

        # Prepare dictionaries
        stock_objs_dict, aggregated_quantity_dict = {}, {}
        # step 2: Create a dictionary of stock objects with a unique key (sku_id, location_id)
        for stock_obj in stock_objs:
            key = (stock_obj.sku_id, stock_obj.location_id)
            stock_objs_dict.setdefault(key, []).append(stock_obj)
            aggregated_quantity_dict[key] = aggregated_quantity_dict.get(key, 0) + stock_obj.quantity

        bulk_update_objs, bulk_create_objs = [], []
        self.sku_codes, self.sku_zones_dict = [], defaultdict(list)
        for req_data in self.inventory_data_list:
            sku_id, location_id, quantity = req_data.get('sku_id'), req_data.get('location_id'), req_data.get('quantity')
            stock_unique_key = (sku_id, location_id)

            #Details for a callback
            sku_code = req_data.get('sku_code')
            zone_id = self.zones_dict.get(location_id)
            self.sku_codes.append(sku_code)
            self.sku_zones_dict[zone_id].append(sku_code)

            # Handle existing stock
            stock_objs = stock_objs_dict.get(stock_unique_key)
            difference = quantity - aggregated_quantity_dict.get(stock_unique_key, 0)

            if difference == 0:
                continue

            if stock_objs:
                difference = self.adjust_stock_quantity(stock_objs, difference)
                bulk_update_objs.extend(stock_objs)

            # Handle new stock creation
            if not stock_objs or difference > 0:
                stock_obj = StockDetail(
                    sku_id=sku_id,
                    location_id=location_id,
                    quantity=difference,
                    original_quantity=difference,
                    receipt_date=datetime.datetime.now(),
                    receipt_type=req_data.get('reason') or 'inventory_sync',
                    account_id=self.warehouse.userprofile.id,
                )
                bulk_create_objs.append(stock_obj)

        # Perform bulk operations
        if bulk_update_objs:
            StockDetail.objects.bulk_update_with_rounding(bulk_update_objs, ["quantity"], batch_size=100)

        if bulk_create_objs:
            #Incremental Receipt Number for Stocks
            receipt_number = get_incremental(self.warehouse, 'receipt_number', '', False, len(bulk_create_objs))
            for stock in bulk_create_objs:
                stock.receipt_number = receipt_number
                receipt_number += 1

            StockDetail.objects.bulk_create_with_rounding(bulk_create_objs, batch_size=100)

    def adjust_stock_quantity(self, stock_obj_list, difference):
        """Adjust stock quantities based on difference."""
        for stock_obj in stock_obj_list:
            if difference > 0:
                stock_obj.quantity += difference
                return 0  # Difference handled
            elif difference < 0:
                if stock_obj.quantity + difference >= 0:
                    stock_obj.quantity += difference
                    return 0  # Difference handled
                difference += stock_obj.quantity
                stock_obj.quantity = 0
        return difference

    def validate_quantity(self, quantity, record_errors):
        try:
            if str(quantity):
                quantity = float(quantity)
                if quantity < 0:
                    record_errors.append('Invalid Quantity')
            else:
                record_errors.append('Physical Quantity Should not be empty')
        except ValueError:
            record_errors.append('Invalid Quantity')
        return record_errors


@get_warehouse
def get_stocks(request, warehouse:User):
    '''
    Get Stocks
    '''
    try:
        # Parse request data
        request_data = parse_request_data(request)

        sku_code = request_data.get('sku_code')
        start_index = request_data.get('start_index', 0)
        stop_index = request_data.get('stop_index', 10)
        column_filters= json.loads(request_data.get('column_filters', {}))
        status = request_data.get('status', 'Available')
        get_location = request_data.get('get_location', False)
        get_batch = request_data.get('get_batch', False)
        order_data = 'sku__sku_code'

        filter_dict = {
            'sku_code': 'sku__sku_code', 'zone_segregation': 'location__zone__segregation'
        }
        column_filters_dict = {}
        for filter_column, value  in column_filters.items():
            if filter_column in filter_dict:
                column_filters_dict[filter_dict[filter_column]] = value

        if not sku_code or not column_filters_dict.get('sku__sku_code'):
            return JsonResponse({'message': 'SKU Code is mandatory'}, status=400)

        #Get SKU Codes
        final_data = get_stock_details_data(
            warehouse, order_data, sku_code, status, column_filters_dict, Q(),
            get_location, get_batch, {}, int(start_index), int(stop_index)
        )
        return JsonResponse({'data': final_data}, status=200)

    except Exception as e:
        log.info(("Get Stocks Failed for user %s with Exception %s") % (warehouse.username, str(e)))
        return JsonResponse({'message': 'Get Stocks Failed', 'error': str(e)}, status=400)


def parse_request_data(request):
    """
    Parse JSON body or fallback to GET parameters.
    """
    try:
        return json.loads(request.body)
    except (json.JSONDecodeError, AttributeError):
        return request.GET
