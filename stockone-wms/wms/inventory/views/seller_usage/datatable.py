import json
from collections import OrderedDict
from django.db.models import Sum, F, Q
from django.contrib.auth.models import User
from inventory.models.locator import CostDimensions, CostDimensionMaster
from inbound.models.supplier import SupplierMaster
from core_operations.views.common.main import frame_datatable_column_filter, get_user_time_zone, get_local_date_known_timezone


from wms_base.wms_utils import init_logger
log = init_logger('logs/seller_usage_datatable.log')

def transform_filters(filters):
    mapping = {
        "from_cost_date__date": "cost_date__date__gte",
        "to_cost_date__date": "cost_date__date__lte",
    }

    updated_filters = {}
    for key, value in filters.items():
        new_key = mapping.get(key, key)
        updated_filters[new_key] = value

    return updated_filters


def get_seller_usage_master(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse: User, filters):
    """ Get Seller Usage Data """

    #search params list
    request_data = request.GET
    if not request_data:
        request_data = json.loads(request.body)
    order_data = ""
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-cost_date"
    sort_type = request_data.get('sort_type', 0)
    order_data_dict = {"cost_date": "cost_date"}
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column
    master_data = []
    if sort_type == '1':
        order_data = '-%s' % order_data

    company = warehouse.userprofile.company
    search_params = {"warehouse_id": warehouse.id}

    if column_headers:
        column_filters_dict = {}
        column_filters = frame_datatable_column_filter(column_headers, date_filters=["from_cost_date", "to_cost_date", "cost_date"])
        customer_filter_dict = {}
        for key, value in column_filters.items():
            if key in customer_filter_dict:
                column_filters_dict[customer_filter_dict.get(key)] = value
            else:
                column_filters_dict[key] = value

        column_filters_dict = transform_filters(column_filters_dict)
        master_data = CostDimensions.objects.using("datahub").filter(**column_filters_dict, **search_params)
    else:
        master_data = CostDimensions.objects.using("datahub").filter(**search_params)

    
    #Calculate Cards 
    total_quantity_by_storage_type = master_data.values('cost_type').annotate(
        total_quantity=Sum('cost_quantity'),
        total_amount=Sum('cost_amount')
    )
    storage_summary = {}
    for item in total_quantity_by_storage_type:
        storage_summary[item['cost_type']] = {
            "total_quantity": item['total_quantity'],
            "total_amount": item['total_amount']
        }

    #pagination records
    temp_data['recordsTotal'] = len(master_data)
    temp_data['recordsFiltered'] = len(master_data)
    timezone = get_user_time_zone(warehouse)

    master_data = master_data.order_by(order_data)

    #user master data preparation
    for data in master_data[start_index: stop_index]:
        creation_date, updation_date  = '',''

        try:
            cost_date = get_local_date_known_timezone(timezone, data.cost_date, send_date=True) if data.cost_date else ''
        except:
            pass
        data_dict = OrderedDict((
                ('seller_name' , data.seller_name),
                ('sku_code', data.sku_code),
                ('cost_type', data.get_cost_type_display()),
                ('cost_reference', data.cost_reference),
                ('cost_quantity', data.cost_quantity),
                ('uom', data.uom),
                ('unit_cost', data.unit_cost),
                ('conversion_factor', data.conversion_factor),
                ('cost_amount', data.cost_amount),
                ("cost_category", data.cost_category),
                ('cost_date', cost_date.strftime('%Y-%m-%d') if cost_date else ''),
                ('id', data.id),
        ))
        temp_data['aaData'].append(data_dict)


    temp_data['card_details'] = [
        {"data": {"quantity": storage_summary.get("Storage", {}).get("total_quantity", 0), "cost" : storage_summary.get("Storage", {}).get("total_amount", 0)}, "type": 'storage_summary', "title": 'Storage', "icon": "fi fi-rr-home"},
        {"data": {"quantity": storage_summary.get("SaleOrders", {}).get("total_quantity", 0), "cost" : storage_summary.get("SaleOrders", {}).get("total_amount", 0)}, "type": 'orders_summary', "title": 'Orders', "icon": "fi fi-rr-shopping-bag"},
        {"data": {"quantity": storage_summary.get("RTV", {}).get("total_quantity", 0), "cost" : storage_summary.get("RTV", {}).get("total_amount", 0)}, "type": 'rtv_summary', "title": 'RTV', "icon": "fi fi-rr-chevron-double-down"},
        {"data": {"quantity": storage_summary.get("GRN", {}).get("total_quantity", 0), "cost" : storage_summary.get("GRN", {}).get("total_amount", 0)}, "type": 'grn_summary', "title": 'GRN', "icon": "fi fi-rr-document"}
    ]


def get_seller_cost_master(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse: User, filters):
    """ Get Seller Cost Master """

    #search params list
    request_data = request.GET
    if not request_data:
        request_data = json.loads(request.body)
    order_data = ""
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)
    order_data_dict = {"cost_date": "cost_date"}
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column
    master_data = []
    if sort_type == '1':
        order_data = '-%s' % order_data

    company = warehouse.userprofile.company
    search_params = {"warehouse_id": warehouse.id}

    if column_headers:
        column_filters_dict = {}
        column_filters = frame_datatable_column_filter(column_headers, date_filters=["creation_date", "updation_date"])
        customer_filter_dict = {}
        for key, value in column_filters.items():
            if key in customer_filter_dict:
                column_filters_dict[customer_filter_dict.get(key)] = value
            else:
                column_filters_dict[key] = value
        master_data = CostDimensionMaster.objects.filter(**column_filters_dict, **search_params).order_by(order_data)
    else:
        master_data = CostDimensionMaster.objects.filter(**search_params).order_by(order_data)




    #prepare the dict for supplier names and id mapping
    supplier_list = list(CostDimensionMaster.objects.filter(warehouse_id=warehouse.id).exclude(seller_id__isnull=True).distinct().values_list('seller_id', flat=True))
    supplier_values = SupplierMaster.objects.filter(user=warehouse.id, id__in=supplier_list).values('id', 'name')

    supplier_name_dict = {}
    for item in supplier_values:
        supplier_name_dict[item['id']] = item['name']

    #pagination records
    temp_data['recordsTotal'] = len(master_data)
    temp_data['recordsFiltered'] = len(master_data)
    timezone = get_user_time_zone(warehouse)

    #user master data preparation
    for data in master_data[start_index: stop_index]:
        creation_date, updation_date  = '',''

        try:
            creation_date = get_local_date_known_timezone(timezone, data.creation_date) if data.creation_date else ''
            updation_date = get_local_date_known_timezone(timezone, data.updation_date) if data.updation_date else ''
        except:
            pass
        data_dict = OrderedDict((
                    ('seller_id', supplier_name_dict.get(data.seller_id, '')),
                    ('sku_cost_category' , data.sku_cost_category),
                    ("storage_cost_unit", data.storage_cost_unit),
                    ('storage_cost', data.storage_cost),
                    ('grn_cost', data.grn_cost),
                    ('sale_orders_cost', data.sale_orders_cost),
                    ('rtv_cost', data.rtv_cost),
                    ('creation_date', creation_date),
                    ('updation_date', updation_date),
                    ('id', data.id),
                    ('status', data.get_status_display())
               ))
        temp_data['aaData'].append(data_dict)
