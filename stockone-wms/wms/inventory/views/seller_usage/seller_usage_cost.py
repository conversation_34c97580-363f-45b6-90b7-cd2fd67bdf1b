from rest_framework.views import APIView
from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from inventory.serializers.locator import CostDimensionMasterSerializer
from inventory.models.locator import CostDimensionMaster
from inbound.models.supplier import SupplierMaster
from django.db.models import Q

class CostDimensionMasterPagination(PageNumberPagination):
    page_size_query_param = "page_size"

class CostDimensionMasterListCreateView(generics.ListCreateAPIView):
    serializer_class = CostDimensionMasterSerializer

    def get_queryset(self):
        # Filter queryset to only show records for the current user's warehouse
        return CostDimensionMaster.objects.filter(warehouse=self.request.warehouse)

    def create(self, request, *args, **kwargs):
        data = request.data
        if isinstance(data, list):
            serializer = self.get_serializer(data=data, many=True)
        else:
            serializer = self.get_serializer(data=data)

        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response({"message": "Created Successfully"}, status=status.HTTP_201_CREATED, headers=headers)

    def perform_create(self, serializer):
        # Set warehouse from request.user
        serializer.save(warehouse=self.request.warehouse)

class CostDimensionMasterRetrieveUpdateDeleteView(generics.RetrieveUpdateDestroyAPIView):
    queryset = CostDimensionMaster.objects.all()
    serializer_class = CostDimensionMasterSerializer

    def get_queryset(self):
        # Filter queryset to only show records for the current user's warehouse
        return CostDimensionMaster.objects.filter(warehouse=self.request.warehouse)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response({"message": "Updated Successfully"}, status=status.HTTP_200_OK)

    def perform_update(self, serializer):
        # Set warehouse from request.user
        serializer.save(warehouse=self.request.warehouse)

class UniqueSkuCostCategoryView(APIView):
    def get(self, request):
        warehouse = request.warehouse
        seller_identifier = request.query_params.get('seller_identifier', None)

        # Base queryset filtered by current user's warehouse
        queryset = CostDimensionMaster.objects.filter(warehouse_id=warehouse.id)

        if seller_identifier:
            try:
                supplier = SupplierMaster.objects.get(supplier_id=seller_identifier)
                # Filter by specific supplier and include null supplier records
                queryset = queryset.filter(Q(seller=supplier) | Q(seller__isnull=True))
            except SupplierMaster.DoesNotExist:
                return Response({"error": f"Seller with seller_identifier {seller_identifier} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            # Only include records with no supplier
            queryset = queryset.filter(seller__isnull=True)

        # Get unique SKU cost categories
        unique_sku_cost_categories = list(queryset.values_list('sku_cost_category', flat=True).distinct())
        unique_sku_cost_categories.insert(0, "default")
        return Response({"sku_cost_categories": unique_sku_cost_categories}, status=status.HTTP_200_OK)