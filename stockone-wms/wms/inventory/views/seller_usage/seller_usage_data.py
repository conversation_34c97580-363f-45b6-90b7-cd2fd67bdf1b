from rest_framework import viewsets
from django.db.models import Sum
from rest_framework.response import Response
from rest_framework.decorators import action
from inventory.models.locator import CostDimensions, CostDimensionMaster
from collections import defaultdict

#auth imports
from auth.utils import get_company_active_warehouses


#serializers
from inventory.serializers.locator import (
    CostDimensionsSerializer, AggregatedCostDimensionsSerializer, StandardResultsSetPagination
)
from datetime import datetime, timedelta

# ViewSet
class CostDimensionsViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = CostDimensionsSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        sku_code = self.request.query_params.get('sku_code')
        seller_id = self.request.query_params.get('seller_id')
        warehouse_name = self.request.query_params.get('warehouse')
        from_date = self.request.query_params.get('from_date', datetime.now() - timedelta(days=30))
        to_date = self.request.query_params.get('to_date', datetime.now())

        # request data
        warehouse = self.request.warehouse
        company = warehouse.userprofile.company
        company_warehouses = get_company_active_warehouses(company)
        queryset = CostDimensions.objects.using("datahub").filter(warehouse_id__in=company_warehouses)

        if sku_code:
            queryset = queryset.filter(sku_code=sku_code)
        if seller_id:
            queryset = queryset.filter(seller_name=seller_id)
        if warehouse_name:
            queryset = queryset.filter(warehouse_name=warehouse_name)
        if from_date:
            queryset = queryset.filter(cost_date__gte=from_date)
        if to_date:
            queryset = queryset.filter(cost_date__lte=to_date)

        return queryset

    @action(detail=False, methods=['get'])
    def aggregated(self, request):
        queryset = self.get_queryset()

        # Step 1: Aggregate all cost types for pagination
        aggregated_queryset = (
            queryset
            .values('sku_code', 'seller_name', 'warehouse_name', 'cost_type', 'cost_date__date')
            .annotate(total_quantity=Sum('cost_quantity'))
            .annotate(total_amount=Sum('cost_amount'))
        )

        page = self.paginate_queryset(aggregated_queryset)
        if page is None:
            return Response([])  # No data to process

        serializer = AggregatedCostDimensionsSerializer(page, many=True)
        return self.get_paginated_response(serializer.data)