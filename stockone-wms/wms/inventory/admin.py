from django.contrib import admin

# wms_base imports
from wms_base.models import User

# core imports
from core.admin import RestrictPermission

# inventory imports
from inventory.models import (ReplenishmentClassification, ReplenishmentMaster)

# Register your models here.


@admin.register(ReplenishmentClassification)
class ReplenishmentClassificationAdmin(RestrictPermission):
    ''' Customising ReplenishmentClassification '''

    list_display = (
        'id', 'sku_code', 'warehouse', 'classification',
        'avg_sales_day', 'min_stock_qty', 'max_stock_qty',
        'sku_avail_qty', 'replenishment_qty', 'sku_pen_po_qty', 'sku_pen_putaway_qty'
    )
    search_fields = ['sku__sku_code', 'classification']
    list_filter = ('classification', 'status')
    list_per_page = 20 # Limit number of items per page

    def get_queryset(self, request):
        # Optimize query with select_related and only fetch necessary fields
        queryset = super().get_queryset(request).select_related('sku')

        # Add additional filtering if needed
        if 'sku_id' in request.GET:
            queryset = queryset.filter(sku_id=request.GET['sku_id'])

        return queryset

    def sku_code(self, obj):
        return obj.sku.sku_code if obj.sku else None

    def warehouse(self, obj):
        user_id = getattr(obj.sku, 'user', None)
        if user_id:
            try:
                return User.objects.get(id=user_id).username
            except User.DoesNotExist:
                return f"User {user_id}"
        return None

    # Add sorting capability
    sku_code.admin_order_field = 'sku__sku_code'
    warehouse.admin_order_field = 'sku__user'

@admin.register(ReplenishmentMaster)
class ReplenishmentMasterAdmin(RestrictPermission):
    ''' Customising ReplenishmentMaster '''

    list_display = (
        'id', 'classification', 'user',
        'min_days', 'max_days',
    )
    search_fields = ['classification']
    list_filter = ('classification', 'status')
    list_per_page = 20 # Limit number of items per page