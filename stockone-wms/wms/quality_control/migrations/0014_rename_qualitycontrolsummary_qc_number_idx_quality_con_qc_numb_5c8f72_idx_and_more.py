# Generated by Django 4.2.20 on 2025-05-15 10:47

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('quality_control', '0013_rename_qualitycontrolsummary_employee_qc_number_quality_control_quality_con_employe_c5d557_idx_and_m'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='qualitycontrolsummary',
            new_name='QUALITY_CON_qc_numb_5c8f72_idx',
            old_name='qualitycontrolsummary_qc_number_idx',
        ),
        migrations.RenameIndex(
            model_name='qualitycontrolsummary',
            new_name='QUALITY_CON_employe_c5d557_idx',
            old_fields=('employee', 'qc_number', 'quality_control'),
        ),
        migrations.RenameIndex(
            model_name='qualitycontrolsummary',
            new_name='QUALITY_CON_qc_numb_5c8f72_idx',
            old_fields=('qc_number',),
        ),
    ]
