# python imports
import pandas as pd

# django imports
from django.db.models import Sum, F

# inventory imports
from inventory.views.locator.stock_detail import get_putaway_and_non_sellable_quantity

# inbound imports
from inbound.models import PurchaseOrder


class StockDataMixIn():
    def __init__(self, user_id, sku_ids):
        self.warehouse_ids = [user_id]
        self.sku_ids = sku_ids
        self.user_id = user_id

    def dict_to_df(self, data_dict, columns, default_columns=[]):
        """
        Convert a dictionary to a DataFrame with specified columns.
        Args:
            data_dict (dict): Dictionary containing data.
            columns (list): List of column names for the DataFrame.
        Returns:
            pd.DataFrame: DataFrame created from the dictionary.
        """
        df = pd.DataFrame(list(data_dict.items()), columns=columns)
        if not df.empty:
            for col in default_columns:
                df[col] = df[col].astype(float).fillna(0.0)
        return df

    def get_data(self):
        pen_putaway_quantity, pen_return_putaway, pull_to_locate_quantity, non_sellable_quantity, jo_pending_putaway = get_putaway_and_non_sellable_quantity(self.warehouse_ids, self.sku_ids)

        self.set_pending_putaway_quantity(pen_putaway_quantity)
        self.set_return_putaway_quantity(pen_return_putaway)
        self.set_pull_to_locate_quantity(pull_to_locate_quantity)
        self.set_non_sellable_quantity(non_sellable_quantity)
        self.set_jo_pending_putaway_quantity(jo_pending_putaway)
        self.set_pending_grn_quantity()

        return {
            'pending_putaway_df': self.pen_pw_df,
            'return_putaway_df': self.return_pw_df,
            'pull_to_locate_df': self.pull_to_locate_df,
            'non_sellable_df': self.non_sellable_df,
            'jo_pending_putaway_df': self.jo_pending_putaway_df,
            'pending_grn_df': self.pending_grn_df
        }

    def set_pending_putaway_quantity(self, pen_putaway_quantity):
        """
        Set the pending putaway quantity DataFrame.
        Args:
            pen_putaway_quantity (list): List of tuples containing SKU codes and their pending putaway quantities.
        """
        columns = ['sku_code', 'pending_putaway_qty']
        default_columns = ['pending_putaway_qty']
        self.pen_pw_df = self.dict_to_df(pen_putaway_quantity, columns, default_columns)

    def set_return_putaway_quantity(self, pen_return_putaway):
        columns = ['sku_code', 'return_putaway_qty']
        default_columns = ['return_putaway_qty']
        self.return_pw_df = self.dict_to_df(pen_return_putaway, columns, default_columns)

    def set_pull_to_locate_quantity(self, pull_to_locate_quantity):
        columns = ['sku_code', 'pull_to_locate_qty']
        default_columns = ['pull_to_locate_qty']
        self.pull_to_locate_df = self.dict_to_df(pull_to_locate_quantity, columns, default_columns)

    def set_non_sellable_quantity(self, non_sellable_quantity):
        columns = ['sku_code', 'non_sellable_qty']
        default_columns = ['non_sellable_qty']
        self.non_sellable_df = self.dict_to_df(non_sellable_quantity, columns, default_columns)

    def set_jo_pending_putaway_quantity(self, jo_pending_putaway):
        columns = ['sku_code', 'jo_pending_putaway_qty']
        default_columns = ['jo_pending_putaway_qty']
        self.jo_pending_putaway_df = self.dict_to_df(jo_pending_putaway, columns, default_columns)

    def set_pending_grn_quantity(self):
        po_objs = PurchaseOrder.objects.filter(
                open_po__sku__user=self.user_id,
                received_quantity__lt=F('open_po__order_quantity')
            ).exclude(status='location-assigned')

        if self.sku_ids:
            po_objs = po_objs.filter(open_po__sku__in=self.sku_ids)

        pending_grn_data =list(po_objs.values_list(
            'open_po__sku__sku_code').distinct().annotate(
                pending_grn_qty=Sum(F('open_po__order_quantity')-F('received_quantity'))
            )
        )
        self.pending_grn_df = pd.DataFrame(pending_grn_data, columns=['sku_code', 'pending_grn_qty'])