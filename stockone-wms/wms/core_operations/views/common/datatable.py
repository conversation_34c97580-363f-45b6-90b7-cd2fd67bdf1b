#package imports
import copy
import datetime
import json
import os
from django.forms import model_to_dict
from django.conf import settings
import pandas as pd
from django.template import Template, Context
from weasyprint import HTML

#django imports
from django.http import HttpResponse, JsonResponse
from django.utils.decorators import method_decorator

#restframework
from rest_framework.authentication import BasicAuthentication
from rest_framework import filters, viewsets
from rest_framework.response import Response

#core imports
from core.models.common import MasterHeadersView, InvoiceForms
from core.models.masters import UserAttributes
from core_operations.views.common.audilogs_html import html_template
from core_operations.views.common.datatable_utils import DATA_TABLE
from core_operations.serializers.datatable import MasterHedersViewSerializer
from core_operations.views.integration.user_integration import CsrfExemptSessionAuthentication
from core_operations.views.common.main import (
    get_misc_value, get_rendered_invoice_data, get_po_company_logo
)

#wms_base imports
from wms_base.models import User
from wms_base.wms_utils import (AJAX_DATA,
    init_logger, generate_file_from_df, COMPANY_LOGO_PATHS
)

#core_operations imports
from core_operations.views.common.main import (create_mail_attachments, get_local_date_known_timezone, get_user_time_zone, 
                                               get_warehouse, get_search_params, 
                                               print_excel, send_data_url
                                            )

#outbound datatable imports
from outbound.views.masters.customer_master.datatable import get_customer_master
from outbound.views.masters.pricingmaster.main import get_price_master_results
from outbound.views.masters.order_type_master import get_ordertype_zone_mapping_results
from outbound.views.masters.customer_sku_mapping import get_customer_sku_mapping
from outbound.views.masters.route_master import get_route_master_results
from outbound.views.audit.audit_master import get_audit_master_results
from outbound.views.audit.main import get_pending_audit_results

from outbound.views.orders.datatable import order_view_data, order_view_line_level_data

from outbound.views.picklist.datatable import open_orders, get_skipped_tasks_results

from outbound.views.invoice.datatables import get_customer_invoice_tab_data

from outbound.views.shipment.datatables import get_shipment_data, get_shipment_tab_data

from outbound.views.manifest.datatables import get_manifest_data

from outbound.views.sales_return.datatable import get_creditnote_tab_data

from outbound.views.flipkart_sorting.sorting_station_master import get_sorting_station_master_results

#channels import
from wms_base.notifications import push_notifications

#celery import
from wms.celery import app as celery_app

#core datatable imports
from core_operations.views.masters.uom_master import get_uom_master
from core_operations.views.masters.sku_master import get_sku_results
from core_operations.views.masters.tax_master import get_tax_master
from core_operations.views.masters.carton_master import get_lpn_data
from core_operations.views.masters.user_master import get_staff_master
from core_operations.views.masters.role_master import get_role_master
from core_operations.views.common.utils import generate_file_link
from core_operations.views.masters.currency_exchange_master import get_currency_exchange_master_results
from core_operations.views.common.main import get_audit_logs_data
from core_operations.views.common.labels import get_labeldata_details
from core_operations.views.common.user_attributes import checklist_datatable
from core_operations.views.integration.main import (
        get_user_integrations_data,
        get_user_integrations_api_data,
        get_user_integrations_calls_data)
from core_operations.views.masters.approval_master import po_approval_config_data

#inventory imports
from inventory.views.masters.replenishment_master import (
    get_inv_replenishment_master,
    get_min_max_based_replenishment_master,
    get_nte_replenishment_master,
    get_ars_replenishment_classificaion_master
)
from inventory.views.masters.sku_pack_master import get_sku_pack_master
from inventory.views.masters.location_master import get_location_details, get_staging_route_details
from inventory.views.masters.zone_master import get_zone_details, get_location_types
from inventory.views.locator.stock_detail import get_batch_level_stock, inventory_summary, sku_stock_summary, get_closing_stock_report_data
from inventory.views.replenishment.datatable import get_batosa_detail
from inventory.views.cycle_count.datatable import (
    get_cycle_count, get_cycle_confirmed, get_scheduled_cycle_count
)
from inventory.views.adjustment.inventory_adjustment import get_inventory_adjustment
from inventory.views.planning.datatable import (
    get_min_max_material_planning_data, get_forecast_results,
    get_material_requirement_planning_data, get_back_order_data
)
from inventory.views.move.move_inventory import get_move_inventory
from inventory.views.masters.hierarchy_master import get_hierarchy_data, get_hierarchy_headers

#seller usage
from inventory.views.seller_usage.datatable import get_seller_usage_master, get_seller_cost_master

#production
from production.views.bom.bom import get_bom_results
from production.views.create_jo.raise_jo import (
    get_open_jo, job_order_view
)
from production.views.create_jo.confirmed_jo import get_jo_confirmed
from production.views.create_jo.datatable import dispense_datatable
from production.views.jo_grn.datatable import (
    jo_grn_datatable
)
from production.views.quality_check.datatable import get_jo_grn_qc_data

#inbound imports
from inbound.views.supplier.datatable import (
    supplier_sku_mapping_datatable, supplier_datatable, supplier_csv_mapping_datatable
    )
from inbound.views.purchase_order.datatable import get_pending_po_suggestions, po_datatable
from inbound.views.grn.datatable import (get_asn_data,
                                         get_sales_return_grn_data_table,
                                         grn_datatable, asn_datatable,
                                         sr_grn_datatable, grn_pending_datatable)
from inbound.views.quality_check.datatable import get_po_grn_qc_data, get_sr_grn_qc_data
from inbound.views.putaway.datatable import (
    po_putaway_datatable, cp_putaway_datatable, jo_putaway_datatable,
    get_sr_putaway_data, get_cancelled_putaway, get_putaway_data_table_data,
    sr_putaway_datatable
    )
from inbound.views.putaway.putaway_mapping import get_putaway_mapping
from inbound.views.rtv.datatable import get_po_putaway_data, get_saved_rtvs,get_closed_rtvs

#Gate Management
from core_operations.views.gate_management.datatable import get_gate_details, get_gate_pass_details

#dock scheduling
from core_operations.views.dock_scheduling.datatable import appointment_details

#Sentry
import sentry_sdk

DATATABLE_MAPPING = {

    #inventory datatables
    'InventoryReplenishmentMaster': get_inv_replenishment_master,
    'MinMaxReplenishmentMaster': get_min_max_based_replenishment_master,
    'NTEReplenishmentMaster' : get_nte_replenishment_master,
    'SKUPackMaster': get_sku_pack_master,
    'LocationMaster': get_location_details,
    'StagingRouting': get_staging_route_details,
    'PutawayMapping' : get_putaway_mapping,
    'ZoneMaster': get_zone_details,
    'SubZoneMaster' : get_zone_details,
    'LocationTypes' : get_location_types,
    'BatchLevelStock': get_batch_level_stock,
    'StockSummary': inventory_summary,
    'ClosingStockReport': get_closing_stock_report_data,
    'BatchWiseStockSummary': sku_stock_summary,
    'LocationWiseStockSummary': sku_stock_summary,
    'LocationBatchWiseStockSummary': sku_stock_summary,
    'MultiWarehouseStockSummary': inventory_summary,
    'MultiWarehouseStockDetail': sku_stock_summary,

    'BAtoSADetail': get_batosa_detail,
    'CycleCount': get_cycle_count,
    'ConfirmCycleCount': get_cycle_confirmed,
    'ScheduledCycleCount': get_scheduled_cycle_count,
    'EmptyLocationCycleCount' : get_cycle_confirmed,
    'InventoryAdjustment': get_inventory_adjustment,
    'MoveInventory': get_move_inventory,

    #Seller Usage
    "SellerUsage": get_seller_usage_master,
    "SellerPricingMaster": get_seller_cost_master,

    #material planning
    'MinMaxMaterialPlanning': get_min_max_material_planning_data,
    'ForeCastMaster': get_forecast_results,
    'MaterialRequirementPlanning': get_material_requirement_planning_data,
    'OutboundBackOrders': get_back_order_data,

    #production datatables
    'JobOrder': job_order_view,
    'BOMMaster': get_bom_results,
    'RaiseJobOrder': get_open_jo,
    'RawMaterialPicklist': get_jo_confirmed,
    'ReceiveJO1': jo_grn_datatable,
    'JODispense': dispense_datatable,
    'JOQualityControl': get_jo_grn_qc_data,
    'JOPutaway' : jo_putaway_datatable,

    #outbound datatables
    'CustomerMaster': get_customer_master,
    'PricingMaster': get_price_master_results,
    'OrderTypeZoneMapping': get_ordertype_zone_mapping_results,
    'CustomerSKUMapping': get_customer_sku_mapping,
    'RouteMaster': get_route_master_results,
    'SortingStationMaster': get_sorting_station_master_results,
    'AuditMaster': get_audit_master_results,
    'AuditView': get_pending_audit_results,

    #picklist datatables
    'OpenOrders': open_orders,
    'OrderView': order_view_data,
    'JobOrderOpenOrders': open_orders,
    'OrderViewSkuLevel': order_view_line_level_data,
    'SkippedTasks': get_skipped_tasks_results,

    #invoice datatables
    'ProcessedOrders': get_customer_invoice_tab_data,
    'CustomerInvoicesTab': get_customer_invoice_tab_data,
    'DeliveryChallans': get_customer_invoice_tab_data,
    'CancelledInvoicesTab' : get_customer_invoice_tab_data,
    'StockTransferInvoiceTab': get_customer_invoice_tab_data,

    #shipment datatables
    'InvoiceShipment': get_shipment_tab_data,
    'RTVShipment': get_shipment_tab_data,
    'InvoiceShipmentInfo': get_shipment_data,
    'RTVShipmentInfo': get_shipment_data,

    #manifest datatables
    'Manifest': get_manifest_data,

    #salesreturn datatables
    'CreditNote' : get_creditnote_tab_data,
    
    #core datatables
    'UOMMaster': get_uom_master,
    'SKUMaster': get_sku_results,
    'TaxMaster': get_tax_master,
    'StaffMaster': get_staff_master,
    'RoleMaster': get_role_master,
    'POApprovalConfig': po_approval_config_data,
    'CurrencyExchangeMaster': get_currency_exchange_master_results,
    'CartonMaster': get_lpn_data,
    'checkList' : checklist_datatable,
    'InventoryApprovalConfig': po_approval_config_data,
    'HierarchyMaster': get_hierarchy_data,

    #Inbound Datatables
    'SupplierSKUMappingMaster': supplier_sku_mapping_datatable,
    'SupplierMaster': supplier_datatable,
    'SupplierCSVMapping': supplier_csv_mapping_datatable,
    'PurchaseOrder' : po_datatable,
    'RaisePendingPurchase': get_pending_po_suggestions,
    'ReceivePOBeta' : grn_datatable,
    'SalesRetunGRN': get_sales_return_grn_data_table, 
    'SalesReturnGrnBeta': sr_grn_datatable,
    'SalesReturnBeta': sr_grn_datatable,
    'ASNData': get_asn_data,
    'ASNDataBeta' : asn_datatable,
    'GRNApprovalPending': asn_datatable,
    'GRNPending': grn_pending_datatable,
    'POPutaway': get_putaway_data_table_data,
    'POPutaway1': po_putaway_datatable,
    'SRPutaway': get_sr_putaway_data,
    'SRPutawayNew': sr_putaway_datatable,
    'CPPutaway' : cp_putaway_datatable,
    'PullToLocate': get_cancelled_putaway,
    'ReturnToVendor': get_po_putaway_data,
    'CreatedRTV': get_saved_rtvs,
    'ClosedRTV' : get_closed_rtvs,
    'POQualityControl': get_po_grn_qc_data,
    'SRQualityControl': get_sr_grn_qc_data,

    # User Integrations
    'UserIntegrations': get_user_integrations_data,
    'UserIntegrationsAPIs': get_user_integrations_api_data,
    'UserIntegrationCalls': get_user_integrations_calls_data,

    #Audit Logs
    "AuditLogs": get_audit_logs_data,

    #label
    "LabelData": get_labeldata_details,


    #Gate Management
    'Gate': get_gate_details,
    'GatePass': get_gate_pass_details,

    #Dock Scheduling
    'AppointmentDetails': appointment_details
}

download_headers_mapping = {
    'SKUMaster': 'sku_master', 'LocationMaster':'location_master', 'StagingRouting':'staging_routing',
    'ZoneMaster':'zone_master','SupplierMaster':'supplier_master',
    "TaxMaster": "tax_master", "UOMMaster":"uom_master",
    "CartonMaster":"lpn_master", "SupplierSKUMappingMaster":"supplier_sku_mapping_master",
    "InventoryReplenishmentMaster":"inventory_replenishment_master", "MinMaxReplenishmentMaster":"min_max_replenishment_master",
    "CustomerMaster":"customer_master", "CustomerSKUMapping":"customer_sku_mapping_master",
    "BOMMaster":"bom_master", "PricingMaster":"pricing_master",
    "StaffMaster":"user_access_control_master", "RoleMaster":"role_master", "OrderTypeZoneMapping":"order_type_zone_mapping_master",
    "CurrencyExchangeMaster":"currency_exchange_master", "POApprovalConfig":"po_approval_config_master",
    "SKUPackMaster":"sku_pack_master", "BatchLevelStock":"batch_level_stock_master",
    "BAtoSADetail":"ba_to_sa_detail_master", "CycleCount":"cycle_count_master","EmptyLocationCycleCount":"empty_location_cycle_count_master",
    "ConfirmCycleCount":"confirm_cycle_count_master", "ScheduledCycleCount":"scheduled_cycle_count_master",
    "MoveInventory":"move_inventory_master", "InventoryAdjustment":"inventory_adjustment_master",
    "RaisePO":"raise_po_master", "ReceivePO":"receive_po_master", "ASNData":"asn_data_master",
    "SalesReturnGrn":"sales_return_grn_master", "ReceivePOBeta":"reecive_po_beta_master",
    "ASNDataBeta":"asn_data_beta_master", "SalesReturnGrnBeta":"sales_return_grn_beta_master",
    "POQualityControl":"po_quality_control_master", "SRQualityControl":"sr_quality_control_master",
    "POPutaway":"po_putaway_master", "SRPutaway":"sr_putaway_master", "CPPutaway":"pull_to_locate_master",
    "RTVMaster":"created_rtv_master", "ReturnToVendor":"return_to_vendor_master", "SalesReturn":"sales_return_master",
    "MinMaxMaterialPlanning":"min_max_material_planning_master", "OutboundBackOrders":"outbound_back_orders_master",
    "ForCastMaster":"fore_cast_master", "MaterialRequirementPlanning":"material_requirement_planning_master",
    "RaiseJobOrder":"raise_job_order_master", "RawMaterialPicklist":"raw_material_picklist_master",
    "JobOrderOpenOrders":"job_order_open_orders_master","OpenOrders":"open_orders_master",
    "JOQualityControl":"jo_quality_control_master", "PutawayConfirmation":"putaway_confirmation_master",
    "ProcessedOrders":"processed_orders_master", "DeliveryChallans":"delivery_challans_master", 
    "CustomerInvoicesTab":"customer_invoices_tab_master", "StockTransferInvoiceTab":"stock_transfer_invoice_tab_master",
    "CancelledInvoicesTab":"cancelled_invoices_master", "InvoiceShipment":"invoice_shipment_master",
    "Manifest":"manifest_master","RTVShipment":"rtv_shipment_master",
    "UserIntegrations":"user_integrations_master", "UserIntegrationsAPIs":"user_integrations_apis_master",
    "UserIntegrationCalls":"user_integration_calls_master", "ClosingStockReport":"closing_stock_report_master","StockSummary":"stock_summary_master",
    'BatchWiseStockSummary': 'batch_stock_summary_master','LocationWiseStockSummary': 'location_stock_summary_master',
    'LocationBatchWiseStockSummary': 'location_batch_stock_summary_master',
    'MultiWarehouseStockSummary': 'multi_warehouse_stock_summary_master',
    'MultiWarehouseStockDetail': 'multi_warehouse_stock_detail_master',
    'AuditLogs':"audit_logs_master", 'PurchaseOrder':"purchase_order_master", "JobOrder":'job_order_master',
    "OrderView": "order_view_master", "JODispense": "job_order_dispense_master", "InventoryApprovalConfig":'inventory_approval_config_master',
    "LabelData": "labeldata_master", "checkList": "checklist_master", "CreatedRTV": "saved_rtv_master","ClosedRTV":"closed_rtv_master","SalesReturnBeta":"sales_return_beta_master",
    "CreditNote" :"credit_note_master", "ReceiveJO1":"receive_jo_master","InvoiceShipmentInfo" : "invoice_shipment_data_master",
    "RTVShipmentInfo":"rtv_shipment_data_master","PutawayMapping" : "putaway_mapping",  "LocationTypes" : "location_type_master",
    "HierarchyMaster":"hierarchy_master", "SubZoneMaster" : "subzone_master", "Gate": "gate_master", "GatePass": "gate_pass_master",
    "RouteMaster": "route_master", "NTEReplenishmentMaster" : "nte_replenishment_master", "SupplierCSVMapping": "supplier_csv_mapping",
    "OrderViewSkuLevel": "order_view_sku_level_master", "GRNApprovalPending": "grn_approval_pending_master",
    "POPutaway1": "po_putaway_master", "GRNPending": "grn_pending_master", "AppointmentDetails": "appointment_detail_master",
    "SortingStationMaster": "sorting_station_master", "AuditView": "audit_view", "AuditMaster": "audit_master",
    "SkippedTasks": "skipped_tasks_view", "SellerUsage": "seller_usage_data_master", "SellerPricingMaster": "seller_usage_cost_master"
}

new_master_tables =  ["SKUMaster", "LocationMaster", "ZoneMaster", "SupplierMaster", "TaxMaster", "CartonMaster", "SupplierSKUMappingMaster",
    "PutawayMapping", "SupplierCSVMapping", "POPutaway1", "StagingRouting",
    "InventoryReplenishmentMaster", "MinMaxReplenishmentMaster", "CustomerMaster", "CustomerSKUMapping",
    "BOMMaster", "CustomerSKUMapping", "PricingMaster", "StaffMaster", "RoleMaster", "OrderZoneTypeMappingMaster",
    "ReceivePOBeta", "ASNDataBeta", "SalesReturnGrnBeta","UOMMaster", "MoveInventory","SalesReturnBeta",
    "CurrencyExchangeMaster", "POApprovalConfig", "SKUPackMaster", "InventoryAdjustment", "CycleCount",
    "ConfirmCycleCount", "ScheduledCycleCount", "EmptyLocationCycleCount", "BatchLevelStock", "OrderTypeZoneMapping",
    "UserIntegrations", "UserIntegrationsAPIs", "UserIntegrationCalls", "StockSummary", "ClosingStockReport", "BAtoSADetail",
    "BatchWiseStockSummary", "LocationWiseStockSummary", "LocationBatchWiseStockSummary", "MultiWarehouseStockSummary",
    "MultiWarehouseStockDetail", "AuditLogs", "PurchaseOrder", "JobOrder",
    "OrderView", "JODispense", "OpenOrders", "JobOrderOpenOrders", "RawMaterialPicklist", "InventoryApprovalConfig",
    "LabelData", "checkList", "ReturnToVendor", "CreatedRTV","ClosedRTV","CreditNote","ProcessedOrders", "DeliveryChallans",
    "CancelledInvoicesTab","CustomerInvoicesTab","StockTransferInvoiceTab", "CPPutaway","InvoiceShipment", "RTVShipment", 
    "ReceiveJO1","InvoiceShipmentInfo", "RTVShipmentInfo","Manifest","ReceiveJO1", "LocationTypes", "POQualityControl", "SRQualityControl",
    "RouteMaster", "HierarchyMaster", "Gate", "GatePass", "RouteMaster", "NTEReplenishmentMaster", "SubZoneMaster", "OrderViewSkuLevel",
    "GRNApprovalPending", "GRNPending", "AppointmentDetails", "SortingStationMaster", "AuditView", "AuditMaster",
    "SkippedTasks", "SellerUsage", "SellerPricingMaster"]

ATTRIBUTES_DICT = {
    'sku_master' : 'sku'
}
log = init_logger('logs/data_table.log')

class MasterHeadersViewSet(viewsets.ModelViewSet):
    serializer_class = MasterHedersViewSerializer
    filter_backends = [filters.SearchFilter]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_queryset(self):
        warehouse = self.request.warehouse
        data = MasterHeadersView.objects.filter(warehouse=warehouse)
        if not data:
            data = MasterHeadersView.objects.filter(warehouse=None)

            if not data:
                result = {}
                for key, value in DATA_TABLE.items():
                    result[key] = list(value.values())[0]
                return result
        return data

    @method_decorator(get_warehouse)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        if isinstance(queryset, dict):
            data = queryset
        else:
            serializer = self.serializer_class(queryset, many=True)
            data = serializer.data[0]
        return Response(data)

@get_warehouse
def results_data(request, warehouse: User):
    headers, search_params, filter_params = get_search_params(request)
    if request.method == 'GET':
        request_data = request.GET
        if not request_data:
            try:
                request_data = json.loads(request.body)
            except json.JSONDecodeError as e:
                log.info('Results Data failed for params'+str(e))
                return JsonResponse({"Invalid JSON": str(e)})
    else:
        request_data = request.POST
    final_data = {}
    config_columns = {"tableColumns": [], "viewableColumns": [], "filterableColumns":[]}
    validate_trigger = "is_viewable"
    if request_data.get('download') == 'true':
        validate_trigger = "download"
    datatable = request_data.get('datatable', None)
    sentry_sdk.set_tag('datatable_name', datatable)
    if datatable is not None:
        master_type = download_headers_mapping.get(datatable, '')
    else:
        master_type = ''
    temp_data = copy.deepcopy(AJAX_DATA)
    temp_data['draw'] = search_params.get('draw')
    excel = request_data.get('excel')
    format_pdf = request_data.get('pdf')
    start_index = search_params.get('start', 0)
    content_type_const = 'application/json'
    if excel == 'true':
        start_index, stop_index = None, None
        special_keys = search_params
        search_params = {'start': 0, 'draw': 1, 'length': 0, 'order_index': 0, 'order_term': u'asc',
                         'search_term': request_data.get('search[value]', '')}
        for key, value in special_keys.items():
            search_params[key] = value
    else:
        stop_index = start_index + search_params.get('length', 0)
    params = [start_index, stop_index, temp_data, search_params.get('search_term', 0)]
    if datatable in DATATABLE_MAPPING.keys():
        fun = DATATABLE_MAPPING[datatable]
        params.extend([search_params.get('order_term', 0), search_params.get('order_index', 0), request, warehouse])
        if 'from_date' in search_params.keys() or 'to_date' in search_params.keys():
            filter_params['from_date'] = search_params.get('from_date','')
            filter_params['to_date'] = search_params.get('to_date','')
            params.append(filter_params)
        elif 'from_datetime' in search_params.keys() or 'to_datetime' in search_params.keys():  
            filter_params['from_datetime'] = search_params.get('from_datetime','')
            filter_params['to_datetime'] = search_params.get('to_datetime','')
            params.append(filter_params)
        elif 'exp_from_datetime' in search_params.keys() or 'exp_to_datetime' in search_params.keys():  
            filter_params['exp_from_datetime'] = search_params.get('exp_from_datetime','')
            filter_params['exp_to_datetime'] = search_params.get('exp_to_datetime','')
            params.append(filter_params)
        elif filter_params:
            params.append(filter_params)
        if 'special_key' in search_params.keys():
            params.append(search_params.get('special_key'))
        if datatable in  new_master_tables:
            config_columns = prepare_datatable(warehouse, validate_trigger, master_type, config_columns)
            filter_params.update({"filterableColumns": config_columns.get("filterableColumns", []), "viewableColumns": config_columns.get("viewableColumns", [])})
        fun(*params)
    else:
        temp_data = {"recordsTotal": 0, "recordsFiltered": 0, "draw": 2, "aaData": [], "headers": {}, "card_details": []}
    
    if datatable in  new_master_tables:
        response_data = {'data': {}, 'message': 'Fail'}
        if validate_trigger == 'download':
            return HttpResponse(get_datatable_download_url(warehouse, temp_data['aaData'], config_columns["columns"], master_type))
        config_columns.pop("columns")
        final_data["columns"] = config_columns
        final_data["dataAndCount"] = {"data": temp_data['aaData']}
        final_data["card_details"] = temp_data.get('card_details', [])
        final_data['count'] = temp_data.get('count', 0)
        if temp_data.get('default_from_range'):
            final_data['default_from_range'] = temp_data['default_from_range']
        if format_pdf == 'true':
            #prepare and generate audit logs 
            audits = prepare_auditlogs_pdf.apply_async(kwargs={"user_id": request.user.id, "table_data": final_data, "url": request.build_absolute_uri('/'), "warehouse_id": warehouse.id})
            response_data['message'] = 'Download is In-progress'
            response_data['data']['task_id'] = audits.id
            response =  HttpResponse(json.dumps(response_data), content_type=content_type_const)
            return response
        return JsonResponse(final_data, content_type=content_type_const)
    else:
        if excel == 'true':
            params = [temp_data, search_params.get('search_term'), search_params.get('order_term'),
                search_params.get('order_index'), request, warehouse, filter_params]

            excel_name = datatable
            headers = temp_data.get('headers', {}) or {}
            if not headers and temp_data and temp_data['aaData']:
                headers_keys = temp_data['aaData'][0].keys()
                for key_str in headers_keys:
                    if key_str not in [None, '', 'null', 'id'] and key_str not in headers.keys():
                        headers[key_str] = key_str
            excel_data = print_excel(request, temp_data, headers, warehouse=warehouse, excel_name=excel_name, file_type='csv')
            return HttpResponse(excel_data)
        return JsonResponse(temp_data, content_type=content_type_const)

def prepare_datatable(warehouse: User, validate_trigger=None, master_type=None, config_columns=None):
    if validate_trigger is None:
        validate_trigger = ""
    if master_type is None:
        master_type = ""
    if config_columns is None:
        config_columns = {}

    if master_type == "hierarchy_master":
        headers = get_hierarchy_headers(warehouse)
        config_columns['viewableColumns'] = headers.get('headers', [])
    else:
        headers = MasterHeadersView.objects.filter(warehouse_id=warehouse.id).values(master_type)
        if not headers.exists():
            headers = MasterHeadersView.objects.filter(warehouse_id__isnull=True).values(master_type)
        headers = headers[0] if headers and headers[0].get(master_type, []) else DATA_TABLE.get(master_type, {})

    qtable_columns_list = []
    for key, heder_data  in headers.items():
        for header_value  in heder_data:
            qtable_columns_dict = header_value
            qtable_columns_dict['name'] = header_value.get('field')
            qtable_columns_list.append(qtable_columns_dict)

        columns = pd.DataFrame(qtable_columns_list).fillna('')
        columns_filterable = pd.DataFrame()
        if columns.empty:
            continue
        if validate_trigger == 'download':
            columns = columns[columns['isDownload'] == True]
        else:
            columns_filterable = columns[columns['editable'] == True]
            columns = columns[columns['isViewable'] == True]
        qtable_columns = columns.to_dict(orient='records')
        config_columns["tableColumns"] = qtable_columns
        config_columns["columns"] = columns
        config_columns["filterableColumns"] = columns_filterable.to_dict(orient='records')
    return config_columns

def get_datatable_download_url(warehouse, data, columns, master_type):
    now = datetime.datetime.now()
    table_data = pd.DataFrame(data)
    required_columns = [col for col in columns['field'] if col in table_data.columns]
    required_columns = get_additonal_columns(warehouse, required_columns, master_type)
    truncated_table_data = table_data.reindex(columns=required_columns, fill_value="")
    convertion_datatable_dict = dict(columns[['name', 'header']].values)
    final_df = truncated_table_data.rename(columns=convertion_datatable_dict)
    file_name = "%s-download-%s.csv" % (master_type, now)
    return generate_file_link(
            generate_file_from_df(final_df, file_name)
        )

def get_additonal_columns(warehouse, required_columns, master_type):
    attr_model = ATTRIBUTES_DICT.get(master_type)
    if attr_model:
        filters = {'user_id' : warehouse.id, 'status' : 1, 'attribute_model' : attr_model}
        required_columns.extend(list(UserAttributes.objects.filter(**filters).values_list('attribute_name',flat=True)))
    return required_columns

@celery_app.task(bind=True)
def prepare_auditlogs_pdf(self, user_id, table_data, url, warehouse_id: int):
    ''' Prepare and generate AudiLogs '''
    #getting user objects
    company_logo = ''
    user = User.objects.get(id=user_id)
    warehouse = User.objects.get(id=warehouse_id)

    #prepare data to be rendered
    now = datetime.datetime.now()
    data_list = table_data['dataAndCount'].get("data", [])
    data_list = [dict(item) for item in data_list]
    timezone = get_user_time_zone(warehouse)
    print_date = get_local_date_known_timezone(timezone, now,send_date=True).strftime('%Y-%m-%d %I:%M')
    logo = warehouse.userprofile.company.logo
    if logo:
        company_logo = logo.url
        if not getattr(settings, 'CLOUD_STORAGE', False):
            company_logo = url + logo.name

    final_dict = {        
        "warehouse" : warehouse.username,
        "company_logo" : company_logo,
        "data_list": data_list,
        "printed_by": str(user),
        "date_time": print_date,
        "warehouse_company_data": model_to_dict(warehouse.userprofile.company),
        "warehouse_data": model_to_dict(warehouse.userprofile),
    }

    #render auditlog html
    rendered_html = render_template(warehouse, final_dict)
    #generate auditlog pdf
    pdf_path = generate_pdf(rendered_html, warehouse)

    #push results to websockets
    push_notifications(warehouse, user, self.request.id , {"status": "Success!Please Download","download_url": pdf_path})


def render_template(warehouse, final_dict):
    '''Fetch and Render Audit logs '''
    audit_logs_format = get_misc_value('AUDIT LOGS', warehouse.id)
    invoice_forms_data = InvoiceForms.objects.filter(invoice_format=audit_logs_format, document_type='AUDIT LOGS', status=1).values('output_data')
    if audit_logs_format not in ['', None, 'null', 'None', 'false'] and invoice_forms_data.exists():
        output_data = invoice_forms_data[0].get('output_data')
        audit_template = get_rendered_invoice_data(output_data, final_dict)
    else:
        template = Template(html_template)
        context = Context(final_dict)
        audit_template = template.render(context)
    return audit_template
    
def generate_pdf(html_content, warehouse):
    pdf_dir = "static/pdf_files"
    os.makedirs(pdf_dir, exist_ok=True)
    file_name = f"{warehouse.username}.auditlogs.pdf"
    pdf_file_path = os.path.join(pdf_dir, file_name)
    HTML(string=html_content).write_pdf(pdf_file_path)  # Generates the PDF
    path= send_data_url(str(pdf_file_path))
    return path