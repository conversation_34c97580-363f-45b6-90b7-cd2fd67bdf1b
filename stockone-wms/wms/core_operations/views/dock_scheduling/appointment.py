#package imports
import pandas as pd
from json import loads
from datetime import datetime
import pytz

#django imports
from django.http import JsonResponse
from django.db import transaction

#wms utils imports
from wms_base.models import User
from wms_base.wms_utils import init_logger
from wms.celery import app as celery_app

#core imports
from core.models import (
    GatePassItem, GatePass
)

#core operations imports
from core_operations.views.common.main import (
    WMSListView, get_local_date_known_timezone
)
from core_operations.views.services.dock_scheduling import DockSchedulingService

log = init_logger('logs/appointment.log')

@celery_app.task
def cancel_appointment_at_service(user, warehouse, appointment_id):
    warehouse = User.objects.get(username=warehouse)
    user = User.objects.get(username=user)
    dock_scheduling_service_instance = DockSchedulingService(user, warehouse)
    appointment_id, errors = dock_scheduling_service_instance.cancel_appointment(appointment_id)
    if errors:
        log.info("Cancel Appointment data failed with error- %s" % str(errors))

class AppointmentSet(WMSListView):
    def post(self, request, *args, **kwargs):
        try:
            self.request_data = loads(request.body)
        except Exception:
            self.request_data = request.POST.get("data") or {}

        self.errors = []

        self.validate_request_data()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)

        self.get_pending_reference_details()
        self.validate_reference_details()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)

        appointment_id, errors = self.create_appointment()
        if errors:
            return JsonResponse({'errors': errors}, status=400)

        self.create_and_update_appointment_details(appointment_id)
        return JsonResponse({'message': 'Appointment created successfully', "appointment_id": appointment_id}, status=200)

    def get(self, request, *args, **kwargs):
        self.final_response = {
            "errors": [],
            "data": {}
        }

        self.set_user_credientials()
        self.request_data = request.GET
        self.timezone = self.warehouse.userprofile.timezone or "Asia/Calcutta"

        self.filters = {
            "warehouse_id": self.warehouse.id,
            "gate_pass__gate_pass_type": "dock-scheduling"
        }
        self.get_appointment_filters()
        self.get_appointment_objects()

        if not self.gate_pass_items_details:
            self.final_response["errors"].append("Appointment details not found")
            return JsonResponse(self.final_response, status=400)

        self.prepare_get_response()
        return JsonResponse(self.final_response, status=200)

    def delete(self, request, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = request.GET
        self.appointment_id = self.request_data.get("appointment_id")

        self.get_appointment_object_for_cancel()

        if self.appointment_items_df.empty:
            return JsonResponse({'errors': ['Appointment details not found']}, status=400)
        elif self.is_cancelled:
            return JsonResponse({"errors": ['Appointment already cancelled!']}, status=400)
        elif self.is_gate_in:
            return JsonResponse({"errors": ["Gate In was done for the appointment, so can't able to cancel!"]}, status=400)

        appointment_id, errors = self.cancel_appointment()
        if errors:
            return JsonResponse({'errors': errors}, status=400)

        self.prepare_appointment_data_for_cancel()
        self.create_and_update_cancel_appointment_details()
        cancel_appointment_at_service.apply_async(args = [self.user.username, self.warehouse.username, self.appointment_id])

        return JsonResponse({'message': 'Appointment cancelled successfully', "appointment_id": appointment_id}, status=200)

    def validate_request_data(self):
        """
        {
            "appointment_date": "2021-09-01",
            "start_time": "09:30:00",
            "end_time": "10:30:00",
            "dock_master_id": 1,
            "function_type": "Inbound",
            "reference_type": "material_receipt",
            "items": [
                {
                    "reference_number": "123456",
                    "no_of_boxes":20,
                    "handling_unit": "Pallet",
                },
                {
                    "reference_number": "123457",
                    "no_of_boxes":10,
                    "handling_unit": "Pallet",
                }
            ]
        }
        """
        self.set_user_credientials()
        self.time_zone = self.warehouse.userprofile.timezone or "Asia/Calcutta"
        if not self.request_data.get("items"):
            self.errors.append("Items are required")
            return

        for field in ["appointment_date", "start_time", "end_time", "dock_master_id", "function_type", "reference_type"]:
            if not self.request_data.get(field):
                self.errors.append(f"{field} is required")
                return

        self.reference_numbers, self.no_of_boxes, self.handling_unit, self.reference_wise_details = [], 0, None, {}
        for item in self.request_data.get("items"):
            for field in ["reference_number", "no_of_boxes", "handling_unit"]:
                if not item.get(field):
                    self.errors.append(f"{field} is required")
                    return
            self.reference_numbers.append(item.get("reference_number"))
            self.no_of_boxes += item.get("no_of_boxes")
            self.handling_unit = item.get("handling_unit")
            self.reference_wise_details[item.get("reference_number")] = {
                "no_of_boxes": item.get("no_of_boxes"),
                "handling_unit": item.get("handling_unit")
            }

    def get_pending_reference_details(self):
        """
        Retrieves the pending reference details for the given warehouse, reference numbers, reference type, and status.
        Populates the 'dock_references_df' attribute with the query results.
        """
        filters = {
            "warehouse_id": self.warehouse.id,
            "transaction_id__in": self.reference_numbers,
            "transaction_type": self.request_data.get("reference_type", "material_receipt"),
            "status": 1,
            "gate_pass__isnull": True
        }
        gate_pass_item_query = GatePassItem.objects.filter(**filters)

        self.dock_references_df = pd.DataFrame(gate_pass_item_query.values())
        for gate_pass_obj in list(gate_pass_item_query):
            self.dock_references_df.loc[self.dock_references_df['id'] == gate_pass_obj.id, 'object'] = gate_pass_obj

    def validate_reference_details(self):
        """
        Validates the reference details for the given request data.
        """
        if self.dock_references_df.empty:
            self.errors.append("Given reference numbers are not found")
            return

        available_reference_numbers = self.dock_references_df['transaction_id'].to_list()
        missing_reference_numbers = set(self.reference_numbers) - set(available_reference_numbers)
        if missing_reference_numbers:
            self.errors.append(f"Reference Numbers {','.join(list(missing_reference_numbers))} not found")
            return

    def create_appointment(self):
        """
        Create a appointment for dock scheduling.

        Returns:
            appointment_id (int): The ID of the created appointment.
            errors (list): List of errors, if any occurred during the appointment creation.
        """
        appointment_data = {
            "user": self.user.username,
            "warehouse": self.warehouse.username,
            "dock_master_id": self.request_data.get("dock_master_id"),
            "appointment_date": self.request_data.get("appointment_date"),
            "start_time": self.request_data.get("start_time"),
            "end_time": self.request_data.get("end_time"),
            "handling_unit": self.handling_unit,
            "no_of_boxes": self.no_of_boxes,
            "status": self.request_data.get("status", "confirmed"),
            "json_data": self.request_data.get("json_data", {})
        }
        dock_scheduling_service_instance = DockSchedulingService(self.user, self.warehouse)
        appointment_id, errors = dock_scheduling_service_instance.create_appointment(appointment_data)
        return appointment_id, errors

    def create_and_update_appointment_details(self, appointment_id):
        """
        Create and update appointment details for a given appointment ID.

        Args:
            appointment_id (int): The ID of the appointment.

        Returns:
            None
        """
        with transaction.atomic('default'):
            gate_pass_obj = GatePass.objects.create(**self.prepare_header_appointment_data(appointment_id))
            for index, row in self.dock_references_df.iterrows():
                gate_pass_item_obj = row['object']
                gate_pass_item_obj.gate_pass_id = gate_pass_obj.id
                gate_pass_item_obj.status = 0
                reference_number = row.get('transaction_id')
                if reference_number and self.reference_wise_details.get(reference_number):
                    gate_pass_item_obj.actual_no_of_boxes = gate_pass_item_obj.no_of_boxes = self.reference_wise_details[reference_number].get("no_of_boxes")
                    gate_pass_item_obj.handling_unit = self.reference_wise_details[reference_number].get("handling_unit")
                self.dock_references_df.loc[index, 'object'] = gate_pass_item_obj
            GatePassItem.objects.bulk_update(self.dock_references_df['object'].to_list(), ['gate_pass', 'status', 'no_of_boxes', 'handling_unit', 'actual_no_of_boxes'], batch_size=100)

    def prepare_header_appointment_data(self, appointment_id):
        """
        Prepare header appointment data for a given appointment ID.

        Args:
            appointment_id (int): The ID of the appointment.

        Returns:
            dict: The prepared appointment header data.

        """
        appointment_header_data = {
            "warehouse_id": self.warehouse.id,
            "account_id": self.warehouse.userprofile.id,
            "gate_pass_id": appointment_id,
            "gate_pass_type": "dock-scheduling",
            "gate_pass_sub_type": self.request_data.get("reference_type", "material_receipt"),
            "status": 0,
            "json_data": self.request_data.get("json_data") or {},
            "lr_no": self.request_data.get("dock_master_id")
        }

        appointment_date_str = self.request_data.get("appointment_date")
        if appointment_date_str:
            local_format = "%Y-%m-%d"
            local_tz = pytz.timezone(self.time_zone)
            local_dt = datetime.strptime(appointment_date_str, local_format)
            local_dt = local_tz.localize(local_dt)
            appointment_header_data['lr_date'] = local_dt.astimezone(pytz.utc)
            
        appointment_header_data["json_data"].update({
            "start_time": self.request_data.get("start_time"),
            "end_time": self.request_data.get("end_time"),
            "function_type": self.request_data.get("function_type"),
            "reference_type": self.request_data.get("reference_type"),
            "handling_unit": self.handling_unit,
            "no_of_boxes": self.no_of_boxes,
            "dock_master_name": self.request_data.get("dock_master_name"),
        })
        return appointment_header_data

    def get_appointment_filters(self):
        """
        Get the appointment filters based on the request data.

        Returns:
            None
        """
        filter_key_mapping = {
            "appointment_id": "gate_pass__gate_pass_id",
            "supplier_id": "json_data__supplier_id",
            "supplier_name": "json_data__supplier_name",
            "asn_number": "transaction_id",
            "invoice_number": "json_data__invoice_number",
            "dock_master_name": "gate_pass__json_data__dock_master_name",
            "dock_master_id": "gate_pass__lr_no"
        }
        status_mapping = {
            "Confirmed": 0,
            "Completed": 1,
            "Cancelled": 8
        }
        for key, value in filter_key_mapping.items():
            if self.request_data.get(key):
                self.filters[value] = self.request_data.get(key)

        if self.request_data.get("status") and self.request_data.get("status") in status_mapping:
            self.filters["status"] = status_mapping.get(self.request_data.get("status"))

        self.prepare_date_time_filters()

    def prepare_date_time_filters(self):
        """
        Prepare date and time filters based on the request data.

        This method maps the request data keys to the corresponding datetime fields and converts the provided date strings
        to datetime objects in the specified time zone. It then adds the converted datetime objects to the filters dictionary
        with the corresponding field names.

        Supported request data keys:
        - appointment_date: Filter based on the appointment date.
        - from_date: Filter based on the starting date.
        - to_date: Filter based on the ending date.

        The datetime fields used for filtering are:
        - gate_pass__lr_date: The appointment date.
        - gate_pass__lr_date__gte: The starting date.
        - gate_pass__lr_date__lte: The ending date.

        If the 'to_date' is provided, it is incremented by one day to include the entire day.

        Note: The 'time_zone' attribute should be set before calling this method.

        Returns:
            None
        """
        datetime_field_mapping = {
            "appointment_date": "gate_pass__lr_date",
            "from_date": "gate_pass__lr_date__gte",
            "to_date": "gate_pass__lr_date__lte"
        }
        for key, value in datetime_field_mapping.items():
            if self.request_data.get(key):
                appointment_date_str = self.request_data.get(key)
                local_format = "%Y-%m-%d"
                local_tz = pytz.timezone(self.timezone)
                local_dt = datetime.strptime(appointment_date_str, local_format)
                local_dt = local_tz.localize(local_dt)
                self.filters[value] = local_dt.astimezone(pytz.utc)

    def get_appointment_objects(self):
        """
        Retrieve appointment objects based on the specified filters.

        Returns:
            QuerySet: A queryset of appointment objects matching the specified filters.
        """
        self.gate_pass_items_details = GatePassItem.objects.filter(**self.filters).values(
            "transaction_id", "transaction_type", "no_of_boxes", "handling_unit", "status", "no_of_boxes", "handling_unit", "json_data", "gate_pass__gate_pass_id",
            "gate_pass__lr_no", "gate_pass__json_data", "gate_pass__lr_date", "gate_pass__json_data", "gate_pass__gate_pass_type", "gate_pass__gate_pass_sub_type"
        )

    def prepare_get_response(self):
        """
        Prepare the response for the get request.

        Returns:
            dict: The prepared response containing appointment details.
        """
        # Prepare appointment item level details
        status_mapping = {
            0: "Confirmed",
            1: "Completed",
            8: "Cancelled"
        }
        for item in self.gate_pass_items_details:
            appointment_id = item.get("gate_pass__gate_pass_id")
            if not self.final_response["data"].get(appointment_id):
                self.final_response["data"][appointment_id] = {
                    "appointment_id": item.get("gate_pass__gate_pass_id"),
                    "start_time": item.get("gate_pass__json_data").get("start_time"),
                    "end_time": item.get("gate_pass__json_data").get("end_time"),
                    "dock_master_id": item.get("gate_pass__lr_no"),
                    "function_type": item.get("gate_pass__json_data").get("function_type"),
                    "handling_unit": item.get("gate_pass__json_data").get("handling_unit"),
                    "no_of_boxes": item.get("gate_pass__json_data").get("no_of_boxes"),
                    "status": status_mapping.get(item.get("status"), ""),
                    "supplier_id": item.get("json_data", {}).get("supplier_id"),
                    "supplier_name": item.get("json_data", {}).get("supplier_name"),
                    "items": [],
                    "no_of_references": 0,
                    "dock_master_name": item.get("gate_pass__json_data").get("dock_master_name"),
                }
                if item.get("gate_pass__lr_date"):
                    self.final_response["data"][appointment_id]["appointment_date"] = get_local_date_known_timezone(self.timezone, item.get("gate_pass__lr_date"), send_date=True).strftime("%Y-%m-%d")

            self.final_response["data"][appointment_id]["items"].append({
                "reference_number": item.get("transaction_id"),
                "transaction_type": item.get("transaction_type"),
                "no_of_boxes": item.get("no_of_boxes"),
                "handling_unit": item.get("handling_unit"),
                "status": item.get("status"),
                "json_data": item.get("json_data"),
            })
            self.final_response["data"][appointment_id]["no_of_references"] += 1

        self.final_response["data"] = list(self.final_response["data"].values())
        if len(self.final_response["data"]) == 1:
            self.final_response["data"] = self.final_response["data"][0]

    def get_appointment_object_for_cancel(self):
        """
        Retrieves the appointment object for cancellation.

        Returns:
            None
        """
        self.appointment_items_df, self.is_cancelled, self.is_gate_in = pd.DataFrame(), False, False
        appointment_items_objs = GatePassItem.objects.filter(warehouse_id=self.warehouse.id, gate_pass__gate_pass_id=self.appointment_id, gate_pass__gate_pass_type='dock-scheduling').select_related('gate_pass')
        self.appointment_items_df = pd.DataFrame(appointment_items_objs.values())
        for gate_pass_obj in list(appointment_items_objs):
            if not self.is_cancelled and gate_pass_obj.gate_pass.status in [8]:
                self.is_cancelled = True
            if not self.is_gate_in and  gate_pass_obj.gate_pass.status in [1,2]:
                self.is_gate_in = True
            self.appointment_items_df.loc[self.appointment_items_df['id'] == gate_pass_obj.id, 'object'] = gate_pass_obj


    def cancel_appointment(self):
        """
        Cancel the appointment for the current instance.

        Returns:
            appointment (int): The ID of the cancelled appointment.
            errors (list): A list of errors encountered during the cancellation process.
        """
        dock_scheduling_service_instance = DockSchedulingService(self.user, self.warehouse)
        appointment_id, errors = dock_scheduling_service_instance.cancel_appointment(self.appointment_id)
        return appointment_id, errors

    def prepare_appointment_data_for_cancel(self):
        """
        Prepare the response for the cancelled appointment.

        Returns:
            dict: The prepared response containing appointment details.
        """
        self.new_gate_pass_items, self.cancel_gate_pass_ids = [], set()
        for index, row in self.appointment_items_df.iterrows():
            gate_pass_item_obj = row['object']
            gate_pass_item_obj.status = 2
            self.appointment_items_df.loc[index, 'object'] = gate_pass_item_obj
            self.new_gate_pass_items.append(GatePassItem(**{
                "warehouse_id": row.get("warehouse_id"),
                "transaction_id": row.get("transaction_id"),
                "transaction_type": row.get("transaction_type"),
                "no_of_boxes": row.get("no_of_boxes"),
                "handling_unit": row.get("handling_unit"),
                "actual_no_of_boxes": row.get("actual_no_of_boxes"),
                "json_data": row.get("json_data"),
                "account_id": row.get("account_id"),
                "status": 1
            }))
            self.cancel_gate_pass_ids.add(row.get("gate_pass_id"))

        self.cancel_gate_pass_ids = list(self.appointment_items_df['gate_pass_id'].unique())

    def create_and_update_cancel_appointment_details(self):
        """
        Create and update the cancelled appointment details for the current instance.

        Returns:
            None
        """
        with transaction.atomic('default'):
            GatePass.objects.filter(id__in=self.cancel_gate_pass_ids).update(status=8)
            GatePassItem.objects.bulk_update(self.appointment_items_df['object'].to_list(), ['status'], batch_size=100)
            GatePassItem.objects.bulk_create(self.new_gate_pass_items, batch_size=100)
