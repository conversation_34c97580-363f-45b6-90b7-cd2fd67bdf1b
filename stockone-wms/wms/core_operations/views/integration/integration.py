#package import
from json import loads
from functools import partial
from copy import deepcopy

# django imports
from django.db import transaction

#wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger
from wms.celery import app as celery_app

#models imports
from core.models import UserIntegrationAPIS, UserIntegrationCalls

#constants import
from .constants import TPIntegration

#3p import
from .run_3pintegration import async_run_3p_int_func

log = init_logger('logs/common_integrations.log')

def webhook_integration_3p(user_id, action, filters={}, async_id=''):
    trigger_3p_integrations(user_id, action, filters=filters, async_id=async_id)

def trigger_3p_integrations(user_id, action_name, filters=None, async_id=''):
    if filters is None:
        filters = {}
    integration_values_list = ['id', 'account_id', 'user_integration_id', 'api_url', 'token_data', 'send_token', 'is_token_url', 
    'access_token', 'trigger', 'data_format', 'api_method', 'data_params', 'filters', 'retry_attempts',
    'is_async','status', 'batch_level_integration']

    """ Common 3P Ingrations """
    int_apis = list(UserIntegrationAPIS.objects.filter(user_integration__user_id=user_id, trigger=action_name, 
                                                    status=1).order_by("-is_async").values(*integration_values_list))
    for api_data in int_apis:
        try:
            if api_data.get('filters') and filters.get('zones_data',{}):
                extra_filter = loads(api_data['filters'])
                filtered_skus = []
                if extra_filter and isinstance(extra_filter, dict) and extra_filter.get("location__zone_id__in"):
                    call_3p = False
                    for zone_id, skus in filters.get('zones_data',{}).items():
                        if str(zone_id) in extra_filter.get('location__zone_id__in'):
                            filtered_skus.extend(skus)
                            call_3p = True
                    if call_3p:
                        filters['sku_codes'] = filtered_skus
                    else:
                        continue
            if api_data.get("is_async"):
                log.info("Async request params int_api_data= "+ str(api_data) +" filters= "+str(filters))
                prepare_and_trigger_3p_integration.apply_async(args=[user_id, api_data, filters, async_id])
            else:
                log.info("Sync request params int_api_data= "+ str(api_data) +" filters= "+str(filters))
                prepare_and_trigger_3p_integration(user_id, api_data, filters, async_id)
        except Exception as exception:
            import traceback
            log.debug(traceback.format_exc())
            log.info("3P Integration Calling failed for user_id "+str(user_id)+", API Url "+ str(api_data.get("api_url")) +", action_name "+ str(action_name) +", filters "+ str(filters) +" and error is " + str(exception))

@celery_app.task
def prepare_and_trigger_3p_integration(user_id, integration_apis_dict, filters, async_id=''):
    """
    3P Integration Payload Preparation
    """
    warehouse = User.objects.get(id=user_id)
    trigger_name = str(integration_apis_dict.get("data_format"))
    
    func_name = TPIntegration.DATA_FORMAT_TO_FUNCATION_MAPPING.get(trigger_name)
    api_filters = {}
    arguments = ()
    if integration_apis_dict.get("filters"):
        api_filters = loads(integration_apis_dict.get("filters"))    

    arguments = get_integration_method_arguments(warehouse, trigger_name, filters, api_filters)
    if func_name and arguments:
        send_in_data_key = arguments.pop("send_in_data_key", False)
        response_ = func_name(**arguments)
        batch_level_integration_enabled = False
        if integration_apis_dict.get("batch_level_integration"):
            batch_level_integration_enabled = True
        if response_:

            with transaction.atomic('default'):
                integration_ids = insert_user_integration_calls(warehouse, response_, integration_apis_dict.get("id"), async_id=async_id, send_in_data_key=send_in_data_key, batch_level_integration_enabled=batch_level_integration_enabled)
                filters_ ={"id__in": integration_ids}

                # async_run_3p_int_func(filters_)
                transaction.on_commit(partial(async_run_3p_int_func, filters=filters_)) # waiting for transaction to commit before calling async function

def get_integration_method_arguments(warehouse, trigger_name, filters, api_filters):
    '''
    Get the arguments for the integration method
    '''
    search_params = {}
    if trigger_name in ["inventory_callback"]:
        if not filters.get("sku_codes"):
            return {}
        if api_filters:
            search_params = api_filters
        sku_codes = filters.get("sku_codes")
        arguments = {"warehouse": warehouse, "sku_codes": sku_codes, "extra_filter": search_params, "send_in_data_key" : True}
    elif trigger_name in  ["order_callback", "order_update"]:
        if not (filters.get("original_order_ids") or filters.get("order_references")):
            return {}
        if api_filters:
            search_params = {"order_cancellation_filter": api_filters.get("order_cancellation_filter", [''])[0]}
            if api_filters.get("order__order_type__in"):
                search_params["order_type__in"] = api_filters.get("order__order_type__in")
        if filters.get('search_parameters'):
            search_params.update(filters.get('search_parameters'))
        arguments = {"warehouse": warehouse, "original_order_ids_list": filters.get("original_order_ids"), "wh_users":[warehouse.id] , "search_parameters": search_params, "order_reference_list": filters.get("order_references"), "send_in_data_key" : True}
    elif trigger_name in ["load_share", "invoice",  "grn", "invoice_callback", "shipment", "invoice_callback_with_packing", "invoice_callback_with_packing_service"]:
        if api_filters:
            filters["search_parameters"] = api_filters
        arguments = {"warehouse": warehouse, "invoice_number": filters.get("invoice_number"), "extra_args": filters}
    elif trigger_name in ["sales_return_grn_callback"]:
        grn_number = filters.get("grn_number")
        if not grn_number:
            return {}
        arguments = {"warehouse": warehouse, "grn_numbers": [grn_number]}
    elif trigger_name in ["po_callback"]:
        po_number = filters.get("po_numbers")
        if not po_number:
            return {}
        arguments = {"warehouse": warehouse, "po_numbers": po_number}
    elif trigger_name in ["rtv_callback"]:
        rtv_number = filters.get("rtv_numbers")
        if not rtv_number:
            return {}
        arguments = {"warehouse": warehouse, "rtv_number": rtv_number}
    elif trigger_name in ["sales_return_callback", "sales_return_cancellation"]:
        return_id = filters.get("return_id")
        if not return_id:
            return {}
        arguments = {"warehouse": warehouse, "return_id" : return_id, "send_in_data_key" : True}
    elif trigger_name in ['asn_callback']:
        if not filters.get("asn_number"):
            return {}
        if api_filters:
            filters.update(**api_filters)
        arguments = {"warehouse": warehouse, "request_data" : filters}
    else:
        if api_filters:
            filters["search_parameters"] = api_filters
        arguments = {"warehouse":warehouse, "filters": filters}
        if trigger_name in ["picklist_detail_with_zero_quantity"]:
            filters_copy = deepcopy(filters)
            filters_copy["execute_zero_quantity"] = True
            arguments["filters"] = filters_copy
    return arguments

def insert_user_integration_calls(user, integration_data, integration_api_id, async_id='', send_in_data_key=False, batch_level_integration_enabled=False):
    log.info(str(integration_data))
    new_created_ids = []
    if batch_level_integration_enabled:
        if send_in_data_key:
            data = {
                "warehouse" : user.username,
                "data": integration_data
            }
        else:
            data = integration_data
        if async_id:
            data = {
                'async_id': async_id,
                'data': integration_data
            }
        created_obj = UserIntegrationCalls.objects.create(user_integrationapis_id=integration_api_id,
                                                        api_data=data,
                                                        status=1, account_id=user.userprofile.id)
        if created_obj:
            new_created_ids.append(created_obj.id)
    else:
        for each_int in integration_data:
            if send_in_data_key:
                data = {
                    "warehouse" : user.username,
                    "data": each_int
                }
            else:
                data = each_int
            if async_id:
                data = {
                    'async_id': async_id,
                    'data': each_int
                }
            created_obj = UserIntegrationCalls.objects.create(user_integrationapis_id=integration_api_id,
                                                        api_data=data,
                                                        status=1, account_id=user.userprofile.id)
            if created_obj:
                new_created_ids.append(created_obj.id)
    return new_created_ids