from django.urls import include, re_path, path

from core_operations.views.masters import (
    sku_master, tax_master, carton_master,
    user_master, uom_master, terms_and_conditions
)

from core_operations.views.common import (
    datatable, main, user_attributes,
    configurations, action_trigger, upload, re_auth, labels, temp_json
)

from core_operations.views.configurations import cluster_picking_config
from core_operations.views.configurations import user_zone_config

from core_operations.views.masters.currency_exchange_master import (
    insert_currency_exchange, get_currency_exchange_mapping_data,
    get_currency_exchange_rate
)

# integration views
from core_operations.views.integration import (
    user_integration,
    main as integration,channels
)

from core_operations.views.integration.invoice_data import process_file_with_stockai

from core_operations.views.common.user_profile import UserProfileViewSet, UserAddressesViewSet, WarehouseConfigViewSet
from core_operations.views.common.datatable import MasterHeadersViewSet
from core_operations.views.common.download_documents import DocumentsMixin
from core_operations.views.common.hub_spoke_detail import HubSpokeDetailSet, SpokeTriggersDetailSet


from core_operations.views.transaction.material_view import MaterialTransactionView, MaterialTransactionReport
from core_operations.views.common.labels import LabelConfigViewSet, LabelDataPrintView, LabelDataStatusUpdateView
from core_operations.views.weights.weights_view import WeightingMachineListCreateView, WeightingMachineRetrieveUpdateView
from core_operations.views.celeryresults.taskresults import CeleryResultAPIView
from core_operations.views.common.email_config import EmailConfigRetrieveUpdateView, EmailConfigListCreateView
from core_operations.views.common.async_api import get_async_api_status
from core_operations.views.common.company_level_configs import CompanyLevelConfigsListCreateView, CompanyLevelConfigsRetrieveUpdateView

#gate management
from core_operations.views.gate_management.gate import GateView
from core_operations.views.gate_management.gate_pass import GatePassView, GatePassItemView
from core_operations.views.gate_management.datatable import get_available_gates
from core_operations.views.common.application_logs import GetLogsView

#dock scheduling
from core_operations.views.dock_scheduling.dock_preview import DockPreview
from core_operations.views.dock_scheduling.appointment import AppointmentSet
from core_operations.views.services.service_authentication import ServiceAuthentication


from rest_framework import routers

#custom urls
from wms_base.permissions.path_validators import path_with_permissions, re_path_with_permissions

#integration urls
router = routers.DefaultRouter()
router.register('user_integrations', user_integration.UserIntegrationsViewSet, basename='User Integrations')
router.register('user_integrations_apis', user_integration.UserIntegrationsAPISViewSet, basename='User Integrations APIs')
router.register('user_integrations_calls', user_integration.UserIntegrationCallsViewSet, basename='User Integrations Calls')
router.register('user_profile', UserProfileViewSet, basename='UserProfile')
router.register('user_address', UserAddressesViewSet, basename='User Addresses')
router.register('warehouse_config', WarehouseConfigViewSet, basename='Warehouse Configs')
router.register('master_headers', MasterHeadersViewSet, basename='DataTable Columns Configuration')
router.register('label', LabelConfigViewSet, basename='Label Configuration')
router.register('hub_spoke_detail', HubSpokeDetailSet, basename='Hub Spoke Detail')
router.register('spoke_triggers_detail', SpokeTriggersDetailSet, basename='Spoke Triggers Detail')

#uploads
router.register('upload', upload.UploadsViewSet, basename='Upload')
router.register('upload-format', upload.UploadFormatViewSet, basename='Upload Download Format')

#temp json
router.register('temp_json', temp_json.TempJsonSet, basename='Temp Json')

urlpatterns = [
    re_path('', include(router.urls)),
    path('get_logs/', GetLogsView.as_view(), name='get_logs'),
    re_path('get_billing_details/$', main.get_billing_details, name="Billing Details"),
    #result data
    re_path('results_data/$', datatable.results_data, name="Datatable View"),
    re_path('check_version_number/', main.check_version_number, name="Git Version"),
    re_path('delete_temp_json/$', main.delete_temp_json, name="TempJson Deletion"),
    re_path('get_format_types/', main.get_format_types, name="Barcode Format Types"),
    re_path('get_uom_list/$', main.get_uom_types_list, name="UOM Types List"),

    #sku master
    re_path('sku/$', sku_master.insert_sku, name="SKU Creation"),
    re_path('search_wms_codes/$', main.search_wms_codes, name="SKUs Codes Details"),
    re_path('search_wms_data/$', main.search_wms_data, name="SKUs Data Details"),
    re_path('update_sku/$', sku_master.update_sku, name="SKU Updation"),
    re_path('get_sku_data/$', sku_master.get_sku_data, name="SKU Details"),
    re_path('get_sku_list/$', main.get_sku_list, name="SKUs List"),
    re_path('check_sku/$', sku_master.check_sku, name="SKU Status"),
    re_path('get_po_choices/$', sku_master.get_po_choices, name="Purchase Order Choices"),
    re_path('get_sku_mrp/$', main.get_sku_mrp, name="SKU MRP View"),
    re_path('products/$', sku_master.ItemsDetailsSet.as_view(), name="SKU"),
    re_path('get_sku_details_for_ean_number/$', main.get_sku_details_for_ean_number, name="EanNumber SKU Details View"),
    re_path('get_category_and_location_type_data/$', main.get_category_and_location_type_data, name="SKU Code Details View"),

    #CurrencyExchange Master
    re_path('currency_exchange/$', insert_currency_exchange, name="Currency Exchange Master Creation"),
    re_path('get_currency_exchange/$', get_currency_exchange_mapping_data, name="Currency Exchange MappingData View"),
    re_path('get_currency_exchange_rate/$', get_currency_exchange_rate, name="Currency Exchange Rate View"),
    re_path('insert_currency_exchange/$', insert_currency_exchange, name="Currency Exchnage Updation"),

    #ApprovalConfig Master
    re_path('approval_config/$', main.new_add_update_po_approval_config, name="ApprovalConfig Creation"),
    re_path('get_approval_config/$', main.new_get_purchase_config_data, name="ApprovalConfig View"),
    re_path('get_approval_users/$', main.get_approval_users, name="Approval Users View"),
    
    #user attributes
    re_path('get_users/$', main.get_users, name="Users Details"),
    re_path('get_user_attributes/$', main.get_user_attributes_list, name="User Attributes View"),
    re_path('incremental_number/$', main.get_incremental_number, name="Incremental Number"),

    #Script API
    re_path('dynamic_script_api/$', main.dynamic_script_api, name="Dynamic Script API"),

    #tax master
    re_path('add_or_update_tax/$', tax_master.add_or_update_tax, name="Tax Master Creation"),
    re_path('get_tax_data/$', tax_master.get_tax_data, name="Tax Master Information View"),
    re_path('tax/$', tax_master.TaxSet.as_view(), name="Tax"),

    #carton master
    re_path('carton/$', carton_master.BinDetialsSet.as_view(), name="LPN Creation"),
    re_path('carton_type/$', carton_master.create_carton_type, name="LPN Format Types Creation"),
    re_path('get_carton_types/$', carton_master.get_carton_types, name="LPN Format Types View"),
    re_path('get_available_cartons/$', main.get_available_cartons, name="Available LPN Types View"),

    #user master
    re_path_with_permissions('user/$', user_master.UserView.as_view(), name="User Access Control Master Creation", permissions={'POST': 'add_user'}),
    re_path_with_permissions('update_staff_values/$', user_master.update_staff_values, name="User Access Control Master Fields Updation", permissions={'POST': 'change_user'}),
    re_path_with_permissions('update_user_status/$', user_master.update_user_status, name="User Status Updation", permissions={'POST': 'change_user'}),
    re_path_with_permissions('update_user/$', user_master.update_staff_values, name="User Updation", permissions={'POST': 'change_user'}),
    re_path('get_warehouse_list/$', main.get_warehouse_list, name="Warehouse List Access"),
    re_path_with_permissions('get_user_groups_list/$', main.get_user_roles_list, name="User Groups List Access", permissions={'GET': 'view_role'}),
    re_path_with_permissions('get_user_roles/$', main.get_user_roles, name="User Roles List Access", permissions={'GET': 'view_role'}),
    re_path_with_permissions('get_company_roles/$', main.get_company_roles, name="Company Roles List Access", permissions={'GET': 'view_role'}),

    #user attributes
    re_path('user_attributes/$', user_attributes.save_update_attribute, name="User Attributes Creation"), 
    re_path('checklist_options/$', user_attributes.checklist_options, name="Checklist Options"),
    re_path('checklist_names/$', user_attributes.checklist_names, name="Checklist Names"),
    re_path('checklist/$', user_attributes.FormFields.as_view(), name="Checklist"),
    re_path('delete_user_attribute/$', main.delete_user_attribute, name="User Attributes Deletion"),
    re_path('get_warehouses/$', main.get_warehouses, name="Warehouses View"),
    re_path('get/user/with/permission/$',main.get_all_wh_user_with_permission, name="User Permission View"),
    
    #terms and conditions
    re_path('terms_and_conditions/$', terms_and_conditions.TermsAndConditionSet.as_view(), name="Terms and Conditions"),

    #configurations
    re_path('configurations/$', configurations.configurations, name="Configurations View"),
    re_path('save_misc_json/$', configurations.save_misc_json, name="Misc Json Creation"),
    re_path('save_config_extra_fields/$', configurations.save_config_extra_fields, name="Configuration Fields Creation"),
    re_path('config_fields/$', configurations.ConfigFields.as_view(), name="Configurations Fields View"),
    re_path('switches/$', configurations.switches, name="Switches View"),
    re_path('order_management_toggle/$', main.order_management_toggle, name="OrderManagementToggle Creation"),
    re_path('save_groups/$', configurations.save_groups, name="SKU Groups Creation"),
    re_path('save_order_extra_fields/$', configurations.save_order_extra_fields, name="Order Extra Fields Creation"),
    re_path('save_return_types/$', configurations.save_return_types, name="Save Return Types"),
    re_path('send_mail_reports/$', configurations.send_mail_reports, name="Send Mail Reports"),
    re_path('update_mail_configuration/$', configurations.update_mail_configuration, name="Mail Configuration Updation"),
    re_path('save_stages/$', configurations.save_stages, name="Production Stages Creation"),
    re_path('save_misc_detail_options/$', configurations.save_misc_detail_options, name="Configuration Updation"),
    re_path('save_multiple_misc_options/$', configurations.save_multiple_misc_options, name="Dropdown Configuration"),
    re_path('fetch_multiple_misc_options/$', configurations.fetch_multiple_misc_options, name="Dropdown Configuration View"),
    re_path('misc_options/$', configurations.MiscOptions.as_view(), name="COnfigurations Creation"),
    re_path('save_order_sku_extra_fields/$', configurations.save_order_sku_extra_fields, name="Order Extra Fields Updation"),
    re_path('save_extra_order_options/$', configurations.save_extra_order_options, name="Extra Order Options Updation"),
    re_path('enable_mail_reports/$', configurations.enable_mail_reports, name="Mail Reports Configuration Creation"),
    re_path('action_trigger/$',action_trigger.ActionTriggerView.as_view(), name="ActionTrigger View"),
    re_path('named_urls/$',re_auth.NamedUrls.as_view(), name="URLs View"),
    re_path('get_categories_list/$', main.get_categories_list, name="Categories List View"),
    re_path('get_department_list/$', main.get_department_list, name="Department List View"),
    re_path('get_company_warehouses/$', main.get_company_warehouses, name="Company Warehouses View"),
    re_path('get_sequences/$',main.get_sequence, name="Warehouse Sequence"),
    re_path('update_sequence/$', main.update_sequence, name="Sequence Updation"),
    re_path('bulk_files_upload/$', main.bulk_files_upload, name="Bulk Files Upload Creation"),
    re_path('save_grn_weighing_options/$', configurations.save_grn_weighing_options, name="GRN Weighing Options"),
    re_path('sla/$', configurations.SlaConfig.as_view(), name="SLA Detail Config"),
    re_path('putaway_config/$', configurations.putaway_config, name="Putaway Config"),
    re_path('wave_picking/config/$',configurations.Wavepicking.as_view(), name="Wave Picking Config"),
    re_path('clusterpicking_config/$', cluster_picking_config.ClusterConfig.as_view(), name="Cluster Config"),
    re_path('user_zone_assignment/config/$', user_zone_config.UserZoneAssignementConfig.as_view(), name="User Zone Config"),
    re_path('get_supplier_csv_mapping_details/$',configurations.get_supplier_csv_mapping_details, name='Spplier CSV Mappings'),
    re_path('validate_availble_misc_data/',configurations.validate_availble_misc_data, name='Spplier CSV Mappings'),
    re_path('incremental_config/$',configurations.IncrementalConfig.as_view(), name='Incremental Config'),

    #user integrations
    re_path('view_integration_call_data/$',integration.view_integration_call_data, name="IntegrationCall Data View"),
    re_path('create_update_integration_call/$',integration.create_update_integration_call, name="IntegrationCall Updation"),
    re_path('execute_3p_integration/$',integration.execute_3p_integration, name="Execute 3p Integration"),
    re_path('get_integration_warehouse_list/$', integration.get_warehouse_list_for_integration, name="Integration Warehouse List View"),
    
    #async api
    re_path('api_status/$', get_async_api_status, name="Async API Status"),

    path('v2/constants/',integration.IntegrationTriggerActionSet.as_view(), name="Integrtion Constants"),
    path('v2/constants/<str:primary_string>/',integration.IntegrationTriggerActionSet.as_view(), name="3p Integrtions Constants With Param1"),
    path('v2/constants/<str:primary_string>/<str:secondary_string>/',integration.IntegrationTriggerActionSet.as_view(), name="3p Integrtion Constants With Param2"),
    path('v2/constants/<str:primary_string>/<str:secondary_string>/<str:third_string>/',integration.IntegrationTriggerActionSet.as_view(), name="3p Integrtions Constants With Param3"),

    #channels
    re_path('link/$', integration.Link.as_view(), name="Link"),
    
    #3pchannels
    re_path('channels/$', channels.ChannelIntegrations.as_view(), name="Channels"),

    #uom master
    re_path('uom/$', uom_master.UOMDetailSet.as_view(), name="UOM View"),

    #download documents
    re_path('documents/$', DocumentsMixin.as_view(), name='Download Documents'),

    #transaction view
    re_path('transaction_view/material/$', MaterialTransactionView.as_view(), name='Material TransactionView'),
    re_path('material_transaction_report/$', MaterialTransactionReport.as_view(), name="Material Transaction Report"),

    #labels
    path('labeldata/', LabelDataPrintView.as_view(), name='Labeldata Print'),
    path('labeldata/update-status/', LabelDataStatusUpdateView.as_view(), name='labeldata-update-status'),
    path('labeldata/<int:id>/', LabelDataPrintView.as_view(), name='Labeldata Detail'),

    #weights
    path('weighing-machines/', WeightingMachineListCreateView.as_view(), name='weighing-machine-list-create'),
    path('weighing-machines/<int:pk>/', WeightingMachineRetrieveUpdateView.as_view(), name='weighing-machine-retrieve-update'),

    #celery
    path('celery-results/', CeleryResultAPIView.as_view(), name='celery-results'),
    path('celery-results/<str:task_id>/', CeleryResultAPIView.as_view(), name='celery-result-detail'),

    #emailconfig
    path('emailconfig/', EmailConfigListCreateView.as_view(), name='email-list-create'),
    path('emailconfig/<int:pk>/', EmailConfigRetrieveUpdateView.as_view(), name='email-retrieve-update'),

    #gate management
    re_path('gate/$', GateView.as_view(), name="Gate Creation"),
    re_path('gate_pass/$', GatePassView.as_view(), name="Gate Pass Creation"),
    re_path('gate_pass_item/$', GatePassItemView.as_view(), name="Gate Pass Item Creation"),
    re_path('get_available_gates/$', get_available_gates, name="Get Available Gates"),

    #dock scheduling
    re_path('dock_preview/$', DockPreview.as_view(), name="Dock Preview"),
    re_path('appointment/$', AppointmentSet.as_view(), name="Appointment"),
    re_path('service_authentication/$', ServiceAuthentication.as_view(), name="Service Authentication"),

    # merge pdf
    path('merge_pdf_urls/', main.merge_pdf_urls, name='Merge PDF URLs'),

    # Companylevel Configurations
    path_with_permissions('company-level-configs/', CompanyLevelConfigsListCreateView.as_view(), name='company-level-configs-list-create', permissions={'POST': 'add_companylevelconfigs', "GET": "view_companylevelconfigs"}),
    path_with_permissions('company-level-configs/<int:pk>/', CompanyLevelConfigsRetrieveUpdateView.as_view(), name='company-level-configs-retrieve-update', permissions={'PUT': 'change_companylevelconfigs', "GET": "view_companylevelconfigs"}),

    re_path('process-file/$', process_file_with_stockai, name="Process File"),


]