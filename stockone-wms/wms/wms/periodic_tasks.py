from wms.celery import app

from django.core import management
from datetime import datetime

from wms_base.wms_utils import init_logger
log = init_logger('logs/periodic_tasks.log')


def execute_command_with_logging(task_name: str, command: str, extra_params: dict = None):
    """Helper function to run a Django management command with logging."""
    start_time = datetime.now().isoformat()
    log.info(f"{task_name} started at {start_time}")
    try:
        if extra_params:
            management.call_command(command, extra_params=str(extra_params))
        else:
            management.call_command(command)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.error(f"Error in {task_name}: {e}")

    end_time = datetime.now().isoformat()
    log.info(f"{task_name} completed at {end_time}")

@app.task
def load_closing_stock():
    """Load closing stock for the day."""
    execute_command_with_logging("load_closing_stock", "load_closing_stock")

@app.task
def async_uploads():
    """ Execute async uploads. """
    execute_command_with_logging("async_uploads", "execute_uploads")

@app.task
def scheduled_cycle_count():
    """ Execute scheduled cycle count. """
    execute_command_with_logging("scheduled_cycle_count", "scheduled_cycle_count")

@app.task
def inventory_pending_approval(to_username: str, warehouse_id: int):
    """ Send pending inventory approval mail. """
    extra_params = {
        'to_user': to_username,
        'warehouse_id': warehouse_id
    }
    execute_command_with_logging("inventory_pending_approval", "inventory_pending_approval", extra_params=extra_params)

@app.task
def sku_wac_updation(warehouse_usernames: list):
    """ Update SKU WAC for the given warehouses. """
    extra_params = {
        'warehouses': warehouse_usernames
    }
    execute_command_with_logging("sku_wac_updation", "sku_wac_updation", extra_params=extra_params)

@app.task
def cancel_expired_orders():
    """ Cancel expired orders. """
    execute_command_with_logging("cancel_expired_orders", "cancel_open_expired_orders")

@app.task
def auto_po_cancellation():
    """ Automatically cancel purchase orders based on expected delivery date. """
    extra_params = {
        'warehouses': []
    }
    execute_command_with_logging("auto_po_cancellation", "auto_po_cancellation", extra_params=extra_params)

@app.task
def merge_stock_id_sale_uom(warehouse_usernames: list):
    """ Merge stock IDs and sale UOM for the given warehouses. """
    extra_params = {
        'warehouses': warehouse_usernames
    }
    execute_command_with_logging("merge_stock_id_sale_uom", "merge_stock_id_sale_uom", extra_params=extra_params)

@app.task
def data_syncer():
    """ Sync data for the given warehouses. """
    extra_params = {
        'warehouses': []
    }
    execute_command_with_logging("data_syncer", "data_syncer", extra_params=extra_params)

@app.task
def ars_stock_transfer_orders_from_central_wh_to_stores(extra_params: dict = {}):
    execute_command_with_logging("ars_stock_transfer_orders", "ars_stock_transfer_orders", extra_params=extra_params)