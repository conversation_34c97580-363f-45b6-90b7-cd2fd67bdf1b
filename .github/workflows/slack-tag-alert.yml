name: slack-tag-events

on:
  create:
    tags:
      - "v*" # Trigger only when a tag starting with "v" is created
  delete:
    tags:
      - "v*" # Trigger only when a tag starting with "v" is deleted

jobs:
  slack:
    runs-on: ubuntu-latest
    steps:
      - name: Send Slack Notification
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_HOOK_URL }}
        run: |
          # Extract event data
          EVENT_TYPE="${{ github.event_name }}"
          REF="${{ github.event.ref }}" # Works for both create and delete
          ACTOR="${{ github.actor }}"
          REPO_NAME="${{ github.repository }}"
          REPO_URL="https://github.com/${{ github.repository }}"
          TAG_URL="https://github.com/${{ github.repository }}/releases/tag/${REF}"
          TIMESTAMP=$(date -u)

          # Validate the tag name starts with "v" (additional safety check)
          if [[ ! "$REF" =~ ^v ]]; then
            echo "Tag does not start with 'v'. Exiting."
            exit 0
          fi

          # Determine action type
          if [[ "$EVENT_TYPE" == "create" ]]; then
            ACTION="Created"
          elif [[ "$EVENT_TYPE" == "delete" ]]; then
            ACTION="Deleted"
          fi

          # Prepare the message blocks
          SLACK_MESSAGE="{
            \"blocks\": [
              {
                \"type\": \"header\",
                \"text\": {
                  \"type\": \"plain_text\",
                  \"text\": \"Tag Event: $ACTION\",
                  \"emoji\": true
                }
              },
              {
                \"type\": \"section\",
                \"fields\": [
                  {
                    \"type\": \"mrkdwn\",
                    \"text\": \"*Tag Name:* <$TAG_URL|$REF>\"
                  },
                  {
                    \"type\": \"mrkdwn\",
                    \"text\": \"*Action:* $ACTION\"
                  },
                  {
                    \"type\": \"mrkdwn\",
                    \"text\": \"*User:* $ACTOR\"
                  },
                  {
                    \"type\": \"mrkdwn\",
                    \"text\": \"*Repository:* <$REPO_URL|$REPO_NAME>\"
                  },
                  {
                    \"type\": \"mrkdwn\",
                    \"text\": \"*Timestamp:* $TIMESTAMP\"
                  }
                ]
              }
            ]
          }"

          # Send the message to Slack
          curl -X POST \
            -H 'Content-type: application/json; charset=utf-8' \
            --data "$SLACK_MESSAGE" \
          $SLACK_WEBHOOK_URL
