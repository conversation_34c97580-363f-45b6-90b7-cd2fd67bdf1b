name: slack-pull-request
on:
  pull_request:
    branches:    
      - main

jobs:
  slack:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run script file
        run: |
            curl -X POST \
            -H 'Content-type: application/json; charset=utf-8' \
            --data '{
                  "blocks": [
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": "New Pull Request",
                        "emoji": true
                      }
                    },
                    {
                      "type": "section",
                      "fields": [
                        {
                          "type": "mrkdwn",
                          "text": "*Repository:*\n${{ github.event.repository.name }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Created by:*\n${{ github.event.pull_request.user.login }}"
                        }
                      ]
                    },
                    {
                      "type": "section",
                      "text": {
                        "type": "mrkdwn",
                        "text": "${{ github.event.pull_request.title }}"
                      },
                      "accessory": {
                        "type": "button",
                        "text": {
                          "type": "plain_text",
                          "text": "Open",
                          "emoji": true
                        },
                        "value": "click_me_123",
                        "url": "${{ github.event.pull_request.html_url }}",
                        "action_id": "button-action"
                      }
                    }
                  ]
                }' \
            ${{ secrets.SLACK_HOOK_URL }}
        shell: bash