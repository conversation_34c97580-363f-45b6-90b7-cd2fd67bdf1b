name: chat-push-notification
on:
  pull_request:
    types:
      - closed
    branches:
      - main
      
jobs:
  chat:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - uses: Co-qn/google-chat-notification@releases/v1
        with:
          name: Merge
          url: ${{ secrets.GOOGLE_CHAT_PULL_REQUEST_WEBHOOK_URL }}
          status: ${{ job.status }}
