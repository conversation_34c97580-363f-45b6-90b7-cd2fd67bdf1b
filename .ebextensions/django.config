container_commands:
  #01_makemigrations:
  #  command: "$PYTHONPATH/python stockone-wms/wms/manage.py makemigrations"
  #02_migrate:
  #  command: "$PYTHONPATH/python stockone-wms/wms/manage.py migrate --database=default"
  #03_migrate:
  #  command: "$PYTHONPATH/python stockone-wms/wms/manage.py migrate --database=reversion"
  #04_create_superuser:
  #  command: "$PYTHONPATH/python stockone-wms/wms/manage.py createsu"
  07_collectstatic:
    command: "$PYTHONPATH/python stockone-wms/wms/manage.py collectstatic --noinput --verbosity=0 --clear"

option_settings:
  aws:elasticbeanstalk:container:python:
    WSGIPath: wms.wsgi:application
  aws:elasticbeanstalk:application:environment:
    AWS_ID: ********************
    AWS_KEY: FuaOm7VAN6BnJQ7IW9cljyBdJHuHs+XZo/Dh9ICy
    AWS_STORAGE_BUCKET_NAME: stockone-prod1
    DB_HOST: stockone-1.cymdh4wcjpto.ap-south-1.rds.amazonaws.com
    DB_REV_HOST: stockone-1.cymdh4wcjpto.ap-south-1.rds.amazonaws.com
    DB_REPORTS_HOST: stockone-1.cymdh4wcjpto.ap-south-1.rds.amazonaws.com
    DB_NAME: stockone
    DB_USER: stockone
    DB_PASS: stockone^123
    DB_PORT: 3306
    DB_REV_NAME: stockone_reversion
    DB_REV_USER: stockone
    DB_REV_PASS: stockone^123
    DB_REV_PORT: 3306
    DB_REPORTS_NAME: stockone
    DB_REPORTS_USER: stockone
    DB_REPORTS_PASS: stockone^123
    DB_REPORTS_PORT: 3306
    DEBUG: 0
    LOG_GROUP_NAME: stockone
    REDIS_URL: redis://stockone-prod.mmlqwy.0001.aps1.cache.amazonaws.com:6379
    USE_CLOUDWATCH: 'TRUE'
    USE_S3: 'TRUE'


