container_commands:
    01_create_celery_log_file_directories:
      command: mkdir -p /var/log/celery /var/run/celery
    02_add_init_celeryd:
      command: cp .ebextensions/files/init-celeryd /etc/init.d/celeryd
    03_add_default_celeryd:
      command: cp .ebextensions/files/default-celeryd /etc/default/celeryd
    04_give_celery_user_ownership_of_directories:
        command: chown -R root:root /var/log/celery /var/run/celery && chown root:root /etc/init.d/celeryd
    05_change_mode_of_celery_directories:
        command: chmod -R 755 /var/log/celery /var/run/celery /etc/init.d/celeryd
    06_reload_settings:
        command: systemctl daemon-reload
    07_start_celery:
        command: /etc/init.d/celeryd restart
