#! /bin/sh

# 0  default param
# 1  github token
# 2  target commitish


#get sonar analysis url
sonar_report="https://sonarqube.stockone.com/dashboard?branch=$2&id=shipsy_stockone-neo_AYz3fsV0X8UvWsq_z-So"

#integration testing url
integration_testing_report=""

#stack hawk analysis report url
stack_hawk_report=""


#getting the latest tag
tag_details=$(curl --location 'https://api.github.com/repos/shipsy/stockone-neo/tags' \
--header 'Accept: application/vnd.github+json' \
--header "Authorization: Bearer $1" \
--header 'X-GitHub-Api-Version: 2022-11-28')

latest_tag=$(echo $tag_details | jq -r '.[0].name')

#getting release notes
release_details=$(curl --location 'https://api.github.com/repos/shipsy/stockone-neo/releases/generate-notes' \
--header 'Accept: application/vnd.github+json' \
--header "Authorization: Bearer $1" \
--header 'X-GitHub-Api-Version: 2022-11-28' \
--header 'Content-Type: application/json' \
--data '{
    "tag_name": "vtest",
    "target_commitish": "'"$2"'",
    "previous_tag_name": "'"$latest_tag"'"
}')

release_notes=$(echo $release_details | jq -r '.body')


#slack release request
slack_request=$(curl --location 'https://hooks.slack.com/triggers/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' \
--header 'Content-Type: application/json' \
--data '{
  "release_notes": "'"$release_notes"'",
  "release_type": "'"$2"'",
  "sonarquebe_report": "'"$sonar_report"'",
  "testing_report": "'"$integration_testing_report"'",
  "stackhawk_report": "'"$stack_hawk_report"'"
}')
