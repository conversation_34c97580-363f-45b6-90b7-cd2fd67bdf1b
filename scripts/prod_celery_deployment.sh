#! /bin/sh

#0 default param
#1 Deployment Machine Ip
#2 Deployment User
#3 Deployment Path
#4 Deployment Branch
#5 Filename
#6 aws account
#7 aws region
#8 aws profile
#9 aws image url

echo  $0 $1 $2 $3 $#

ips=$(echo $1 | tr "," "\n")

echo $ips
for addr in $ips;
do
  printf "========== Deploying $5 in $addr ============\n"
  printf "========== 1 is  $1  ==============\n"
  printf "========== 2 is  $2  ==============\n"
  printf "========== 3 is  $3  ==============\n"
  printf "========== 4 is  $4  ==============\n"
  printf "========== 5 is  $5  ==============\n"

  # Deploying new version
  ssh $2@$addr <<EOF
    cd $3
    ls
    pwd
    echo "============== ENV ========"
    echo "version number $4"
    export IMAGE_URL=$9
    printenv

    echo "============== AWS LOGIN ========"
    aws ecr get-login-password --region $7 --profile $8 | docker login --username AWS --password-stdin $6.dkr.ecr.$7.amazonaws.com

    echo "============== DOCKER CONFIG ========"
    docker-compose -f $5 config

    echo "============== DOCKER DOWN ========"
    docker-compose -f $5 down

    echo "============== DOCKER REBUILD ========"
    docker-compose -f $5 up -d --build

    echo "============== DOCKER LOGOUT ========"
    docker logout

    echo "============== DEPLOYMENT COMPLETED ========"
    exit
EOF
done