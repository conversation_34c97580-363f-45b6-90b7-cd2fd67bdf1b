#/bin/bash

# 0 default param
# 1 taskname
# 2 servicename
# 3 image name
# 4 cluster name
# 5 aws region
# 6 healthcheck
# 7 aws profile
# 8 current version

set -e

print_unhealthy() {
    echo -e "\e[91m❌ Unhealthy\e[0m"  # Print "❌ Unhealthy" in red text
}

print_healthy() {
    echo -e "\e[92m✅ HEALTHY\e[0m"  # Print "✅ HEALTHY" in green text
}

TASK_NAME=$1
SERVICE_NAME=$2
IMAGE_NAME=$3
CLUSTER_NAME=$4
REGION=$5
HEALTH_CHECK=$6
PROFILE=$7
CURRENT_VERSION=$8

# getting the latest task revision
TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_NAME" --region "$REGION" --profile "$PROFILE")
echo "========= Got Latest Task Definition ========="

#preparing new task revision with new image
IMAGE_NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$IMAGE_NAME" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
echo "========= New task definition prepared ========="

# adding or updating CURRENT_VERSION in environment
NEW_TASK_DEFINITION=$(echo "$IMAGE_NEW_TASK_DEFINITION" | jq --arg CURRENT_VERSION "$CURRENT_VERSION" '
  .containerDefinitions[0].environment = (
    (.containerDefinitions[0].environment // [])
    | map(select(type == "object"))
    | map(
        if .name == "CURRENT_VERSION" 
        then .value = $CURRENT_VERSION 
        else . 
        end
      )
    | if any(.name == "CURRENT_VERSION") 
      then . 
      else . + [{ "name": "CURRENT_VERSION", "value": $CURRENT_VERSION }] 
      end
  )
')
echo "========= Added/Updated CURRENT_VERSION in environment ========="

#creating new task revision
NEW_REVISION=$(aws ecs register-task-definition --region "$REGION" --cli-input-json "$NEW_TASK_DEFINITION" --profile "$PROFILE")
NEW_REVISION_DATA=$(echo $NEW_REVISION | jq '.taskDefinition.revision')
echo "========= registered new task revision ========="

#updating service with new task revision
NEW_SERVICE=$(aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --task-definition $TASK_NAME --region $REGION --force-new-deployment --profile "$PROFILE")
echo "========= Updated service for Deployment ========="

# Waiting for the deployment to complete
echo "Waiting for the deployment to complete..."
aws ecs wait services-stable --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $REGION --profile "$PROFILE"

if [ "$HEALTH_CHECK" = "true" ]; then
    # Checking task health
    TASK_ARN=$(aws ecs list-tasks --cluster $CLUSTER_NAME --service-name $SERVICE_NAME --region $REGION --profile "$PROFILE" | jq -r '.taskArns[0]')
    TASK_HEALTH=$(aws ecs describe-tasks --cluster $CLUSTER_NAME --tasks $TASK_ARN --region $REGION --profile "$PROFILE" | jq -r '.tasks[0].healthStatus')

    if [ "$TASK_HEALTH" = "HEALTHY" ]; then
        echo "Task is healthy."
        print_healthy
    else
        echo "Task is not healthy"
        print_unhealthy
        exit 1
    fi

    # Checking target group associations
    TARGET_GROUP_ARN=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $REGION  --profile "$PROFILE" | jq -r '.services[0].loadBalancers[0].targetGroupArn')

    if [ -n "$TARGET_GROUP_ARN" ]; then
        echo "Task is associated with Target Group: $TARGET_GROUP_ARN"
    else
        echo "Task is not associated with any Target Group."
        exit 1
    fi
    #healthcheck completed

else
        echo "service dont have healthchecks"
    fi

#finish deployment
echo "Deployment completed successfully. ${TASK_NAME}, Revision: ${NEW_REVISION_DATA}"

#echo "${TASK_DEFINITION}"
#echo "${NEW_TASK_DEFINITION}"
#echo "${NEW_REVISION}"
#echo "${NEW_REVISION_DATA}"
