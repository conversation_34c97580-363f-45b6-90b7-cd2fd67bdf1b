#! /bin/sh

# 0  default param
# 1  devrev issue id
# 2  devrev token
# 3  deployed url
# 4  pull url
# 5  github token


# get issue objectid
issue_details=$(curl --location --request POST "https://api.devrev.ai/works.get?id=$1" \
            --header "Authorization: $2"
)

# Extracting ID from the response
issue_id=$(echo "$issue_details" | jq -r '.work.id')
issue_comment=$(curl --location --silent 'https://api.devrev.ai/timeline-entries.create' \
--header 'Content-Type: application/json' \
--header "Authorization: $2" \
--data '{
    "body": "Deployed URL '"$3"'",
    "body_type": "text", 
    "labels": [
        "display:discussions"
    ],
    "object": "'"$issue_id"'",
    "type": "timeline_comment",
    "visibility": "internal"
}')

issue_custom_field=$(curl --location --silent 'https://app.devrev.ai/api/gateway/internal/works.update' \
--header 'Content-Type: application/json' \
--header "Authorization: $2" \
--data '{
    "tnt__repo_name": "stockone-neo",
    "tnt__github_pr": '"$4"' , 
    "id": "'"$issue_id"'", 
    "type": "issue",
    "custom_schema_fragments": [
        "don:core:dvrv-us-1:devo/ZsM1nPvA:tenant_fragment/3"
    ]
}')


issue_title=$(echo "$issue_details" | jq -r '.work.title')
if [ "${issue_title}" = "null" ]
then
    issue_title="Devrev Issue Not Linked to the Branch, Check Branch Naming Convention"
fi


#updating pullrequest title with devrev issue name
update_pull_title=$(curl --location --request PATCH "https://api.github.com/repos/shipsy/stockone-neo/pulls/$4" \
--header "Accept: application/vnd.github+json" \
--header "Authorization: Bearer $5" \
--header "X-GitHub-Api-Version: 2022-11-28" \
--header "Content-Type: application/json" \
--data-raw "{
    \"title\": \"$issue_title\"
}")

echo -n "${issue_title}"