#! /bin/sh

# 0  default param
# 1  clickup taskid
# 2  clickup token
# 3  deployed url
# 4  pull url
# 5  github token


#commenting pullrequest url in clickup task
pull_url=$(curl -s --location --request POST "https://api.clickup.com/api/v2/task/$1/comment" \
--header "Content-Type: application/json" \
--header "Authorization: $2" \
--data-raw "{
    \"comment_text\": \"$3\",
    \"notify_all\": false
}")

#setting pullrequest number to clickup customid
setting_custom_id=$(curl -s --location --request POST "https://api.clickup.com/api/v2/task/$1/field/$6" \
--header "Content-Type: application/json" \
--header "Authorization: $2" \
--data-raw "{
    \"value\": \"$4\"
}")

#retriving clickup taskname
pull_title=$(curl -sb \
          -H "Content-Type: application/json" \
          -H "Authorization: $2" \
           https://api.clickup.com/api/v2/task/$1 | jq '.name' | sed 's/"//g' )

if [ "${pull_title}" = "null" ]
then
    pull_title="Clickup Task Not Linked to the Branch, Check Branch Naming Convention"
fi

#updating pullrequest title with clickup task
update_pull_title=$(curl --location --request PATCH "https://api.github.com/repos/shipsy/stockone-neo/pulls/$4" \
--header "Accept: application/vnd.github+json" \
--header "Authorization: Bearer $5" \
--header "X-GitHub-Api-Version: 2022-11-28" \
--header "Content-Type: application/json" \
--data-raw "{
    \"title\": \"$pull_title\"
}")

echo -n "${pull_title}"
