#!/bin/bash

# Path to the logs folder
LOGS_DIR="stockone-wms/wms/logs"
SEARCH_PATTERNS_HIGH=("traceback" "error" "critical" "rollback")
SEARCH_PATTERNS_LOW=("secret" "api_key" "exception")

# File to store all matches found
ERROR_LOG_FILE="stockone-wms/wms/logs/error_matches.log"

# Array to hold the files with potential issues
FILES_WITH_ISSUES=()

# Count variable for the total number of matches
TOTAL_MATCH_COUNT=0

# Check for emergency commit flag
if [[ "$*" == *"--no-verify"* ]]; then
  echo -e "\033[0;33mEmergency commit detected: Skipping log checks.\033[0m"
  exit 0
fi

# Check if the logs directory exists
if [ ! -d "$LOGS_DIR" ]; then
  echo "Commit aborted: Log directory ($LOGS_DIR) does not exist."
  exit 1
fi

# Clear the error log file before writing new matches
> "$ERROR_LOG_FILE"

# Function to check a log file for a specific pattern group
check_log_file() {
  local FILE=$1
  local PATTERNS=("${!2}")
  local MATCHES=$(grep -inE "$(IFS='|'; echo "${PATTERNS[*]}")" "$FILE")
  local MATCH_COUNT=0

  # Filter out lines containing 'debug' (case-insensitive)
  MATCHES=$(echo "$MATCHES" | grep -vi 'debug')
  MATCH_COUNT=$(echo "$MATCHES" | wc -l) # Count all remaining matches in this file

  # Add to the total count
  TOTAL_MATCH_COUNT=$((TOTAL_MATCH_COUNT + MATCH_COUNT))

  if [ -n "$MATCHES" ]; then
    FILES_WITH_ISSUES+=("$FILE")

    # Write the filename to the error log file
    echo -e "\033[0;31mFile: $FILE\033[0m" | tee -a "$ERROR_LOG_FILE"

    # Display up to 3 matches on the console and write all matches to the error log file
    echo "$MATCHES" | head -n 3 | while IFS=: read -r LINE_NUMBER LINE_CONTENT; do
      # Extract a snippet of 3-5 words from the match line
      SNIPPET=$(echo "$LINE_CONTENT" | awk '{for(i=1;i<=5;i++) printf $i" "; print "..."}')
      # Print line info in orange, match content in green
      echo -e "  \033[38;5;214mLine $LINE_NUMBER\033[0m: \033[0;32m$SNIPPET\033[0m"
    done

    # Append all matches to the error log file
    echo "$MATCHES" >> "$ERROR_LOG_FILE"
  fi
}

# Check for staged log files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep "\.log$")

# Search for issues in staged files
if [ -n "$STAGED_FILES" ]; then
  echo -e "\033[0;34mChecking staged log files...\033[0m"
  for STAGED_FILE in $STAGED_FILES; do
    if [ -f "$STAGED_FILE" ]; then
      check_log_file "$STAGED_FILE" SEARCH_PATTERNS_HIGH[@]
      check_log_file "$STAGED_FILE" SEARCH_PATTERNS_LOW[@]
    fi
  done
fi

# Search for issues in the logs directory
for LOG_FILE in "$LOGS_DIR"/*.log; do
  if [ -f "$LOG_FILE" ]; then
    check_log_file "$LOG_FILE" SEARCH_PATTERNS_HIGH[@]
    check_log_file "$LOG_FILE" SEARCH_PATTERNS_LOW[@]
  fi
done

# If any issues are found
if [ ${#FILES_WITH_ISSUES[@]} -gt 0 ]; then
  echo -e "\033[0;34mTotal matches found: $TOTAL_MATCH_COUNT\033[0m" # Total matches in blue
  echo -e "\033[0;31mCommit Aborted: Please fix issues in the above files in the current development and retry commit.\033[0m"
  echo -e "\033[0;34mDetails logged in: $ERROR_LOG_FILE\033[0m"
  exit 1
fi

# If no issues are found, allow the commit
echo -e "\033[0;32mNo issues found. Proceeding with commit. 😊\033[0m"
exit 0