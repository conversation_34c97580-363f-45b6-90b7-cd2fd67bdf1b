#! /bin/sh

# 0  default param
# 1  clickup taskid
# 2  clickup token
# 3  pullnumber
# 4  clickup custom id
# 5  clickup repo field id
# 6  repo name


setting_custom_id=$(curl -s --location --request POST "https://api.clickup.com/api/v2/task/$1/field/$4" \
--header "Content-Type: application/json" \
--header "Authorization: $2" \
--data-raw "{
    \"value\": \"$3\"
}")

setting_repo_name=$(curl -s --location --request POST "https://api.clickup.com/api/v2/task/$1/field/$5" \
--header "Content-Type: application/json" \
--header "Authorization: $2" \
--data-raw "{
    \"value\": \"$6\"
}")

setting_jenkins_projectname=$(curl -s --location --request POST "https://api.clickup.com/api/v2/task/$1/field/f99b6c94-9ec3-4ada-9ec8-2ea68601c599" \
--header "Content-Type: application/json" \
--header "Authorization: $2" \
--data-raw "{
    \"value\": \"NEO_TEST\"
}")


echo -n "${setting_custom_id}"