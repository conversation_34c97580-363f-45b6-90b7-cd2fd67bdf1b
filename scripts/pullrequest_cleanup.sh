#! /bin/bash

# 0  default param
# 1  pull numbers
# 2 database host
# 3 database user
# 4 database password

prs=$(echo $1 | tr "," "\n")
echo $ips
for pr in $prs;
do
   echo "pr number is $pr"
   cd $pr
   echo ${pr:4:4}
   export PULL=${pr:4}
   docker-compose down || true
   cd ..
   docker ps -a --filter "name=$pr"
   docker images "$pr*"
   docker volume ls --filter "name=$pr"
   docker network ls --filter "name=$pr*"

   docker rm -f $(docker ps -aq --filter "name=$pr") || true
   docker rmi -f $(docker images "$pr*" -q) || true
   docker volume rm $(docker volume ls --filter "name=$pr" -q) || true
   docker network rm $(docker network ls --filter "name=$pr_*" -q) || true

   sudo rm -rf $pr
   export PGPASSWORD=$4
   psql --host $2 --user $3 -c "drop database ${pr}_stockone_neo" || true
done