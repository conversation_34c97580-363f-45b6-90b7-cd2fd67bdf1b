#!/bin/bash

# 0  default param
# 1  CLICKHOUSE_HOST
# 2  CLICKHOUSE_PORT
# 3  CLICKHOUSE_USER
# 4  CLICKHOUSE_PASSWORD
# 5  CLICKHOUSE_DATABASE
# 6  CLICKHOUSE TABLE

# Set the table creation SQL query
TABLE_QUERY="CREATE TABLE IF NOT EXISTS ${5}.${6}
(
    \`id\` UUID DEFAULT generateUUIDv4(),
    \`request_time\` DateTime('UTC'),
    \`warehouse_id\` UInt64,
    \`warehouse\` String,
    \`username\` String,
    \`request_path\` String,
    \`request_method\` String,
    \`request\` String,
    \`response\` String,
    \`status_code\` UInt16,
    \`module_name\` Nullable(String),
    \`header_referer\` Nullable(String),
    \`request_id\` Nullable(String)
)
ENGINE = MergeTree
PARTITION BY toMonth(request_time)
ORDER BY request_time;"

# Use clickhouse-client to execute the table creation query
clickhouse-client --host=$1 --port=$2 --user=$3 --password=$4 --query="$TABLE_QUERY"

# Check the exit code to see if the query was successful
if [ $? -eq 0 ]; then
  echo "Table 'audit_logs' created successfully."
else
  echo "Error creating table 'audit_logs'."
fi
