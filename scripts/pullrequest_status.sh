#! /bash/bash

# 0  default param
# 1  github token
# 2 home path (/home/<USER>
# 3 database host
# 4 database user
# 5 database password

# intructions
    # - make sure to have cleanpullapps.txt file in home path
    # - make sure to have the pullrequest_cleanup.sh file in home path

state='"open"'
TARGET_LABEL="review"
cd $2/ ; ls -d */ > $2/cleanpullapps.txt
while 
    IFS="" read -r p || [ -n "$p" ]
do   
     http_response=$(curl -sb \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $1" \
          https://api.github.com/repos/shipsy/stockone-neo/pulls/${p:4:4} | jq '.state')
     printf "${http_response}"
     printf "${p}"
     if [ $http_response != $state ] && [ $p != "logs/" ]
     then
          printf "Closed PullNumber ${p:4:4}\n"
          bash $2/pullrequest_cleanup.sh pull${p:4:4} $3 $4 $5
     else
          printf "Open PullNumber ${p:4:4}\n"
          label_response=$(curl -sb \
                -H "Accept: application/vnd.github+json"\
                -H "Authorization: Bearer $1"\
                -H "X-GitHub-Api-Version: 2022-11-28"\
                https://api.github.com/repos/shipsy/stockone-neo/issues/${p:4:4}/labels)
          # Check if the "review" label exists
          echo "$label_response"
          if [[ $(echo "$label_response" | jq -r ".[].name") =~ $TARGET_LABEL ]]; then
                echo "The pull request have the '$TARGET_LABEL' label."
          else
                echo "The pull request does not have the '$TARGET_LABEL' label."
                bash $2/pullrequest_cleanup.sh pull${p:4:4} $3 $4 $5
          fi
     fi
done < $2/cleanpullapps.txt