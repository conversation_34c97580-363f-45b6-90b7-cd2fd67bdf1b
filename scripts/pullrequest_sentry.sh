#!/bin/bash

# 0  default param
# 1  SENTRY TOKEN
# 2  PULL NUMBER

# Make the API request and save the response
response=$(curl --location --silent "https://sentry.shipsy.io/api/0/projects/shipsy/neo-internal/issues/?query=server_name%3Aneo${2}" \
--header "Authorization: Bearer $1")

all_issues="https://sentry.shipsy.io/organizations/shipsy/issues/?project=87&query=is%3Aunresolved+server_name%3Aneo${2}&referrer=issue-list&statsPeriod=1h&stream_index=0"

# Check if the response is not empty
if [ -n "$response" ]; then

    # Count the number of issues
    count=$(echo "$response" | jq length)

    # Print the report
    echo "<h2>🛠️ Report from Sentry: There are ${count} issues. <a href=\"$all_issues\">View all issues</a></h2>"

    # Start building the table structure
    echo "<table>"
    echo "<tr>"
    echo "<th>Title</th>"
    echo "<th>Permalink</th>"
    echo "</tr>"

    # Loop through each item and extract relevant fields using jq
    echo "${response}" | jq -r '.[] | "<tr><td>\(.title)</td><td><a href=\"\(.permalink)\">\(.permalink)</a> 🔗</td></tr>"'

    # Close the table
    echo "</table>"
else
    echo "Empty response from API."
fi
