#! /bin/sh

# 0  default param
# 1  github usernames
# 2  github_token


prs=$(echo $1 | tr "," "\n")

for pr in $prs;
do
    user=$(curl -sb \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $2" \
           https://api.github.com/repos/shipsy/stockone-neo/pulls/${pr} | jq '.user' | jq '.login' | sed 's/"//g' )

    http_response=$(curl -sb \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $2" \
           https://api.github.com/users/${user} | jq '.email' )

    if [ $http_response != null ]
    then
        echo -n "${http_response},"
    fi
done