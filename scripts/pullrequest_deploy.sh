#! /bin/sh

echo  $0 $1 $2 $#

# 0  default param
# 1  Deployment Server
# 2  Deployment User
# 3  Database Server
# 4  Database User
# 5  Database Password
# 6  Pullrequest Number
# 7  GITHUB Token
# 8  CLICKHOUSE_HOST
# 9  CLICKHOUSE_PORT
# 10 CLICKHOUSE_USER
# 11 CLICKHOUSE_PASS
# 12 SENTY_DSN
# 13 EMAIL_HOST_PASSWORD


ips=$(echo $1 | tr "," "\n")

#creating a database
export PGPASSWORD=$5
psql --host $3 --user $4 -c "create database pull$6_stockone_neo"

#can be in multiple ips defualt is only one ip
echo $ips
for addr in $ips;
do
  #setting up ui nginx
  pwd
  ls
  > stockone-neo-js/app/nginx_api.conf
  (echo -n "proxy_pass  " ;echo -n http://neo-api; echo -n $6;echo ":8001;") > stockone-neo-js/app/nginx_api.conf

  #copying neccessary files to deployment machine
  zip -r neo-pull$6.zip stockone-wms nginx init #
  echo "Zipped Suucessfully"
  scp neo-pull$6.zip $2@$1:/home/<USER>/
  ssh $2@$addr <<EOF
    rm -rf pull$6
    mkdir -p pull$6
    cd /home/<USER>/
    echo A | unzip neo-pull$6.zip -d pull$6
    sudo rm neo-pull$6.zip
    pwd
    exit
EOF
  scp compose/pullrequest-application.yml $2@$addr:/home/<USER>/pull$6/docker-compose.yml
  scp scripts/pullrequest_env.sh $2@$addr:/home/<USER>/pull$6/
  scp scripts/clickhouse_audit_table.sh $2@$addr:/home/<USER>/pull$6/
  ssh $2@$addr <<EOF
    cd /home/<USER>/pull$6/
    pwd
    ls
    clickhouse-client --host=$8 --user=${10} --password=${11} --query="CREATE DATABASE IF NOT EXISTS pull$6_stockone_neo;"
    sh clickhouse_audit_table.sh $8 $9 ${10} ${11} pull$6_stockone_neo pull$6_audit_logs
    sh pullrequest_env.sh $3 $4 $5 $6 $7 $8 $9 ${10} ${11} pull$6_stockone_neo pull$6_audit_logs ${12} ${13} > .env
    sudo docker-compose down
    exit
EOF
done