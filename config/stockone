server {
    listen 80;
    listen [::]:80;
    server_name byjus.mieone.com;
    root /var/www/html/WMS_ANGULAR/ANGULAR_WMS/app;
    location / {
                # First attempt to serve request as file, then
                # as directory, then fall back to displaying a 404.
                try_files $uri $uri/ =404;
        }
}

server {
   # Define the directory where the contents being requested are stored
   # root /usr/src/app/project/;

   # Define the default page that will be served If no page was requested
   # (ie. if www.kennedyfamilyrecipes.com is requested)
   # index index.html;

   # Define the server name, IP address, and/or port of the server
   listen 8939;
   server_name byjus.mieone.com;

   # Define the specified charset to the “Content-Type” response header field
   charset utf-8;

   # Configure NGINX to deliver static content from the specified folder
    client_max_body_size 200M;
   # Configure NGINX to reverse proxy HTTP requests to the upstream server (Gunicorn (WSGI server))
   location /static {
           autoindex on;
           root /var/www/html/WMS_ANGULAR/stockone-wms/wms;
   }

   location / {
        include proxy_params;
        proxy_pass http://unix:/var/www/html/WMS_ANGULAR/stockone-wms/wmsstockonegunicorn.sock;
   }

}
