[Unit]
Description=Gunicorn instance to serve Stockone
After=network.target

[Service]
User=root
Group=root
WorkingDirectory=/var/www/html/WMS_ANGULAR/stockone-wms/wms
Environment="PATH=/var/www/html/WMS_ANGULAR/stockone-wms/wms/setup/wms/bin"
Environment="DB_HOST=app.stockone.com"
Environment="DB_NAME=stockone"
Environment="DB_USER=stockone"
Environment="DB_PASS=stockone^123"
Environment="DB_REV_HOST=app.stockone.com"
Environment="DB_REV_NAME=stockone_reversion"
Environment="DB_REV_USER=stockone"
Environment="DB_REV_PASS=stockone^123"
Environment="DB_REPORTS_HOST=app.stockone.com"
Environment="DB_REPORTS_NAME=stockone_reversion"
Environment="DB_REPORTS_USER=stockone"
Environment="DB_REPORTS_PASS=stockone^123"
ExecStart=/var/www/html/WMS_ANGULAR/stockone-wms/wms/setup/wms/bin/gunicorn --workers 20 --bind unix:/var/www/html/WMS_ANGULAR/stockone-wms/wmsstockonegunicorn.sock -m 007 wms.wsgi --access-logfile=/var/www/html/WMS_ANGULAR/stockone-wms/wms/logs/stockone-guinicorn.access.log --log-file=/var/www/html/WMS_ANGULAR/stockone-wms/wms/logs/stockone-guinicorn.sys.log --log-level debug --capture-output --timeout=900

[Install]
WantedBy=multi-user.target