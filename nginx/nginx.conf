upstream hello_django {
    include nginx_api.conf;
}
upstream hello_ui {
    include nginx_ui.conf;
}

server {

    listen 80;
    server_name _;
    client_max_body_size 200M;

    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
    add_header Referrer-Policy "strict-origin";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options nosniff;

    access_log  /var/log/nginx/access.log;
    error_log  /var/log/nginx/error.log;

    
    location /app/ {
        proxy_pass http://hello_ui;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
        add_header Last-Modified $date_gmt;
        add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        if_modified_since off;
        expires off;
    }

    location /static/ {
        autoindex on;
        alias /stockone-wms/wms/static/;
    }

    location ~ ^/(o|api|admin|auth|accounts|django_query_profiler|schema|inventory|core|inbound|outbound|production|dashboard|wms_base|reports|staticfiles|static)/ {
        proxy_pass http://hello_django;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout       3600;
        proxy_send_timeout          3600;
        proxy_read_timeout          3600;
        send_timeout                3600;
        proxy_set_header Host $host;
        proxy_redirect off;
    }

    location /v2/ {
        proxy_pass http://hello_ui;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
        add_header Last-Modified $date_gmt;
        add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        if_modified_since off;
        expires off;
    }

}