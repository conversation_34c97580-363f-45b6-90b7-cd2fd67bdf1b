version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ELASTIC_PASSWORD=your_elastic_password # Define the password for the "elastic" user
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - action.auto_create_index=true
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data

  kibana:
    image: docker.elastic.co/kibana/kibana:8.9.0
    container_name: kibana
    environment:
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_SERVICE_TOKEN=AAEAAWVsYXN0aWMva2liYW5hL3Rva2VuX1k5UV9NNUlCeFpRRFhRRWhydFFwOm1LR01zaW5KU2JPM1J0OV9pbHFrOWc
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

volumes:
  es_data:

