version: '3.8'
services:
  opensearch:
    image: opensearchproject/opensearch:2.10.0
    container_name: opensearch-node1
    environment:
      - discovery.type=single-node
      - DISABLE_SECURITY_PLUGIN=true
      - network.host=0.0.0.0
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - "OPENSEARCH_USERNAME=admin"     # Default username
      - "OPENSEARCH_PASSWORD=admin"     # Default password
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - opensearch-data:/usr/share/opensearch/data
    ports:
      - 9200:9200
      - 9600:9600
    networks:
      - opensearch-net

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2.10.0
    container_name: opensearch-dashboards
    ports:
      - 5601:5601
    environment:
      OPENSEARCH_HOSTS: '["http://opensearch-node1:9200"]'
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: true
    networks:
      - opensearch-net

volumes:
  opensearch-data:

networks:
  opensearch-net:
    driver: bridge
