version: "3.8"

services:
  redis:
    image: "redis:alpine"
    hostname: redis   
    command: redis-server --requirepass JguvU^FP4V
    ports:
      - 6378:6379

  api:
    container_name: api
    build: ./stockone-wms/wms
    command: gunicorn --workers 20 --bind 0:8001 -m 007 wms.wsgi --access-logfile=stockone-guinicorn.access.log --log-file=logs/stockone-guinicorn.sys.log --log-level debug --capture-output --timeout=900
    volumes:
      - static_volume:/application/static
      - ./stockone-wms/wms/:/application/
    env_file: 
      - ./.env
    environment:
       ENTRYPOINT_FLAG: 'true'
    stdin_open: true
    tty: true
    healthcheck:
        test: curl --fail http://api:8001/mieone_panel/ || exit 1
        interval: 30s
        retries: 30
        start_period: 20s
        timeout: 10s
    
  celery:
    image: api
    build: ./stockone-wms/wms
    command: ['celery', '-A', 'wms.celery', 'worker', '-l', 'info', '-B']
    env_file: 
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    depends_on:
      api:
        condition: service_healthy

  flower:
    image: api
    build: ./stockone-wms/wms
    command: ['celery', '-A', 'wms.celery', 'flower', '--url-prefix=flower']
    env_file: 
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    ports:
      - 5557:5555
    depends_on:
      - redis
      - celery

  angular:
    container_name: ui
    build: ./ANGULAR_WMS
    volumes:
      - ./ANGULAR_WMS/app:/app/
    depends_on:
      api:
        condition: service_healthy
 
  nginx:
    build: ./nginx
    volumes:
      - static_volume:/stockone-wms/wms/static
    ports:
      - 1337:80
    depends_on:
      - api
      - angular

volumes:
  static_volume: