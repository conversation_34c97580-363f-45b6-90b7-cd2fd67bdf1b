version: "3.8"

services:

  celery:
    image: ${IMAGE_URL}
    command: ['celery', '-A', 'wms.celery', 'worker', '-l', 'info', '-B']
    env_file:
      - .env
    volumes:
      - ./celery_logs/:/application/logs/
    environment:
       ENTRYPOINT_FLAG: 'false'

  flower:
    image: ${IMAGE_URL}
    command: ['celery', '-A', 'wms.celery', 'flower', '--url-prefix=flower']
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    ports:
      - 5557:5555
    depends_on:
      - celery