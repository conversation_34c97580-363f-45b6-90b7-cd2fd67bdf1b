version: "3.8"

services:
  db1:
    image: mysql/mysql-server:8.0.23
    command: mysqld --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
       MYSQL_DATABASE: 'stockone'
       MYSQL_USER: 'stockone'
       MYSQL_PASSWORD: 'stockone^123'
       MYSQL_ROOT_PASSWORD: 'stockone^1234'

    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    

  db2:
    image: mysql/mysql-server:8.0.23
    command: mysqld --default-authentication-plugin=mysql_native_password
    restart: always    
    environment:
       MYSQL_DATABASE: 'stockone_reversion'
       MYSQL_USER: 'stockone'
       MYSQL_PASSWORD: 'stockone^123'
       MYSQL_ROOT_PASSWORD: 'stockone^1234'
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
  

  redis:
    image: "redis:alpine"
    hostname: redis   
   

  api:
    container_name: api
    build: ./stockone-wms/wms
    command: gunicorn --workers 5 --bind 0:8001 -m 007 wms.wsgi --access-logfile=stockone-guinicorn.access.log --log-file=logs/stockone-guinicorn.sys.log --log-level debug --capture-output --timeout=900
    env_file: 
      - ./stockone-wms/wms/.env
    environment:
       ENTRYPOINT_FLAG: 'true'
    depends_on:
      db1:
        condition: service_healthy
      db2:
        condition: service_healthy
    stdin_open: true
    tty: true
    volumes:
      - static_volume:/application/static
    healthcheck:
        test: wget --no-verbose --tries=1 http://api:8001/mieone_panel/ || exit 1
        interval: 60s
        retries: 20
        start_period: 20s
        timeout: 10s

  angular:
    container_name: ui
    build: ./ANGULAR_WMS
    volumes:
      - ./ANGULAR_WMS/app:/app/
    depends_on:
      api:
        condition: service_healthy
        
  celery:
    image: api
    build: ./stockone-wms/wms
    command: ['celery', '-A', 'wms.celery', 'worker', '-l', 'info', '-B']
    env_file:
     - ./stockone-wms/wms/.env
    environment:
       ENTRYPOINT_FLAG: 'false'
    depends_on:
      - api

  nginx:
    build: ./nginx
    ports:
      - 1339:80
    volumes:
      - static_volume:/stockone-wms/wms/static
    depends_on:
      - api
      - angular

  mobile:
    build: ./MOBILE/
    ports:
      - 1338:80


  cypress:
    build: ./NEW_CYPRESS_WMS/
    stdin_open: true
    tty: true
    command: tail -F anything
    env_file: 
      - ./NEW_CYPRESS_WMS/cypress/.env

volumes:
  static_volume: