version: "3.8"

services:
  db1:
    image: postgres:latest
    restart: always
    environment:
      - POSTGRES_DB=stockone
      - POSTGRES_USER=stockone
      - POSTGRES_PASSWORD=stockone^123
    ports:
      - '5433:5432'
    volumes:
      - db1-data:/var/lib/postgresql/data

  redis:
    image: "redis:latest"
    hostname: redis   
    command: redis-server --requirepass JguvU^FP4V
    ports:
      - '6378:6379'
    volumes:
      - redis_data:/data

  api:
    container_name: api
    build: ./stockone-wms/
    command: uvicorn wms.asgi:application --host 0.0.0.0 --port 8001 --reload
    volumes:
      - static_volume:/application/static
      - ./stockone-wms/wms/:/application/
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'true'
    ports:
      - 8081:8001
    depends_on:
      - db1
    stdin_open: true
    tty: true
    healthcheck:
        test: curl --fail http://api:8001/admin/ || exit 1
        interval: 30s
        retries: 30
        start_period: 20s
        timeout: 10s

  celery:
    image: api
    build: ./stockone-wms/
    command: ['celery', '-A', 'wms.celery', 'worker', '-l', 'info', '-B']
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    depends_on:
      api:
        condition: service_healthy

  flower:
    image: api
    build: ./stockone-wms/
    command: ['celery', '-A', 'wms.celery', 'flower', '--url-prefix=flower']
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    ports:
      - 5557:5555
    depends_on:
      - redis
      - celery
volumes:
  static_volume:
  db1-data:
  redis_data:
