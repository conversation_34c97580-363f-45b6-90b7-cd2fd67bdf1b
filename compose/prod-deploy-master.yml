version: "3.8"

services:

  api:
    container_name: api
    image: ${IMAGE_URL}
    command: gunicorn --workers 4 --bind 0:8001 -m 007 wms.asgi --access-logfile=stockone-guinicorn.access.log --log-file=logs/stockone-guinicorn.sys.log --log-level debug --capture-output --timeout=900 --worker-class wms.asgi_worker.UvicornWorker
    volumes:
      - static_volume:/application/static
      - ./logs/:/application/logs/
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'true'
    ports:
      - 8081:8001
    stdin_open: true
    tty: true
    healthcheck:
        test: curl --fail http://api:8001/admin/ || exit 1
        interval: 30s
        retries: 30
        start_period: 20s
        timeout: 10s

  health-check:
    image: alpine
    command: tail -f /dev/null
    depends_on:
      api:
        condition: service_healthy

volumes:
  static_volume:
