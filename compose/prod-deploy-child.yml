version: "3.8"

services:

  api:
    container_name: api
    image: abusiddique42/stockone-neo-api:${TAG}
    command: gunicorn --workers 20 --bind 0:8001 -m 007 wms.wsgi --access-logfile=stockone-guinicorn.access.log --log-file=logs/stockone-guinicorn.sys.log --log-level debug --capture-output --timeout=900
    volumes:
      - static_volume:/application/static
      - ./logs/:/application/logs/
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    ports:
      - 8081:8001
    stdin_open: true
    tty: true
    healthcheck:
        test: curl --fail http://api:8001/admin/ || exit 1
        interval: 30s
        retries: 30
        start_period: 20s
        timeout: 10s

  health-check:
    image: alpine
    command: tail -f /dev/null
    depends_on:
      api:
        condition: service_healthy

volumes:
  static_volume:
