user                    nginx;
error_log               /var/log/nginx/error.log warn;
pid                     /var/run/nginx.pid;
worker_processes        auto;
worker_rlimit_nofile    33282;

events {
    worker_connections  1024;
}

http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;

  log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

  include       conf.d/*.conf;

  map $http_upgrade $connection_upgrade {
      default     "upgrade";
  }
  
  server {
    	listen 80;
    	server_name _;
    	if ($http_x_forwarded_proto = 'http'){
    		return 301 https://$host$request_uri;
    	}
      root /var/app/current/ANGULAR_WMS/app;
      location /static {
		proxy_pass https://stockone-prod1.s3.ap-south-1.amazonaws.com;
      }
      location /media {
		proxy_pass https://stockone-prod1.s3.ap-south-1.amazonaws.com;
      }
      
      location ~ ^/(rest_api|o|api|mieone_panel|get_pos_user_data|search_customer_data|get_current_order_id|customer_order|search_product_data|lms|report_builder|pro|app|wms_base|quality_control|reports)/ {
          proxy_pass          http://127.0.0.1:8000;
          proxy_http_version  1.1;

          proxy_set_header    Connection          $connection_upgrade;
          proxy_set_header    Upgrade             $http_upgrade;
          proxy_set_header    Host                $host;
          proxy_set_header    X-Real-IP           $remote_addr;
          proxy_set_header    X-Forwarded-For     $proxy_add_x_forwarded_for;
	  proxy_read_timeout 1000;

      }

      access_log    /var/log/nginx/access.log main;

      types_hash_max_size   2048;
      client_header_timeout 3600;
      #proxy_connect_timeout 1000;
      #proxy_send_timeout    1000;
      send_timeout 1000;
      client_max_body_size 200M;
      client_body_timeout   3600;
      keepalive_timeout 6000s;
	  proxy_connect_timeout 6000s;
	  proxy_send_timeout 6000s;
	  proxy_read_timeout 6000s;
	  fastcgi_send_timeout 6000s; 
	  fastcgi_read_timeout 6000s;

      gzip                  off;
      gzip_comp_level       4;
      
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
      # add_header  X-Frame-Options "SAMEORIGIN" always;
      add_header X-XSS-Protection "1; mode=block";
      # Include the Elastic Beanstalk generated locations
    #   include conf.d/elasticbeanstalk/00_application.conf;
      include conf.d/elasticbeanstalk/healthd.conf;
  }
}
