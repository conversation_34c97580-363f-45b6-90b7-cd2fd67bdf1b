pipeline {
    agent none
    environment {
        DEPLOYMENT_MACHINE = credentials("${env.BR<PERSON>CH_NAME}")
        BRANCH = "${env.BRANCH_NAME}"
        GIT_HUB_TOKEN = credentials("GIT_HUB_TOKEN")
        DEPLOYMENT_USER = 'platform'
        HOT_DEPLOYMENT_PATH = '/var/www/html/Hot/stockone-pro/'
        TEST_DEPLOYMENT_PATH = '/var/www/html/stockone-pro/'
        TECH_SLACK = credentials("TECH_SLACK")
    }
    stages {

        stage('Start Deployment') {
            agent {
                label 'slave2'
            }
            steps{
                script {
                    sh "echo hello from stage1"
                }
            }
        }

        stage('Deployment') {
            agent {
                label 'slave2'
            }
            steps {
                script {

                    if ( "${BRANCH}" == "Hot" ) {
                        sh "echo Deploying Hot Branch"
                        sh "chmod +x deploy_docker.sh"
                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"${BRANCH} DEPLOYMENT STARTED!\"}' ${TECH_SLACK}"
                        sh "sudo sh deploy_docker.sh ${DEPLOYMENT_MACHINE} ${DEPLOYMENT_USER}  ${GIT_HUB_TOKEN} ${HOT_DEPLOYMENT_PATH} ${BRANCH}"
                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"${BRANCH} DEPLOYMENT COMPLETED!\"}' ${TECH_SLACK}"
                    } else if ("${BRANCH}" == "inter_deploy") {
                        sh "echo  Deploying inter_deploy  Branch"
                        sh "chmod +x deploy_docker.sh"
                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"${BRANCH} DEPLOYMENT STARTED!\"}' ${TECH_SLACK}"
                        sh "sudo sh deploy_docker.sh ${DEPLOYMENT_MACHINE} ${DEPLOYMENT_USER}  ${GIT_HUB_TOKEN} ${TEST_DEPLOYMENT_PATH} ${BRANCH}"
                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"${BRANCH} DEPLOYMENT COMPLETED!\"}' ${TECH_SLACK}"
                    } else {
                        sh "echo ${BRANCH} not set for Deployemt" 
                    }
                }
            }
        }

        stage ('stage3') {
            agent {
                label 'slave2'
            }
            steps{
                script {
                    sh "echo hello from stage3"
                }
            }
        }

    }
}