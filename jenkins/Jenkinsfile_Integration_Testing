def buildNumber = env.BUILD_NUMBER as int
if (buildNumber > 1) milestone(buildNumber - 1)
milestone(buildNumber)

pipeline {
    agent any
    environment {
        CYPRESS_ENV_PATH = './NEW_CYPRESS_WMS/cypress/.env'
        UI_ENV='./ANGULAR_WMS/app/variable_env.json'
        API_ENV='./stockone-wms/wms/.env'
        // JENKINS_NODE = credentials("JENKINS_NODE")
        APPLICATION='http://**************:1339'
        MOBILE='http://**************:1338'
        BRANCH = "${env.BRANCH_NAME}"
        COMMIT="${env.GIT_COMMIT}"
    }
    
    stages {
        stage('Setting Environment') {
            steps{
                script{
                    sh "echo ${COMMIT}"
                    sh "git submodule update --init"
                    sh "cd NEW_CYPRESS_WMS && ls"
                    sh "cd NEW_CYPRESS_WMS && ls"
                    sh "echo \"{ \"version\" : \"jenkins\" }\" > ${UI_ENV}"
                    sh  "echo VERSION_NUMBER=jenkins >> ${API_ENV}"
                    dir('NEW_CYPRESS_WMS') {
                        // The below will clone your repo and will be checked out to master branch by default.
                        git branch: 'main' , credentialsId: 'RapidCI', url: 'https://github.com/shipsy/stockone-automation.git'
                        sh 'ls'
                        // Do a ls -lart to view all the files are cloned. It will be clonned. This is just for you to be sure about it.
                        sh "ls -lart ./*" 
                        // List all branches in your repo. 
                        sh "git branch -a"
                        // Checkout to a specific branch in your repo.
                        sh "git checkout main"
                        //checking on which path
                        sh "pwd"
                    }
                    sh "cd MOBILE && ls"
                    dir('MOBILE') {
                        // The below will clone your repo and will be checked out to master branch by default.
                        git branch: 'main' , credentialsId: 'RapidCI', url: 'https://github.com/shipsy/StockOne-Mobile.git'
                        sh 'ls'
                        // Do a ls -lart to view all the files are cloned. It will be clonned. This is just for you to be sure about it.
                        sh "ls -lart ./*" 
                        // List all branches in your repo. 
                        sh "git branch -a"
                        // Checkout to a specific branch in your repo.
                        sh "git checkout main"
                        //checking on which path
                        sh "pwd"
                    }
                    sh "pwd"
                    sh '''
                    docker version
                    docker-compose version
                    curl --version
                    docker-compose -f docker-compose-test.yml down
                    docker image prune -f
                '''
                }
            }
        }

        stage('SonarQube Analysis') {
            when { expression { env.BRANCH_NAME != 'dev' } }
            steps{
                script {
                    sh "ls -lart ./*" 
                    def scannerHome = tool 'sonarqube';
                    withSonarQubeEnv() {
                        sh "${scannerHome}/bin/sonar-scanner \
                        -Dsonar.sources=. \
                        -Dsonar.python.version=3.8 \
                        -Dsonar.exclusions=ANGULAR_WMS/app/vendor/**,**/*.java,**/*.js,\
                        -Dsonar.sourceEncoding=UTF-8"
                    }
                }
            }
        }

        stage('Build') {
            parallel {
                stage('SETTING_ENV_IN_1') {
                    steps{
                        script {
                            echo env.BRANCH_NAME
                            sh "echo BRANCH_NAME=${BRANCH_NAME} >> ${CYPRESS_ENV_PATH}"
                            sh "echo EMAILS_LIST=<EMAIL>, <EMAIL>,<EMAIL> >> ${CYPRESS_ENV_PATH}"
                            sh "echo COMMIT=${COMMIT} >> ${CYPRESS_ENV_PATH}"
                            sh "echo LOCAL=false >> ${CYPRESS_ENV_PATH}"
                            sh "cat ${CYPRESS_ENV_PATH}"
                            sh 'docker-compose -f docker-compose-test.yml config'
                            sh 'docker-compose -f docker-compose-test.yml up --build -d'
                        }
                    }
                }
                stage('SETTING_ENV_IN_4') {
                    agent {
                        label "slave5"
                    }
                    steps{
                        script {

                            sh 'ls'
                            // sh "git submodule update --init"
                            sh "cd NEW_CYPRESS_WMS && ls"
                            dir('NEW_CYPRESS_WMS') {
                                // The below will clone your repo and will be checked out to master branch by default.
                                git branch: 'main' , credentialsId: "RapidCI", url: 'https://github.com/shipsy/stockone-automation.git'
                                sh 'ls'
                                // Do a ls -lart to view all the files are cloned. It will be clonned. This is just for you to be sure about it.
                                sh "ls -lart ./*" 
                                // List all branches in your repo. 
                                sh "git branch -a"
                                // Checkout to a specific branch in your repo.
                                sh "git checkout main"
                                //checking on which path
                                sh "pwd"
                                //checking on which branch
                                sh "git branch"
                            }
                            sh "pwd"
                            sh '''
                                sudo docker version
                                sudo docker-compose version
                                sudo curl --version
                            '''

                            // def DEPLOYED_URL = env.JENKINS_NODE 

                            sh "pwd"
                            sh "> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_app=${env.APPLICATION} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_backend=${env.APPLICATION} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_mobile=${env.MOBILE} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_uname=test_bangalore_wh1 >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_pswd=Stockone@123 >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_admin_uname=admin >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_admin_pswd=admin >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_prefix=test >> ${CYPRESS_ENV_PATH}"
                            sh "echo BRANCH_NAME=${BRANCH_NAME} >> ${CYPRESS_ENV_PATH}"
                            sh "echo EMAILS_LIST=<EMAIL>, <EMAIL>,<EMAIL> >> ${CYPRESS_ENV_PATH}"
                            sh "echo COMMIT=${COMMIT} >> ${CYPRESS_ENV_PATH}"
                            sh "echo LOCAL=false >> ${CYPRESS_ENV_PATH}"

                            sh "cat ${CYPRESS_ENV_PATH}"
                            sh "sudo docker-compose -f docker-compose-cypress.yml up --build -d"

                        }
                    }
                }
                stage('SETTING_ENV_IN_2') {
                    agent {
                        label "slave4"
                    }
                    steps{
                        script {

                            sh 'ls'
                            // sh "git submodule update --init"
                            sh "cd NEW_CYPRESS_WMS && ls"
                            dir('NEW_CYPRESS_WMS') {
                                // The below will clone your repo and will be checked out to master branch by default.
                                git branch: 'main' , credentialsId: "RapidCI", url: 'https://github.com/shipsy/stockone-automation.git'
                                sh 'ls'
                                // Do a ls -lart to view all the files are cloned. It will be clonned. This is just for you to be sure about it.
                                sh "ls -lart ./*" 
                                // List all branches in your repo. 
                                sh "git branch -a"
                                // Checkout to a specific branch in your repo.
                                sh "git checkout main"
                                //checking on which path
                                sh "pwd"
                                //checking on which branch
                                sh "git branch"
                            }
                            sh "pwd"
                            sh '''
                                sudo docker version
                                sudo docker-compose version
                                sudo curl --version
                            '''

                            // def DEPLOYED_URL = env.JENKINS_NODE 

                            sh "pwd"
                            sh "> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_app=${env.APPLICATION} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_backend=${env.APPLICATION} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_mobile=${env.MOBILE} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_uname=test_bangalore_wh1 >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_pswd=Stockone@123 >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_admin_uname=admin >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_admin_pswd=admin >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_prefix=test >> ${CYPRESS_ENV_PATH}"
                            sh "echo BRANCH_NAME=${BRANCH_NAME} >> ${CYPRESS_ENV_PATH}"
                            sh "echo EMAILS_LIST=<EMAIL>, <EMAIL>,<EMAIL> >> ${CYPRESS_ENV_PATH}"
                            sh "echo COMMIT=${COMMIT} >> ${CYPRESS_ENV_PATH}"
                            sh "echo LOCAL=false >> ${CYPRESS_ENV_PATH}"

                            sh "cat ${CYPRESS_ENV_PATH}"
                            sh "sudo docker-compose -f docker-compose-cypress.yml up --build -d"

                        }
                    }
                }
                stage('SETTING_ENV_IN_3') {
                    agent {
                        label "slave3"
                    }
                    steps{
                        script {

                            sh 'ls'
                            // sh "git submodule update --init"
                            sh "cd NEW_CYPRESS_WMS && ls"
                            dir('NEW_CYPRESS_WMS') {
                                // The below will clone your repo and will be checked out to master branch by default.
                                git branch: 'main' , credentialsId: "RapidCI", url: 'https://github.com/shipsy/stockone-automation.git'
                                sh 'ls'
                                // Do a ls -lart to view all the files are cloned. It will be clonned. This is just for you to be sure about it.
                                sh "ls -lart ./*" 
                                // List all branches in your repo. 
                                sh "git branch -a"
                                // Checkout to a specific branch in your repo.
                                sh "git checkout main"
                                //checking on which path
                                sh "pwd"
                                //checking on which branch
                                sh "git branch"
                            }
                            sh "pwd"
                            sh '''
                                sudo docker version
                                sudo docker-compose version
                                sudo curl --version
                            '''

                            // def DEPLOYED_URL = env.JENKINS_NODE 

                            sh "pwd"
                            sh "> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_app=${env.APPLICATION} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_backend=${env.APPLICATION} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_mobile=${env.MOBILE} >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_uname=test_bangalore_wh1 >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_pswd=Stockone@123 >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_admin_uname=admin >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_admin_pswd=admin >> ${CYPRESS_ENV_PATH}"
                            sh "echo CYPRESS_prefix=test >> ${CYPRESS_ENV_PATH}"
                            sh "echo BRANCH_NAME=${BRANCH_NAME} >> ${CYPRESS_ENV_PATH}"
                            sh "echo EMAILS_LIST=<EMAIL>, <EMAIL>,<EMAIL> >> ${CYPRESS_ENV_PATH}"
                            sh "echo COMMIT=${COMMIT} >> ${CYPRESS_ENV_PATH}"
                            sh "echo LOCAL=false >> ${CYPRESS_ENV_PATH}"

                            sh "cat ${CYPRESS_ENV_PATH}"
                            sh "sudo docker-compose -f docker-compose-cypress.yml up --build -d"

                        }
                    }
                }
            }


            
        }

        stage('Test Preparation') {
            agent {
                label "slave5"
            }      
            steps{
                script {
                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js creation'
                }
            }
        }
        stage('Integration Test'){
            parallel {
                stage('INTEGRATION_TEST1'){
                    agent {
                        label "slave5"
                    }
                    steps {
                        script {
                            parallel (
                                "DOCK_TO_STOCK_1" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js doc_to_stock_module_1'
                                },
                                "DOCK_TO_STOCK_2" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js doc_to_stock_module_2'
                                },
                                "PI_1" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js production_and_inventory_module_1'
                                },
                                "API" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js api'
                                }
                            )
                            sh  'sudo docker-compose -f docker-compose-cypress.yml down'

                        }
                    }
                }
                stage('INTEGRATION_TEST2'){
                    agent {
                        label "slave3"
                    }
                    steps {
                        script {
                            parallel (
                                 "ORDER_TO_CASH_1" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js order_to_cash_module_1'
                                },
                                "ORDER_TO_CASH_2" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js order_to_cash_module_2'
                                }
                            )
                            sh  'sudo docker-compose -f docker-compose-cypress.yml down'
                        }
                    }
                }
                stage('INTEGRATION_TEST3'){
                    agent {
                        label "slave4"
                    }
                    steps {
                        script {
                            parallel (
                                "FULFILMENT_1" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js fulfilment_module_1'
                                },
                                "FULFILLMENT_2" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js fulfilment_module_2'
                                },
                                "PI_2" : {
                                    sh 'sudo docker-compose -f docker-compose-cypress.yml exec -T cypress xvfb-run -a node runner.js production_and_inventory_module_2'
                                }
                            )
                            sh  'sudo docker-compose -f docker-compose-cypress.yml down'
                        }
                    }
                }
            }
        }
        stage ('Publish Test Result') {
            steps{
                sh 'docker-compose -f docker-compose-test.yml exec -T cypress node fb_mail.js'
            }
        }
        stage('Cleaning Environment') {      
            steps{
                sh '''
                    docker-compose -f docker-compose-test.yml down
                '''
            }
        }

        stage("Quality Gate"){
            steps{
                script {
                    timeout(time: 1, unit: 'HOURS') {
                        def qg = waitForQualityGate()
                        if (qg.status != 'OK') {
                            error "Pipeline aborted due to quality gate failure: ${qg.status}"
                        }
                    }
                }
            }
        }

        // stage('Deployment') {
        //     steps{
        //         script {
        //             try{
        //                 sh 'echo BRANCH_NAME=${BRANCH} >> ${CYPRESS_ENV_PATH}'
        //                 if (env.BRANCH_NAME == 'dev') {
        //                     sh 'ls'
        //                     sh 'sudo sh deploy_dev.sh ${DEPLOYMENT_MACHINE} ${BRANCH_NAME}'
        //                 } else if (env.BRANCH_NAME == 'staging') {
        //                     sh 'ls'
        //                     sh 'sudo sh deploy_dev.sh ${DEPLOYMENT_MACHINE} ${BRANCH_NAME}'
        //                 } else {
        //                     echo 'NOT DEPLOYED only deploying auto-deployment branch for testing'
        //                 }
        //             } catch (e) {
        //                 // fail the build if an exception is thrown
        //                 currentBuild.result = "FAILED"
        //                 throw e
        //             } finally {
        //                     // Post build steps here
        //                     /* Success or failure, always run post build steps */
        //                     // send email
        //                     // publish test results etc etc
        //             }     
        //         }
        //     }
        // }
    }

   post {
        failure {
            script {
                sh 'sudo docker-compose -f docker-compose-test.yml down'
            }
        }
    }
}
