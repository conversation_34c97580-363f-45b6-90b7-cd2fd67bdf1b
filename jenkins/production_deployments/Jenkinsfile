Boolean simulationCheck(String inputString) {
    if (inputString == "Yes"){
        return true
    } else {
        return false
    }
}

String lbTarget(machineIP, operation, machineName, lbId, lbProjectKey) {

    sh "echo ${operation} target ${machineName}"
    if (operation == "add"){
        sh "chmod +x HETZNER/add_target.sh" 
        sh "sudo sh HETZNER/add_target.sh ${lbId} ${lbProjectKey} ${machineIP}"
    } else {
        sh "chmod +x HETZNER/remove_target.sh" 
        sh "sudo sh HETZNER/remove_target.sh ${lbId} ${lbProjectKey} ${machineIP}"
    }
    return ''
}





pipeline {
    agent none
    environment {
        BRANCH = "${env.BRANCH_NAME}"
        GIT_HUB_TOKEN = credentials("GIT_HUB_TOKEN")
        DEPLOYMENT_USER = 'platform'
        NXT_MASTER_DEPLOYMENT_MACHINE =  credentials("NXT_MASTER_DEPLOYMENT_MACHINE")
        NXT_CHILD_DEPLOYMENT_MACHINE =  credentials("NXT_CHILD_DEPLOYMENT_MACHINE")
        NXT_CELERY_DEPLOMENT_MACHINE = credentials("NXT_CELERY_DEPLOMENT_MACHINE")
        NXT_LB_ID = credentials("NXT_LB_ID")
        LB_PROJECT_KEY = credentials("LB_PROJECT_KEY")
        NXT_DEPLOYMENT_PATH = '/var/www/html/stockone-pro/'
        PROD_DEPLOY_BRANCH = 'main'
        TEST_DEPLOYMENT_MACHINE = credentials("TEST_DEPLOYMENT_MACHINE")
        TEST_DEPLOYMENT_PATH = '/var/www/html/stockone-pro/'
        SIMULATION_INPUT = 'No'
        TEST_INPUT = 'No'
        NXT_MASTER_INPUT = 'No'
        NXT_CHILD_INPUT = 'No'
        RELEASE_SLACK = credentials("RELEASE_SLACK")
        MASTER_FILE = 'docker-compose-deploy-master.yml'
        CHILD_FILE = 'docker-compose-deploy-child.yml'
        NORMAL_FILE = 'docker-compose-deploy.yml'
    }
    stages {

        stage('Simulation Input') {
            steps{
                script {
                    TEST_INPUT = input(
                        message: "Simulate Current Version ${BRANCH} Deployment",
                        submitterParameter: 'submitter',
                        parameters: [
                                [$class: 'ChoiceParameterDefinition',
                                choices: ['No','Yes'].join('\n'),
                                name: 'input',
                                description: 'Menu - select box option']
                        ])
                }
            }
        }

        stage('Simulate Deployment') {
            agent {
                label 'slave2'
            }
            steps{
                script {

                    sh "echo  Current Version ${BRANCH}"
                    echo "${TEST_INPUT.submitter} Approved Simulation Deployment"
                    echo "The answer is: ${TEST_INPUT}"
                    SIMULATION_INPUT = "${TEST_INPUT.input}"

                    if( "${TEST_INPUT.input}" == "Yes"){
                        sh "echo  Deploying ${BRANCH} Branch"
                        sh "chmod +x deploy_docker.sh"
                        sh "sudo sh deploy_docker.sh ${TEST_DEPLOYMENT_MACHINE} ${DEPLOYMENT_USER}  ${GIT_HUB_TOKEN} ${TEST_DEPLOYMENT_PATH} ${PROD_DEPLOY_BRANCH} ${BRANCH} ${NORMAL_FILE}" 
                        sh "echo deployed to TEST"
                    } else {
                        sh "echo Not deployed to Nxt"
                    }
                }
            }
        }

        stage('Production Deploymnets') {
            when {
                expression { simulationCheck("${SIMULATION_INPUT}") }
                beforeAgent true
            }
            parallel {
                stage('NXT') {
                    agent none
                    stages {
                        stage('NXT MASTER Input') {
                            steps{
                                script {
                                    NXT_MASTER_INPUT = input(
                                        message: "NXT MASTER Version ${BRANCH} Deployment",
                                        submitterParameter: 'submitter',
                                        parameters: [
                                                [$class: 'ChoiceParameterDefinition',
                                                choices: ['No','Yes'].join('\n'),
                                                name: 'input',
                                                description: 'Menu - select box option']
                                        ])
                                }
                            }
                        }
                        stage('NXT MASTER DEPLOYMENT') {
                            agent {
                                label 'slave2'
                            }
                            steps {
                                script {

                                    echo "The answer is: ${NXT_MASTER_INPUT}"
                                    echo "${NXT_MASTER_INPUT.submitter} Approved NXT MASTER Deployment"
                                    sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"${NXT_MASTER_INPUT.submitter} Approved NXT MASTER ${BRANCH} DEPLOYMENT!\"}' ${RELEASE_SLACK}"

                                    if( "${NXT_MASTER_INPUT.input}" == "Yes"){
                                        sh "echo  Deploying ${BRANCH} Branch"
                                        lbTarget(NXT_MASTER_DEPLOYMENT_MACHINE, "remove", "MASTER", NXT_LB_ID, LB_PROJECT_KEY)
                                        sh "chmod +x deploy_docker.sh" 
                                        sh "sudo sh deploy_docker.sh ${NXT_MASTER_DEPLOYMENT_MACHINE} ${DEPLOYMENT_USER}  ${GIT_HUB_TOKEN} ${NXT_DEPLOYMENT_PATH} ${PROD_DEPLOY_BRANCH} ${BRANCH} ${MASTER_FILE}"
                                        sh "echo deployed to Nxt"
                                    } else {
                                        sh "echo Not deployed to Nxt"
                                    }
                                }
                            }
                        }
                        stage('NXT CHILD Input') {
                            steps{
                                script {
                                    NXT_CHILD_INPUT = input(
                                        message: "NXT Version ${BRANCH} CHILD Deployment",
                                        submitterParameter: 'submitter',
                                        parameters: [
                                                [$class: 'ChoiceParameterDefinition',
                                                choices: ['No','Yes'].join('\n'),
                                                name: 'input',
                                                description: 'Menu - select box option']
                                        ])
                                }
                            }
                        }
                        stage('NXT CHILD DEPLOYMENT') {
                            agent {
                                label 'slave2'
                            }
                            steps {
                                script {

                                    echo "The answer is: ${NXT_CHILD_INPUT}"
                                    echo "${NXT_CHILD_INPUT.submitter} Approved NXT CHILD Deployment"
                                    sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"${NXT_CHILD_INPUT.submitter} Approved NXT CHILD ${BRANCH} DEPLOYMENT!\"}' ${RELEASE_SLACK}"

                                    if( "${NXT_CHILD_INPUT.input}" == "Yes"){
                                        // attaching back master after child approval
                                        lbTarget(NXT_MASTER_DEPLOYMENT_MACHINE, "add", "MASTER", NXT_LB_ID, LB_PROJECT_KEY)
                                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"RapidCICD released ${BRANCH} for NXT MASTER\"}' ${RELEASE_SLACK}"

                                        // deploying child after attaching master back to load balancer
                                        sh "echo  Deploying ${BRANCH} Branch"
                                        lbTarget(NXT_CHILD_DEPLOYMENT_MACHINE, "remove", "CHILD", NXT_LB_ID, LB_PROJECT_KEY)
                                        sh "chmod +x deploy_docker.sh"
                                        sh "sudo sh deploy_docker.sh ${NXT_CHILD_DEPLOYMENT_MACHINE} ${DEPLOYMENT_USER}  ${GIT_HUB_TOKEN} ${NXT_DEPLOYMENT_PATH} ${PROD_DEPLOY_BRANCH} ${BRANCH} ${CHILD_FILE}"
                                        sh "echo deployed to Nxt"
                                        lbTarget(NXT_CHILD_DEPLOYMENT_MACHINE, "add", "CHILD", NXT_LB_ID, LB_PROJECT_KEY)
                                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"RapidCICD released ${BRANCH} for NXT CHILD\"}' ${RELEASE_SLACK}"
                                    } else {
                                        sh "echo Not deployed to Nxt"
                                    }
                                }
                            }
                        }
                        stage('CELERY DEPLOYMENT') {
                            agent {
                                label 'slave2'
                            }
                            steps {
                                script {

                                    if( "${NXT_CHILD_INPUT.input}" == "Yes"){
                                        sh "echo  Deploying ${BRANCH} Branch"
                                        sh "chmod +x deploy_celery.sh"
                                        sh "sudo sh deploy_celery.sh ${NXT_CELERY_DEPLOMENT_MACHINE} ${DEPLOYMENT_USER} ${GIT_HUB_TOKEN} ${NXT_DEPLOYMENT_PATH} ${PROD_DEPLOY_BRANCH} ${BRANCH}"
                                        sh "echo deployed celery"
                                        sh "curl -X POST -H 'Content-type: application/json' --data '{\"text\":\"RapidCICD released ${BRANCH} for NXT CELERY\"}' ${RELEASE_SLACK}"
                                    } else {
                                        sh "echo Not deployed to NXT CELERY"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        stage ('Finish Deploymnet') {
            agent {
                label 'slave2'
            }
            steps{
                script {
                    sh "echo hello from stage3"
                }
            }
        }

    }
}