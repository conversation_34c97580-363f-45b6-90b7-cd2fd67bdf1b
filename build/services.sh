
# 0  default param
# 1  Authour Name
# 2  Version Name
# 3  Platform Name

cp $defPath/config/init-celeryd /etc/init.d/celeryd
sudo chmod 755 /etc/init.d/celeryd
sudo chown root:root /etc/init.d/celeryd
echo "Created Celery Init"

cp $defPath/config/default-celeryd /etc/default/celeryd
sudo /etc/init.d/celeryd status

sudo /etc/init.d/celeryd restart

echo "Celery Started"

cp $defPath/config/init-celerybeat /etc/init.d/celerybeat
sudo chmod 755 /etc/init.d/celerybeat
sudo chown root:root /etc/init.d/celerybeat
echo "Created Celery Beat Init"

cp $defPath/config/default-celeryd /etc/default/celerybeat
sudo /etc/init.d/celerybeat status

sudo /etc/init.d/celerybeat restart

echo "Celery Beat Started"

# * * * * * /usr/local/bin/lockrun --lockfile /tmp/uwsgi_new_wms.lock -- uwsgi --close-on-exec -s /tmp/uwsgi_new_wms.sock --chdir /var/www/html/WMS_ANGULAR/stockone-wms/wms --pp .. -w wms.wsgi -C666 -p 4 -H /var/www/html/WMS_ANGULAR/stockone-wms/wms/setup/wms 1>> /tmp/new_wms_log 2>> /tmp/new_wms_err --buffer-size 32768
cp $defPath/config/stockone-gunicorn.service /etc/systemd/system/stockone-gunicorn.service

systemctl daemon-reload

service stockone-gunicorn restart

echo "Gunicorn Started"

socketFile=$defPath/stockone-wms/wmsstockonegunicorn.sock
a=0
while [ $a -lt 10 ]
do
    if [ -S "$socketFile" ];
    then
        chmod 666  $socketFile
        break
    else
        echo "Socket File Not Created Sleeping"
        sleep 5
    fi
done

sh $defPath/build/slack.sh $1 $2 $3
