#!/bin/bash
apt-get install python3.8 --assume-yes
apt-get install virtualenv --assume-yes
pip install setuptools --assume-yes
pip install uwsgi --assume-yes
sudo apt-get install mysql-client --assume-yes
sudo apt-get install build-essential libssl-dev libffi-dev python-dev --assume-yes
sudo apt-get install libmysqlclient-dev python-MySQLdb python-setuptools --assume-yes
sudo apt-get install libmysqlclient-dev --assume-yes
sudo apt-get install python-dev --assume-yes 
sudo apt-get install build-essential --assume-yes 
sudo apt-get install libssl-dev --assume-yes 
sudo apt-get install libffi-dev --assume-yes 
sudo apt-get install libxml2-dev --assume-yes 
sudo apt-get install libxslt1-dev --assume-yes 
sudo apt-get install zlib1g-dev --assume-yes 
sudo apt-get install python-pip --assume-yes