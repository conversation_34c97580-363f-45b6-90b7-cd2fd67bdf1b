#!/bin/bash
curl -sL https://deb.nodesource.com/setup_6.x | sudo -E bash
sudo bash nodesource_setup.sh
sudo apt-get install npm --assume-yes
sudo apt-get install nodejs --assume-yes
nodejs -v
npm -v
cd $defPath/ANGULAR_WMS/
npm install --global bower grunt-cli
npm install
npm audit fix
npm audit fix --force
bower install --allow-root
mv $defPath/ANGULAR_WMS/bower_components/ $defPath/ANGULAR_WMS/app/vendor
npm install sweetalert@1.1.0
npm audit fix
rm -rf $defPath/ANGULAR_WMS/app/vendor/sweetalert/
mv $defPath/ANGULAR_WMS/node_modules/sweetalert/  $defPath/ANGULAR_WMS/app/vendor/
cp -r $defPath/ANGULAR_WMS/app/prod.html $defPath/ANGULAR_WMS/app/index.html

