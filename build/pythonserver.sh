#!/bin/bash
cd $defPath/stockone-wms/wms/setup
sh $defPath/stockone-wms/wms/setup/setup.sh
source $defPath/stockone-wms/wms/setup/wms/bin/activate
mkdir $defPath/stockone-wms/wms/logs
pip install -r $defPath/stockone-wms/wms/setup/requirements.pip
cd $defPath/stockone-wms/wms/

python3 manage.py collectstatic --noinput
# python manage.py makemigrations lms --noinput
python3 manage.py migrate --noinput