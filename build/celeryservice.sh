cp $defPath/config/init-celeryd /etc/init.d/celeryd
sudo chmod 755 /etc/init.d/celeryd
sudo chown root:root /etc/init.d/celeryd
echo "Created Celery Init"

cp $defPath/config/default-celeryd /etc/default/celeryd
sudo /etc/init.d/celeryd status

sudo /etc/init.d/celeryd restart

echo "Celery Started"

cp $defPath/config/init-celerybeat /etc/init.d/celerybeat
sudo chmod 755 /etc/init.d/celerybeat
sudo chown root:root /etc/init.d/celerybeat
echo "Created Celery Beat Init"

cp $defPath/config/default-celeryd /etc/default/celerybeat
sudo /etc/init.d/celerybeat status

sudo /etc/init.d/celerybeat restart

echo "Celery Beat Started"