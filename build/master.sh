#!/bin/bash
echo "Installing Nginx"
sh build/addNginx.sh
cd $defPath

echo "Installing Redis"
. build/addRedis.sh
cd $defPath

echo "Installing User Interface"
. build/user_interface.sh
cd $defPath
echo "User Interface Installed"

echo "Installing Prerequisites"
. build/prerequisites.sh
cd $defPath
echo "Prerequisites Installed"

echo "Initializing Virtual Environment and Gathering Dependencies"
. build/pythonserver.sh
cd $defPath
echo "Adding Services"
. build/services.sh
