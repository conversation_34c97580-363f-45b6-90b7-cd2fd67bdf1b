apt-get update

apt-get install nginx --assume-yes

echo "Nginx Installed"
sudo /etc/init.d/nginx start
sudo /etc/init.d/nginx stop

echo "Nginx Started"

echo "Adding Configurations"
rm /etc/nginx/sites-enabled/stockone
rm /etc/nginx/sites-enabled/default
cp -rf $defPath/config/stockone /etc/nginx/sites-enabled/stockone

rm /etc/nginx/sites-available/stockone
rm /etc/nginx/sites-available/default
cp -rf $defPath/config/stockone /etc/nginx/sites-available/stockone
echo "Configurations Added Successfully"

service nginx restart