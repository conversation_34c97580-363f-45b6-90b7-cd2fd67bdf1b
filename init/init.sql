CREATE DATABASE IF NOT EXISTS stockone;
CREATE DATABASE IF NOT EXISTS stockone_reversion;
CREATE DATABASE IF NOT EXISTS test_stockone;
CREATE DATABASE IF NOT EXISTS test_stockone_reversion;
-- CREATE USER stockone@'%' identified by 'stockone^123';
GRANT ALL PRIVILEGES ON test_stockone.* TO stockone@'%';
GRANT ALL PRIVILEGES ON test_stockone_reversion.* TO stockone@'%';

CREATE DATABASE IF NOT EXISTS stockone_reversion;
GRANT ALL PRIVILEGES ON stockone_reversion.* TO stockone@'%';